<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bbac</groupId>
		<artifactId>hrsf</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>hrsf-auth</artifactId>
	<packaging>jar</packaging>

	<description>hrsf 认证授权中心，基于 spring security oAuth2</description>

	<dependencies>
		<!--注册中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
		</dependency>
		<!--配置中心客户端-->
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
		</dependency>
		<!--断路器依赖-->
		<dependency>
			<groupId>com.bbac</groupId>
			<artifactId>hrsf-common-feign</artifactId>
		</dependency>
		<!--upms api、model 模块-->
		<dependency>
			<groupId>com.bbac</groupId>
			<artifactId>hrsf-upms-api</artifactId>
		</dependency>
		<!--security-->
		<dependency>
			<groupId>com.bbac</groupId>
			<artifactId>hrsf-common-security</artifactId>
		</dependency>
		<!--JDBC相关-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>
		<!--freemarker-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-freemarker</artifactId>
		</dependency>
		<!--数据库-->
		<!--<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>-->
		<!-- https://mvnrepository.com/artifact/com.github.noraui/ojdbc8 -->
		<dependency>
			<groupId>com.github.noraui</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>12.2.0.1</version>
		</dependency>
		<!--undertow容器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-undertow</artifactId>
		</dependency>
		<!-- log -->
		<dependency>
			<groupId>com.bbac</groupId>
			<artifactId>hrsf-common-log</artifactId>
		</dependency>
	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>io.fabric8</groupId>
				<artifactId>docker-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
