/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.auth.config;

import com.bbac.hrsf.common.security.component.HrsfDaoAuthenticationProvider;
import com.bbac.hrsf.common.security.grant.CustomAppAuthenticationProvider;
import com.bbac.hrsf.common.security.grant.SSOAuthenticationProvider;
import com.bbac.hrsf.common.security.handler.FormAuthenticationFailureHandler;
import com.bbac.hrsf.common.security.handler.SsoLogoutSuccessHandler;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.*;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.factory.PasswordEncoderFactories;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

/**
 * <AUTHOR>
 * @date 2022/1/12 认证相关配置
 */
@Primary
@Order(90)
@Configuration
public class WebSecurityConfiguration extends WebSecurityConfigurerAdapter {

    @Value("${iam.inner.loginUrl}")
    private String innerLoginUrl;


    @Override
    @SneakyThrows
    protected void configure(HttpSecurity http) {
        http.formLogin()
                /*
                .loginPage(innerLoginUrl)
						测试对接单点登录先注释掉这部门逻辑
				.loginProcessingUrl("/token/form")*/
				.failureHandler(authenticationFailureHandler()).and().logout()
				.logoutSuccessHandler(logoutSuccessHandler()).deleteCookies("JSESSIONID").invalidateHttpSession(true)
                .and().authorizeRequests().antMatchers("/login", "/token/**", "/actuator/**", "/mobile/**").permitAll()
                .anyRequest().authenticated().and().csrf().disable();
        http.formLogin().disable();
    }

    /**
     * 自定义 provider 列表注入
     *
     * @param auth AuthenticationManagerBuilder
     */
    @Override
    protected void configure(AuthenticationManagerBuilder auth) {
        HrsfDaoAuthenticationProvider daoAuthenticationProvider = new HrsfDaoAuthenticationProvider();
        daoAuthenticationProvider.setPasswordEncoder(passwordEncoder());

        // 处理默认的密码模式认证
        auth.authenticationProvider(daoAuthenticationProvider);
        // 自定义的认证模式
        auth.authenticationProvider(new CustomAppAuthenticationProvider());
        // 新增SSO认证模式
        auth.authenticationProvider(new SSOAuthenticationProvider());
    }

    @Bean
    @Override
    @SneakyThrows
    public AuthenticationManager authenticationManagerBean() {
        return super.authenticationManagerBean();
    }

    /**
     * 认证中心静态资源处理
     *
     * @param web WebSecurity
     */
    @Override
    public void configure(WebSecurity web) {
        web.ignoring().antMatchers("/css/**");
    }

    /**
     * sso 表单登录失败处理
     *
     * @return FormAuthenticationFailureHandler
     */
    @Bean
    public AuthenticationFailureHandler authenticationFailureHandler() {
        return new FormAuthenticationFailureHandler();
    }

    /**
     * SSO 退出逻辑处理
     *
     * @return LogoutSuccessHandler
     */
    @Bean
    public LogoutSuccessHandler logoutSuccessHandler() {
        return new SsoLogoutSuccessHandler();
    }

    /**
     * 密码处理器
     *
     * @return 动态密码处理器 {类型}密文
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return PasswordEncoderFactories.createDelegatingPasswordEncoder();
    }

}
