<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.bbac</groupId>
		<artifactId>hrsf-common</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>

	<artifactId>hrsf-common-log</artifactId>
	<packaging>jar</packaging>

	<description>hrsf 日志服务</description>


	<dependencies>
		<!--工具类核心包-->
		<dependency>
			<groupId>com.bbac</groupId>
			<artifactId>hrsf-common-core</artifactId>
		</dependency>
		<!--UPMS接口模块-->
		<dependency>
			<groupId>com.bbac</groupId>
			<artifactId>hrsf-upms-api</artifactId>
		</dependency>
		<!--安全依赖获取上下文信息-->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security.oauth.boot</groupId>
			<artifactId>spring-security-oauth2-autoconfigure</artifactId>
		</dependency>
	</dependencies>
</project>
