package com.bbac.hrsf.common.security.grant;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.bbac.hrsf.common.core.sso.Token;
import com.bbac.hrsf.common.core.sso.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AccountStatusException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.OAuth2Request;
import org.springframework.security.oauth2.provider.OAuth2RequestFactory;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 资源所有者电话令牌授予者
 *
 * <AUTHOR>
 * @since 2021-09-14
 */
@Slf4j
@Component
public class SSOTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = "sso";

    private static final String TOKEN_URL = "{}/oauth2/token?appcode={}&secret={}&code={}";
    private static final String USER_INFO_URL = "{}/oauth2/userinfo?appcode={}&secret={}&token={}";

    @Value("${iam.inner.callback}")
    private String innerCallback;
    @Value("${iam.inner.authorization}")
    private String authorization;

    private final AuthenticationManager authenticationManager;

    public SSOTokenGranter(AuthenticationManager authenticationManager,
                           AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService,
                           OAuth2RequestFactory requestFactory) {
        this(authenticationManager, tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
    }

    protected SSOTokenGranter(AuthenticationManager authenticationManager,
                              AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService,
                              OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.authenticationManager = authenticationManager;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<>(tokenRequest.getRequestParameters());

        //获取回调的code参数
        String baseUrl = parameters.get("baseUrl");
        String code = parameters.get("code");
        String appcode = parameters.get("appcode");
        String secret = parameters.get("secret");
        String tokenUrl = StrUtil.format(TOKEN_URL, baseUrl, appcode, secret, code);
        log.info("bbac iam callback tokenUrl is {}", tokenUrl);
        String returnToken = HttpUtil.get(StrUtil.format(TOKEN_URL, baseUrl, appcode, secret, code));
        log.info("单点系统获取返回的token:{}", returnToken);
        if (StrUtil.isBlank(returnToken)) {
            throw new BadCredentialsException("获取SSO Token失败");
        }
        Token token = JSONUtil.toBean(returnToken, Token.class);
        String userInfoUrl = StrUtil.format(USER_INFO_URL, baseUrl, appcode, secret, token.getAccessToken());
        log.info("bbac iam callback userInfoUrl is {}", userInfoUrl);
        String returnUserInfo = HttpUtil.get(userInfoUrl);
        log.info("单点系统获取返回的用户信息:{}", userInfoUrl);
        if (StrUtil.isBlank(returnUserInfo)) {
            throw new BadCredentialsException("获取SSO Token失败");
        }
        UserInfo userInfo = JSONUtil.toBean(returnUserInfo, UserInfo.class);
        log.info("userinfo:{}", JSONUtil.toJsonStr(userInfo));
        String accountName = userInfo.getAccountName();

        // Protect from downstream leaks of code
        parameters.remove("baseUrl");
        parameters.remove("code");
        parameters.remove("appcode");
        parameters.remove("secret");

        Authentication userAuth = new SSOAuthenticationToken(accountName, tokenRequest.getGrantType());
        ((AbstractAuthenticationToken) userAuth).setDetails(parameters);
        try {
            userAuth = authenticationManager.authenticate(userAuth);
        } catch (AccountStatusException | BadCredentialsException ase) {
            // covers expired, locked, disabled cases (mentioned in section 5.2, draft 31)
            throw new InvalidGrantException(ase.getMessage());
        }
        // If the phone/code are wrong the spec says we should send 400/invalid grant

        if (userAuth == null || !userAuth.isAuthenticated()) {
            throw new InvalidGrantException("Could not authenticate user: ");
        }

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, userAuth);
    }

}
