/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.security.component;

import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/2/1 接口权限判断工具
 */
public class PermissionRoleService {

    /**
     *
     */
    public boolean hasPermission() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        Optional<GrantedAuthority> optional = SecurityUtils.getUser().getAuthorities().stream().filter(
                data -> String.valueOf(RoleTypeEnum.HR.getType())
                        .equals(data.getAuthority().substring(data.getAuthority().lastIndexOf("_") + 1))
        ).findFirst();
        if (optional.isPresent()) {
            return true;
        }
        return false;
    }

    /**
     * 人才卡权限处理
     */
    public boolean hasPermissionTalentCard() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            return false;
        }
        Optional<GrantedAuthority> optional = SecurityUtils.getUser().getAuthorities().stream().filter(
                data -> String.valueOf(RoleTypeEnum.HR.getType()).equals(data.getAuthority().substring(data.getAuthority().lastIndexOf("_") + 1)) ||
                        String.valueOf(RoleTypeEnum.TALENT_CARD_HR.getType()).equals(data.getAuthority().substring(data.getAuthority().lastIndexOf("_") + 1)))
                .findFirst();
        if (optional.isPresent()) {
            return true;
        }
        return false;
    }

}
