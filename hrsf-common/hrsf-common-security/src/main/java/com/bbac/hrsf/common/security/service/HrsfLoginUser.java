/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.security.service;

import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2019/2/1 扩展用户信息
 */
public class HrsfLoginUser extends User {

    /**
     * 用户ID
     */
    @Getter
    private final String id;

    /**
     * 员工姓名
     */
    @Getter
    private final String fullName;

    /**
     * 部门ID
     */
    @Getter
    private final Long deptId;

    /**
     * 手机号
     */
    @Getter
    private final String phone;

    /**
     * 头像
     */
    @Getter
    private final String photo;

    public HrsfLoginUser(String id, Long deptId, String username, String password, String phone, String fullName,String photo, boolean enabled,
                         boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked,
                         Collection<? extends GrantedAuthority> authorities) {
        super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
        this.id = id;
        this.deptId = deptId;
        this.phone = phone;
        this.fullName = fullName;
        this.photo = photo;
    }

}
