package com.bbac.hrsf.common.security.grant;

import lombok.Getter;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetails;

/**
 * <AUTHOR>
 * @since 2021-09-14
 */
public class SSOAuthenticationToken extends AbstractAuthenticationToken {


    private static final long serialVersionUID = -920680933973451374L;

    private final Object principal;

    /**
     * 用户名
     */
    private String username;

    /**
     * 授权类型
     */
    @Getter
    private String grantType;


    public SSOAuthenticationToken(String username,String grantType) {
        super(AuthorityUtils.NO_AUTHORITIES);
        this.principal = username;
        this.username = username;
        this.grantType = grantType;
    }

    public SSOAuthenticationToken(UserDetails sysUser) {
        super(sysUser.getAuthorities());
        this.principal = sysUser;
        // 设置认证成功 必须
        super.setAuthenticated(true);
    }



    @Override
    public Object getPrincipal() {
        return this.principal;
    }

    @Override
    public String getCredentials() {
        return this.username;
    }
}
