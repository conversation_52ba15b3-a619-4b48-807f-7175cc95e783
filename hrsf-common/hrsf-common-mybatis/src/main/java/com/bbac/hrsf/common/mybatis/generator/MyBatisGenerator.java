package com.bbac.hrsf.common.mybatis.generator;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;

import java.util.Collections;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-micro-rfq-service</li>
 * <li>CreateTime : 2021/07/05 16:16:57</li>
 * <li>Description :
 * <p>
 *     Mapper 自动生成类
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class MyBatisGenerator {

    public static String JDBC_URL =
            "*****************************************";
    public static String FPWD = "123456";
    public static String USERNAME = "T_HRSF_PMS";
    public static String[] TABLES = new String[]{
            "HRSF_PMS_USER_RELATION_INFO"
    };

    public static void main(String[] args) {
        FastAutoGenerator.create(JDBC_URL, USERNAME, FPWD)
                .globalConfig(builder -> {
                    builder.author("qinxiaokun") // 设置作者
                            .enableSwagger() // 开启 swagger 模式
                            .fileOverride() // 覆盖已生成文件
                            .dateType(DateType.ONLY_DATE)
                            .outputDir(System.getProperty("user.dir") + "/generate"); // 指定输出目录
                })
                .packageConfig(builder -> {
                    builder.parent("com.bbac.hrsf.common.mybatis") // 设置父包名
                            .moduleName("moudleName") // 设置父包模块名
                            .pathInfo(Collections.singletonMap(OutputFile.mapperXml, System.getProperty("user.dir") +
                                    "/src/main/resources/mapper")); // 设置mapperXml生成路径
                })
                .strategyConfig(builder -> {
                    builder.addInclude(TABLES) // 设置需要生成的表名
                            .addTablePrefix("t_") // 设置过滤表前缀
                            .entityBuilder()
                            .naming(NamingStrategy.underline_to_camel)
                            .columnNaming(NamingStrategy.underline_to_camel)
                            .enableTableFieldAnnotation()
                            .enableLombok()
                            .superClass("com.bbac.hrsf.common.mybatis.base.BaseEntity")
                            .logicDeleteColumnName("del_flag")
                            .idType(IdType.ASSIGN_ID)
                            .enableChainModel()
                            .controllerBuilder()
                            .enableRestStyle()
                            .enableHyphenStyle();
                })
                .templateConfig(builder -> {
                    builder.mapperXml("/templates/mapper.xml");
                })
                .execute();

    }
}
