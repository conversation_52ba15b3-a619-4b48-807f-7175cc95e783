<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.bbac</groupId>
	<artifactId>hrsf-common-bom</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>

	<name>hrsf-common-bom</name>
	<url>hrsf cloud parent</url>
	<description>hrsf cloud parent</description>

	<properties>
		<hrsf.common.version>${project.version}</hrsf.common.version>
		<spring-boot.version>2.6.3</spring-boot.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<security.oauth.version>2.1.8.RELEASE</security.oauth.version>
		<log4j2.version>2.17.1</log4j2.version>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<git.commit.plugin>4.9.9</git.commit.plugin>
		<spring.checkstyle.plugin>0.0.29</spring.checkstyle.plugin>
		<fastjson.version>1.2.78</fastjson.version>
		<swagger.core.version>1.5.24</swagger.core.version>
		<mybatis-plus.version>3.5.1</mybatis-plus.version>
		<nacos.version>2.0.3</nacos.version>
		<excel.version>1.1.1</excel.version>
		<oss.version>1.0.3</oss.version>
		<sms.version>2.0.2</sms.version>
		<jaxb.version>2.3.5</jaxb.version>
	</properties>

	<!-- 定义全局jar版本,模块使用需要再次引入但不用写版本号-->
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-core</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-datasource</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-log</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-mybatis</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-security</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-feign</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-swagger</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-test</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-upms-api</artifactId>
				<version>${hrsf.common.version}</version>
			</dependency>
			<!--稳定版本，替代spring security bom内置-->
			<dependency>
				<groupId>org.springframework.security.oauth.boot</groupId>
				<artifactId>spring-security-oauth2-autoconfigure</artifactId>
				<version>${security.oauth.version}</version>
			</dependency>
			<!--swagger 最新依赖内置版本-->
			<dependency>
				<groupId>io.swagger</groupId>
				<artifactId>swagger-models</artifactId>
				<version>${swagger.core.version}</version>
			</dependency>
			<dependency>
				<groupId>io.swagger</groupId>
				<artifactId>swagger-annotations</artifactId>
				<version>${swagger.core.version}</version>
			</dependency>
			<!--fastjson 版本-->
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.nacos</groupId>
				<artifactId>nacos-client</artifactId>
				<version>${nacos.version}</version>
			</dependency>
			<!-- excel 导入导出 -->
			<dependency>
				<groupId>com.pig4cloud.excel</groupId>
				<artifactId>excel-spring-boot-starter</artifactId>
				<version>${excel.version}</version>
			</dependency>
			<!--  阿里云短信下发 -->
			<dependency>
				<groupId>io.springboot.sms</groupId>
				<artifactId>aliyun-sms-spring-boot-starter</artifactId>
				<version>${sms.version}</version>
			</dependency>
			<!--mybatis-plus-->
			<dependency>
				<groupId>com.pig4cloud.plugin</groupId>
				<artifactId>oss-spring-boot-starter</artifactId>
				<version>${oss.version}</version>
			</dependency>
			<!--orm 相关-->
			<dependency>
				<groupId>com.baomidou</groupId>
				<artifactId>mybatis-plus-boot-starter</artifactId>
				<version>${mybatis-plus.version}</version>
			</dependency>
			<!--web 模块-->
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-starter-web</artifactId>
				<version>${spring-boot.version}</version>
				<exclusions>
					<!--排除tomcat依赖-->
					<exclusion>
						<artifactId>spring-boot-starter-tomcat</artifactId>
						<groupId>org.springframework.boot</groupId>
					</exclusion>
				</exclusions>
			</dependency>
			<!--  指定 log4j 版本-->
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-to-slf4j</artifactId>
				<version>${log4j2.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.logging.log4j</groupId>
				<artifactId>log4j-bom</artifactId>
				<version>${log4j2.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<plugins>
			<!--打包jar 与git commit 关联插件-->
			<plugin>
				<groupId>io.github.git-commit-id</groupId>
				<artifactId>git-commit-id-maven-plugin</artifactId>
				<version>${git.commit.plugin}</version>
			</plugin>
			<!--代码格式插件，默认使用spring 规则-->
			<plugin>
				<groupId>io.spring.javaformat</groupId>
				<artifactId>spring-javaformat-maven-plugin</artifactId>
				<version>${spring.checkstyle.plugin}</version>
			</plugin>
		</plugins>
	</build>
</project>
