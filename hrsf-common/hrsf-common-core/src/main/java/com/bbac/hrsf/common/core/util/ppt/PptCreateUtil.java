package com.bbac.hrsf.common.core.util.ppt;

import com.bbac.hrsf.common.core.constant.DataFileConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xslf.usermodel.XMLSlideShow;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;


/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.common.core.util.ppt.PPTCreateUtil</li>
 * <li>CreateTime : 2022/04/21 13:13:18</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class PptCreateUtil {

    /**
     * @return void
     * <AUTHOR>
     * @Description
     * @Date 14:22 2020/11/5
     * @Param [ppt, outputStream]
     **/
    public static void pptWirteOut(XMLSlideShow ppt, ServletOutputStream outputStream) {
        try {
            ppt.write(outputStream);
            System.out.println("download PPT successfully");
            outputStream.close();
            ppt.close();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("An exception occurred in PPT download...");
        }
    }


    public static HttpServletResponse getServletResponse(HttpServletResponse response, String fileType, String name) {
        try {
            response.setCharacterEncoding("UTF-8");
            if (DataFileConstant.PPT.equals(fileType)) {
                //设置输出文件类型为pptx文件
                response.setContentType(DataFileConstant.CONTENT_TYPE_OF_PPT);
            } else if (DataFileConstant.PDF.equals(fileType)) {
                //设置输出文件类型为pptx文件
                response.setContentType(DataFileConstant.CONTENT_TYPE_OF_PDF);
            }else if (DataFileConstant.PPTX.equals(fileType)) {
                //设置输出文件类型为pptx文件
                response.setContentType(DataFileConstant.CONTENT_TYPE_OF_PPTX);
            }

            //通知浏览器下载文件而不是打开
            response.setHeader("Content-Disposition", "attachment;fileName=" + java.net.URLEncoder.encode(name, DataFileConstant.CHARSET_OF_UTF8));
            response.setHeader("Pragma", java.net.URLEncoder.encode(name, DataFileConstant.CHARSET_OF_UTF8));
        } catch (UnsupportedEncodingException e) {
            log.error("happened exception when set httpServletResponse！！！");
            e.printStackTrace();
        }
        return response;
    }

}
