/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
public interface CommonConstants {

    /**
     * 删除
     */
    String STATUS_DEL = "1";

    /**
     * 正常
     */
    String STATUS_NORMAL = "0";

    /**
     * 锁定
     */
    String STATUS_LOCK = "9";

    /**
     * 菜单树根节点
     */
    Long MENU_TREE_ROOT_ID = -1L;

    /**
     * 菜单
     */
    String MENU = "0";

    /**
     * 编码
     */
    String UTF8 = "UTF-8";

    /**
     * JSON 资源
     */
    String CONTENT_TYPE = "application/json; charset=utf-8";

    /**
     * 前端工程名
     */
    String FRONT_END_PROJECT = "hrsf-ui";

    /**
     * 后端工程名
     */
    String BACK_END_PROJECT = "hrsf";

    /**
     * 成功标记
     */
    Integer SUCCESS = 0;

    /**
     * 失败标记
     */
    Integer  FAIL = 1;

    /**
     * token_expire
     */
    int TOKEN_EXPIRE = 2001;

    /**
     * 蓝领分数导入校验失败
     */
    int EXPORT_VERIFY_ERROR = 2002;

    /**
     * 无权限
     */
    int NO_PERMISSION = 2003;

    /**
     * 验证码前缀
     */
    String DEFAULT_CODE_KEY = "DEFAULT_CODE_KEY_";

    /**
     * 当前页
     */
    String CURRENT = "current";

    /**
     * size
     */
    String SIZE = "size";

    /**
     * size
     */
    String HR = "HR";

    /**
     * size
     */
    String PMAPI = "PMAPI";

    /**
     * size
     */
    String TODO = "To-Do";

    /**
     * INVALID OPERATE MESSAGE
     */
    String INVALID_OPERATE_MESSAGE = "无效操作, Invalid Operation";
    /**
     * PROCESSING MESSAGE
     */
    String PROCESSING_MESSAGE = "任务处理中, Task Is Processing";

    /**
     * size
     */
    String SUCCESS_FLAG = "Success";

    /**
     * 是否字段
     */
    String FLAG_Y = "Y";
    /**
     * 是否字段
     */
    String FLAG_NO = "N";

    /**
     * 百分号
     */
    String PERCENT = "%";

    /**
     * N/A
     */
    String NA = "N/A";

    /**
     * N/A
     */
    String DEFAULT_NA = "NA";

    /**
     * 包含high
     */
    String HIGH = "high";

    /**
     * en_US
     */
    String EN_US = "en_US";

    /**
     * zh_CN
     */
    String ZH_CN = "zh_CN";

    /**
     * ele_2
     */
    String ELE_2 = "ele_2";

    /**
     * ele_3
     */
    String ELE_1 = "ele_1";
    /**
     * ele_0
     */
    String ELE_0 = "ele_0";

    /**
     * ele_3
     */
    String BACK_SPECIAL = "BACK_SPECIAL";

    /**
     * ele_3
     */
    String CONTENT = "【{} {}】-{}-{}";

    /**
     * ele_3
     */
    String MEETING_INBOX = "meetingInbox";

    String BBAC_TOKEN = "bbac-Token";

    /**
     * 请求过期消息
     */
    String HTTP_TIME_OUT_MESSAGE = "Read timed out";


}
