/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用工形式
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum RoleTypeEnum {

    /**
     *
     */
    HR(1001L, "HR管理员"),

    HRL4(1002L, "HRL4管理员"),
    /**
     *
     */
    HRL3(1003L, "HRL3管理员"),

    TALENT_CARD_HR(1004L, "人才卡管理员");

    /**
     * 类型
     */
    private final Long type;

    /**
     * 描述
     */
    private final String description;


    public static String getDescByType(String type) {
        for (RoleTypeEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getDescription();
            }
        }
        return "";
    }
}
