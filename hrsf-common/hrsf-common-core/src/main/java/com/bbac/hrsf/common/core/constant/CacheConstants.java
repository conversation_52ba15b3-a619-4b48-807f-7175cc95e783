/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant;

/**
 * <AUTHOR>
 * @date 2020年01月01日
 * <p>
 * 缓存的key 常量
 */
public interface CacheConstants {

	/**
	 * oauth 缓存前缀
	 */
	String PROJECT_OAUTH_ACCESS = "hrsf_oauth:access:";

	/**
	 * oauth 缓存令牌前缀
	 */
	String PROJECT_OAUTH_TOKEN = "hrsf_oauth:token:";

	/**
	 * 验证码前缀
	 */
	String DEFAULT_CODE_KEY = "DEFAULT_CODE_KEY:";

	/**
	 * 当前历史绩效分数任务ID
	 */
	String CURRENT_USER_SCORE_TASK_ID_HIS = "CURRENT_USER_SCORE_TASK_ID_HISTORY";
	/**
	 * 当前绩效复制任务ID
	 */
	String CURRENT_USER_SCORE_TASK_ID_DUP = "CURRENT_USER_SCORE_TASK_ID_DUPLICATE";

	/**
	 * 当前历史绩效分数任务ID 缓存时间(10分钟)
	 */
	long CURRENT_USER_SCORE_TASK_ID_CACHE_TIME = 10;

	/**
	 * 菜单信息缓存
	 */
	String MENU_DETAILS = "menu_details";

	/**
	 * 用户信息缓存
	 */
	String USER_DETAILS = "user_details";

	/**
	 * 字典信息缓存
	 */
	String DICT_DETAILS = "dict_details";

	/**
	 * oauth 客户端信息
	 */
	String CLIENT_DETAILS_KEY = "hrsf_oauth:client:details";

}
