/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 用工形式
 */
@Getter
@RequiredArgsConstructor
public enum EmployeeTypeEnum {

    /**
     *
     */
    A("1", "正式员工"),

    /**
     *
     */
    B("2", "第三方员工"),

    /**
     *
     */
    C("3", "顶岗实习生"),

    /**
     *
     */
    D("4", "白领实习生"),

    /**
     *
     */
    E("5", "退休返聘"),

    /**
     *
     */
    F("6", "内退返聘"),

    /**
     *
     */
    G("7", "内退"),

    /**
     *
     */
    H("8", "待岗人员"),

    /**
     *
     */
    I("9", "长病长休"),

    /**
     *
     */
    J("10", "戴姆勒集团调入"),

    /**
     *
     */
    K("11", "其他考核人员"),

    /**
     *
     */
    L("12", "其他不考核人员");


    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

}
