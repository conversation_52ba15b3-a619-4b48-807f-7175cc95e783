package com.bbac.hrsf.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的结果
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PotencyListDataEnum {

    POTENCYLIST1("potencyList1", "5_2", "潜力等级区域1"),
    POTENCYLIST2("potencyList2", "4_2", "潜力等级区域2"),
    POTENCYLIST3("potencyList3", "3_2", "潜力等级区域3"),
    POTENCYLIST4("potencyList4", "2_2", "潜力等级区域4"),
    POTENCYLIST5("potencyList5", "1_2", "潜力等级区域5"),
    POTENCYLIST6("potencyList6", "5_1", "潜力等级区域6"),
    POTENCYLIST7("potencyList7", "4_1", "潜力等级区域7"),
    POTENCYLIST8("potencyList8", "3_1", "潜力等级区域8"),
    POTENCYLIST9("potencyList9", "2_1", "潜力等级区域9"),
    POTENCYLIST10("potencyList10", "1_1", "潜力等级区域10");


    /**
     * 结果
     */
    private final String result;

    /**
     * 类别
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

}
