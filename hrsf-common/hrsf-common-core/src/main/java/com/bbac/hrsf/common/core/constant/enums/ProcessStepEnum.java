/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 表单状态
 */
@Getter
@RequiredArgsConstructor
public enum ProcessStepEnum {

    /**
     * 表单状态
     */
    START("-1", "SYSTEM", "START", "发起校准会议"),
    HRBP("0", "HRBP", "task01", "绩效协调员"),
    OWNER("1", "OWNER", "task02", "负责人"),
    HR_MANAGER("2", "HR_MANAGER", "task03", "HR管理员审批"),
    HRL4("3", "HR_L4", "task04", "HR L4审批"),
    HRL3("4", "HR_L3", "task05", "HR L3审批"),
    END("5", "END", "END", "流程结束");

    /**
     * 类型
     */
    private final String type;

    /**
     * 英文类型
     */
    private final String sign;

    /**
     * 英文类型
     */
    private final String taskId;

    /**
     * 描述
     */
    private final String description;

    public static ProcessStepEnum getByCode(String type) {
        for (ProcessStepEnum v : values()) {
            if (v.getType().equals(type)) {
                return v;
            }

        }
        return null;
    }

    /**
     * 如果没有匹配上，则默认返回的是结束节点
     *
     * @param taskDefinitionKey
     * @return
     */
    public static ProcessStepEnum getByTaskId(String taskDefinitionKey) {
        for (ProcessStepEnum v : values()) {
            if (v.getTaskId().equals(taskDefinitionKey)) {
                return v;
            }
        }
        return END;
    }

    public static String getTypeByTaskId(String processStep) {
        for (ProcessStepEnum v : values()) {
            if (v.getTaskId().equals(processStep)) {
                return v.getType();
            }
        }
        return null;
    }

    public static String getSignByType(String type) {
        for (ProcessStepEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getSign();
            }
        }
        return null;
    }

    /**
     * 这里处理逻辑需要考虑到template=3的公司级校准会议
     * -- todo
     *
     * @param taskDefinitionKey
     * @return
     */
    public static String getNextStep(String taskDefinitionKey) {
        ProcessStepEnum processStepEnum = getByTaskId(taskDefinitionKey);
        return processStepEnum == null ? END.getType() : processStepEnum.getType();

        /*if (CalibrationTemplateEnum.THREE.getType().equals(template)) {
            switch (getByTaskId(taskDefinitionKey)) {
                case HR_MANAGER:
                    return END.getType();
                default:
                    break;
            }
        } else {
            switch (getByCode(taskDefinitionKey)) {
                case HRBP:
                    return OWNER.getType();
                case OWNER:
                    return HR_MANAGER.getType();
                case HR_MANAGER:
                    return HRL4.getType();
                case HRL4:
                    return HRL3.getType();
                case HRL3:
                    return END.getType();
                default:
                    break;
            }
        }
        return END.getType();*/
    }

    /**
     * 业务上特殊处理--所有的节点都回到绩效协调员
     *
     * @param taskDefinitionKey
     * @return
     */
    public static String getPreStep(String taskDefinitionKey) {

        ProcessStepEnum processStepEnum = getByTaskId(taskDefinitionKey);
        return processStepEnum.getType();
        /*switch (getByCode(processStep)) {
            case END:
            case HRL3:
            case HRL4:
            case HR_MANAGER:
            case OWNER:
                return HRBP.getType();
            default:
                break;
        }
        return HRBP.getType();*/
    }


    /**
     * 业务上特殊处理--所有的节点都回到绩效协调员
     *
     * @param taskDefinitionKey
     * @return
     */
    public static String getPreTaskKey(String taskDefinitionKey) {
        ProcessStepEnum processStepEnum = getByTaskId(taskDefinitionKey);
        switch (processStepEnum) {
            case HRL3:
                return HRL4.getTaskId();
            case HRL4:
                return HR_MANAGER.getTaskId();
            case HR_MANAGER:
                return OWNER.getTaskId();
            case OWNER:
                return HRBP.getTaskId();
            default:
                break;
        }
        return HRBP.getTaskId();
    }


}
