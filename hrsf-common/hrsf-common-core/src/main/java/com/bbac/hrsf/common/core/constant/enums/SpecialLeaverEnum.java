/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 用工形式
 */
@Getter
@RequiredArgsConstructor
public enum SpecialLeaverEnum {

    /**
     *
     */
    A("1", "调至集团人员"),

    /**
     *
     */
    B("2", "退休"),

    /**
     *
     */
    C("3", "因公去世"),

    /**
     *
     */
    D("4", "其他考核人员"),

    /**
     *
     */
    E("5", "其他不考核人员");

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

}
