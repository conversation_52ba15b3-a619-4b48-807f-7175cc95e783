/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 用工形式
 */
@Getter
@RequiredArgsConstructor
public enum PunishTypeEnum {

    /**
     *
     */
    A("9", "作废"),

    /**
     *
     */
    B("31", "警告"),
    D("32", "严重警告"),
    E("33", "记过"),
    F("34", "降级"),
    G("35", "免职"),
    H("36", "解除劳动合同");

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

    public static String getByType(String type) {
        for (PunishTypeEnum transactType : values()) {
            if (transactType.getType().equals(type)) {
                //获取指定的枚举
                return transactType.getDescription();
            }
        }
        return null;
    }


}
