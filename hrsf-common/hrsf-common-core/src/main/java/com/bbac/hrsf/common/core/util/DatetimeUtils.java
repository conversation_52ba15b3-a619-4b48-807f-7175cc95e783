package com.bbac.hrsf.common.core.util;

import cn.hutool.core.util.ReUtil;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.common.core.util.DatetimeUtils</li>
 * <li>CreateTime : 2022/05/16 14:14:50</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class DatetimeUtils {
    private static final String REGEX = "\\((.*?)\\)";
    private static final String SYMBOL = "\\+";

    public static Long DateStrToLong(String dateStr) {
        String timeStr = ReUtil.getGroup1(REGEX, dateStr);
        return Long.valueOf(timeStr.replaceAll(SYMBOL, ""));
    }
}