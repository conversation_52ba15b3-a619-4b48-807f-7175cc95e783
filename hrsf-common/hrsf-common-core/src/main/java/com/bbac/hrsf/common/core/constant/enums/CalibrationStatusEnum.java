/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 校准会议状态
 */
@Getter
@RequiredArgsConstructor
public enum CalibrationStatusEnum {

    /**
     *
     */
    GONING((byte) 0, "进行中"),

    /**
     *
     */
    FREEZE((byte) 1, "冻结"),
    END((byte) 2, "结束");

    /**
     * 类型
     */
    private Byte type;

    /**
     * 描述
     */
    private String description;

    CalibrationStatusEnum(Byte type, String description) {
        this.type = type;
        this.description = description;
    }

    /**
     * 这里处理逻辑需要考虑到template=3的公司级校准会议
     * -- todo
     *
     * @param taskDefinitionKey
     * @param template
     * @return
     */
    public static Byte getNextStep(String taskDefinitionKey, String template) {

        /**
         * 如果公司级校准会议则特殊处理，公司级校准会议只有两个节点
         */
        if (CalibrationTemplateEnum.THREE.getType().equals(template)) {
            switch (ProcessStepEnum.getByTaskId(taskDefinitionKey)) {
                case END:
                    return END.getType();
                default:
                    break;
            }
        } else {
            /**
             * 如果流程节点是HR_L3,则说明是最后一个审批节点
             * 则返回END,其余节点则返回GOING
             */
            switch (ProcessStepEnum.getByTaskId(taskDefinitionKey)) {
                case END:
                    return END.getType();
                default:
                    break;
            }
        }
        return GONING.getType();
    }

}
