/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 用工形式
 */
@Getter
@RequiredArgsConstructor
public enum CalibrationTemplateEnum {

    /**
     *
     */
    ONE("1", "蓝白领季度+蓝领年度"),

    /**
     *
     */
    TWO("2", "白领年度"),
    THREE("3", "公司级校准"),
    ;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    CalibrationTemplateEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }
}
