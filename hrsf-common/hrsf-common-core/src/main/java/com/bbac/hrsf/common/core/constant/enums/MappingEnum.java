package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @author: liu, jie
 * @create: 2022-06-09
 **/
@Getter
@RequiredArgsConstructor
public enum MappingEnum {

    A("1", "0"),
    B("2", "32.5"),
    C("3", "65"),
    D("4", "97.5"),
    E("5", "130"),
    ;


    private final String type;

    private final String description;

    public static String getTypeByDesc(String description) {
        for (MappingEnum v : values()) {
            if (v.getDescription().equals(description)) {
                return v.getType();
            }
        }
        return A.getType();
    }
}
