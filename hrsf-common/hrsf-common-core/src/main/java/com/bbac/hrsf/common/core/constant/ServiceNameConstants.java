/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant;

/**
 * <AUTHOR>
 * @date 2018年06月22日16:41:01 服务名称
 */
public interface ServiceNameConstants {

	/**
	 * 认证服务的SERVICEID
	 */
	String AUTH_SERVICE = "hrsf-auth";

	/**
	 * UMPS模块
	 */
	String UMPS_SERVICE = "hrsf-upms-biz";

	/**
	 * PMS模块
	 */
	String PMS_SERVICE = "hrsf-performance-biz";

}
