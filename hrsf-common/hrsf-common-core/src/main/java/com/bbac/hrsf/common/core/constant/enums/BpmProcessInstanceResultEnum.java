package com.bbac.hrsf.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的结果
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmProcessInstanceResultEnum {


    LAUNCH(0, "已发起"),
    REJECT(1, "已退回"),
    APPROVE(2, "已提交"),
    REJECT_LAST_STEP(3, "退回到上一步");

    /**
     * 结果
     */
    private final Integer result;
    /**
     * 描述
     */
    private final String desc;

}
