package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @author: liu, jie
 * @create: 2022-06-08
 **/
@Getter
@RequiredArgsConstructor
public enum EmailStatusEnum {

    SEND("Y", "已发送运维"),
    ZERO("0", "0次尝试"),
    ONE("1", "1次尝试"),
    TWO("2", "2次尝试"),
    THREE("3", "3次尝试"),
    SUCCESS("SUCCESS", "SUCCESS"),
    ;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    EmailStatusEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }
}
