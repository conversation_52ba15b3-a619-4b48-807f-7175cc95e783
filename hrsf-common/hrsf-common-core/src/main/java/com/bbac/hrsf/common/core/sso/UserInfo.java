package com.bbac.hrsf.common.core.sso;

import lombok.*;

import java.util.Map;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.common.core.sso.UserInfo</li>
 * <li>CreateTime : 2022/03/22 19:19:17</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class UserInfo {

    /** 用户IP */
    private String userid;

    /** 账户名称 */
    private String accountName;

    /** 账户其他属性 */
    private Map<String, String> accountAttrs;

    /** 用户其他属性 */
    private Map<String, String> mappingAttrs;
}