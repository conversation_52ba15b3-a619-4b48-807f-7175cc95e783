/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 业绩等级
 */
@Getter
@RequiredArgsConstructor
public enum PmsLevelEnum {

    /**
     * 表单状态
     */
    Insufficient(BigDecimal.valueOf(0), "完全不达标 Insufficient"),
    Inconsistent(BigDecimal.valueOf(32.5), "不完全达标 Inconsistent"),
    Successful(BigDecimal.valueOf(65), "完全达标 Successful"),
    Excellent(BigDecimal.valueOf(97.5), "优秀 Excellent"),
    Outstanding(BigDecimal.valueOf(130), "杰出 Outstanding");
    /**
     * 类型
     */
    private final BigDecimal type;

    /**
     * 描述
     */
    private final String description;

    public static String getByCode(BigDecimal type) {
        for (PmsLevelEnum v : values()) {
            if (v.type.compareTo(type) == 0) {
                return v.name();
            }
        }
        return "NO_MATCHING";
    }

    public static String getDescByCode(BigDecimal type) {
        if (Objects.isNull(type)) {
            return "";
        }
        for (PmsLevelEnum v : values()) {
            if (v.type.compareTo(type) == 0) {
                return v.getDescription();
            }
        }
        return "NO_MATCHING";
    }

    public static BigDecimal getByDescription(String description) {
        for (PmsLevelEnum v : values()) {
            if (v.description.equals(description)) {
                return v.getType();
            }
        }
        return null;
    }

}
