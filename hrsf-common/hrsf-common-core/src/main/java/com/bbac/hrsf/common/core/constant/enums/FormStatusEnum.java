/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import com.bbac.hrsf.common.core.constant.CommonConstants;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 表单状态
 */
@Getter
@RequiredArgsConstructor
public enum FormStatusEnum {

    /**
     * 表单状态
     */
    In_Rating("0", "评分中 In Rating"),
    Rated("1", "已评分 Rated"),
    In_Calibration("2", "校准中 In Calibration"),
    In_HR_Approval("3", "HR审批中 In HR Approval"),
    Completed("4", "已完成 Completed");

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;


    public static FormStatusEnum getByCode(String type) {
        for (FormStatusEnum v : values()) {
            if (v.getType().equals(type)) {
                return v;
            }

        }
        return null;
    }

    /**
     * @param processStep
     * @param template
     * @return
     */
    public static String getFormStatusByCurrentStep(String processStep, String template) {

        /**
         * 公司级校准会议只有一个审批节点
         * 该节点审批完后
         * 则就更新状态为已完成
         */
        if (CalibrationTemplateEnum.THREE.getType().equals(template)) {
            switch (ProcessStepEnum.getByTaskId(processStep)) {
                case END:
                    return Completed.getType();
                default:
                    break;
            }
        } else {
            switch (ProcessStepEnum.getByTaskId(processStep)) {
                case HRBP:
                    return Completed.getType();
                case OWNER:
                    return In_Calibration.getType();
                case HR_MANAGER:
                case HRL4:
                case HRL3:
                    return In_HR_Approval.getType();
                case END:
                    return Completed.getType();
                default:
                    break;
            }
        }
        return null;
    }

    /**
     * @param template
     * @return
     */
    public static String getPreStep(String template) {
        if (CalibrationTemplateEnum.TWO.getType().equals(template)
                || CalibrationTemplateEnum.ONE.getType().equals(template)) {
            return CommonConstants.BACK_SPECIAL;
        } else {
            return Rated.getType();
        }
    }

}
