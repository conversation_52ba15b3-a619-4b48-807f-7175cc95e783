package com.bbac.hrsf.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的结果
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum BpmProcessParamEnum {

    TASK01("pmsUserList", "节点1:变量"),
    TASK02("deptHeadList", "节点2:变量"),
    TASK03("hrList", "节点3:变量"),
    TASK04("hrL4List", "节点4:变量"),
    TASK05("hrL3List", "节点5:变量");


    /**
     * 结果
     */
    private final String result;
    /**
     * 描述
     */
    private final String desc;

}
