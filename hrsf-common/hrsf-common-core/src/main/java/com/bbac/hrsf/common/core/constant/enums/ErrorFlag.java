package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @author: liu, jie
 * @create: 2022-06-23
 **/
@Getter
@RequiredArgsConstructor
public enum ErrorFlag {

    writeDataBack("1", "writeDataBack"),
    whiteYearWriteDataBack("2", "whiteYearWriteDataBack"),
    whiteYearFinalWriteDataBack("3", "whiteYearFinalWriteDataBack"),
    finalWriteDataBack("4", "finalWriteDataBack"),
    SYNC("5", "syncInfoJobHandler"),
    //todo
    syncPmsJobHandler("6", "syncPmsJobHandler"),
    syncFormId("7", "syncFormId"),
    ;

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

}
