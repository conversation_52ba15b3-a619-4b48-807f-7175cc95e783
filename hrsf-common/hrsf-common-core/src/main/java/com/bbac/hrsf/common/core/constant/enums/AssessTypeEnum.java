/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用工形式
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum AssessTypeEnum {

    /**
     *
     */
    YEAR("YEAR", "年度", "Yearly"),

    /**
     *
     */
    Q1("Q1", "1季度", "Q1"),
    Q2("Q2", "2季度", "Q2"),
    Q3("Q3", "3季度", "Q3"),
    Q4("Q4", "4季度", "Q4");

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

    /**
     * 描述EN
     */
    private final String descriptionEn;


    public static String getDescByType(String type) {
        for (AssessTypeEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getDescription();
            }
        }
        return "";
    }

    public static String getDescEnByType(String type) {
        for (AssessTypeEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getDescriptionEn();
            }
        }
        return "";
    }
}
