package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @author: liu, jie
 * @create: 2022-06-09
 **/
@Getter
@RequiredArgsConstructor
public enum VariableEnum {
    MOVE("1", "换岗 Move"),
    STAY("2", "留待 Stay"),
    ;


    private final String type;

    private final String description;

    public static String getDescByType(String type) {
        for (VariableEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getDescription();
            }
        }
        return "";
    }

    public static String getTypeByDesc(String desc) {
        for (VariableEnum v : values()) {
            if (v.getDescription().equals(desc)) {
                return v.getType();
            }
        }
        return "";
    }
}
