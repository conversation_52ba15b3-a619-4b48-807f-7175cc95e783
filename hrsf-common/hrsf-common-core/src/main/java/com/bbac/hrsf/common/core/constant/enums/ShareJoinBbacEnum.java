package com.bbac.hrsf.common.core.constant.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程实例的结果
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ShareJoinBbacEnum {

    A("1", "股东方加入 From Shareholders"),
    B("2", "股东方晋升加入From Shareholders(promotion)"),
    C("3", "股东方平级加入(>=6个月) From shareholders(≥6 months, same position)"),
    D("4", "股东方平级加入(<6个月) From shareholders (<6 months、same position)");


    /**
     * 结果
     */
    private final String type;
    /**
     * 描述
     */
    private final String desc;

    public static String getDescByType(String type) {
        for (ShareJoinBbacEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getDesc();
            }
        }
        return "";
    }
}
