/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019-05-16
 * <p>
 * 用工形式
 */
@Getter
@RequiredArgsConstructor
public enum PmsUpdateEventEnum {

    /**
     *
     */
    A("A", "获取员工的表单ID"),

    /**
     *
     */
    B("B", "获取员工季度分数/回写季度均分给主数据"),
    C("C", "获取员工调岗考核总分和等级"),
    D("D", "获取员工惩"),
    E("E", "启动校准会议流程"),
    F("F", "创建流程实例"),
    G("G", "HRBP数据回写"),
    H("H", "L3数据回写"),
    I("I", "绩效协调员数据回写"),
    J("J", "发送邮件"),
    K("K", "员工绩效考核修改启动流程"),
    L("L", "处理机构负责人-未在主数据中则置空"),
    M("M", "季度均分值置空"),
    ;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    PmsUpdateEventEnum(String type, String description) {
        this.type = type;
        this.description = description;
    }
}
