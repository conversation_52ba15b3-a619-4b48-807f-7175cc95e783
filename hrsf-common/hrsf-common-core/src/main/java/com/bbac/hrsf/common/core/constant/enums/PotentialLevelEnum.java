package com.bbac.hrsf.common.core.constant.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @author: liu, jie
 * @author: liu, jie
 * @create: 2022-06-09
 **/
@Getter
@RequiredArgsConstructor
public enum PotentialLevelEnum {
    /**
     * 修改问题清单260
     * 这里特殊处理一下 1,2,3对应返回值为3
     */
    A("5", "1", "配置有待评审 Placement to be reviewed"),
    B("4", "2", "在同级别发展 Development on the same level"),
    C("3", "3", "有发展前途的人才 Promising talent"),
    D("2", "3", "一个发展步骤 1-Step"),
    E("1", "3", "就绪 Ready"),
    ;


    private final String type;


    private final String mapping;
    private final String description;

    public static String getDescByType(String type) {
        for (PotentialLevelEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getDescription();
            }
        }
        return "";
    }

    public static String getMappingByType(String type) {
        for (PotentialLevelEnum v : values()) {
            if (v.getType().equals(type)) {
                return v.getMapping();
            }
        }
        return "";
    }

    public static String getTypeByDesc(String desc) {
        for (PotentialLevelEnum v : values()) {
            if (v.getDescription().equals(desc)) {
                return v.getType();
            }
        }
        return "";
    }
}
