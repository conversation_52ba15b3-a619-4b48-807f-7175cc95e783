package com.bbac.hrsf.test;

import cn.hutool.core.date.*;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.*;
import com.google.common.base.Throwables;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.*;
import java.util.concurrent.*;

/**
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class SFInterfaceTest {
    public static ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
            .setNameFormat("access-super-prime-%d").build();
    public static void main(String[] args) throws ExecutionException, InterruptedException {

        // 原始日期字符串
        String dateStr = "/Date(1695718722000+0000)/";

// 使用正则表达式提取时间戳部分
        String timestampStr = dateStr.replaceAll("^/Date\\((\\d+)\\+\\d+\\)/$", "$1");

        try {
            // 将时间戳字符串转换为长整数（毫秒）
            long timestamp = Long.parseLong(timestampStr);
            System.out.println("时间戳（毫秒）: " + timestamp);
        } catch (NumberFormatException e) {
            System.err.println("无法解析日期字符串");
        }
//
//
//        List<String> strings = Arrays.asList("107046", "107018", "107009", "108796", "107010", "103105", "106050"
//                , "106037", "108794", "106011", "106049", "kjonnah", "106038", "103104", "106010"
//                , "104048", "104010"/*, "103029"*/, "104018", "104036"
//                , "108792", "104047", "104009", "ceduarda", "802985");
//        DateTime startDate = DateUtil.date();
//
//
//
//        StandardPBEStringEncryptor encryptor = new StandardPBEStringEncryptor();
//        encryptor.setPassword("3Da3m2NKPvY^LXcH"); //设置加密盐值
////        encryptor.setPassword("hrsf"); //设置加密盐值
//        String passwordEn = encryptor.encrypt("cJ9i4z=5n-7:apc");
//        String passwordEn1 = encryptor.encrypt(".cWe7*Y_ke7nyAv");
//        String passwordEn2 = encryptor.encrypt("BLwqEp@Wcy!-KiC");
////        String passwordEn1 = encryptor.encrypt("Thrsf!123");
////        String passwordEn2 = encryptor.encrypt("C*f3Gs8!tPXD@V/");
//        String passwordDe = encryptor.decrypt("mpk3sOHfraxuCQc7q7i4UroXmAw5taS/");
//        System.out.println("password密文：" + passwordEn);
//        System.out.println("password密文：" + passwordEn1);
//        System.out.println("password密文：" + passwordEn2);
////        System.out.println("password密文：" + passwordEn1);
////        System.out.println("password密文：" + passwordEn2);
//        System.out.println("password明文：" + passwordDe);

//        log.info("获取绩效结果开始时间:{}",startDate);
//        ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(30,
//                50,
//                120,
//                TimeUnit.MILLISECONDS,
//                new LinkedBlockingQueue<>(32),
//                namedThreadFactory,
//                new ThreadPoolExecutor.AbortPolicy());
//        List<CompletableFuture> list=new ArrayList<>();
//        strings.stream().forEach(o->{
//            list.add(CompletableFuture.supplyAsync(() -> getRating(o),threadPoolExecutor));
//        });
//        CompletableFuture cf4 = CompletableFuture.allOf(list.toArray(new CompletableFuture[list.size()]));
//        cf4.get();
//        strings.stream().forEach(o->{
//            getRating(o);
//        });
//        DateTime endDate = DateUtil.date();
//        long between = DateUtil.between(startDate, endDate, DateUnit.SECOND);
//        log.info("获取绩效结果结束时间:{}",endDate);
//        log.info("25名员工总用时(秒):{}",between);
//        threadPoolExecutor.shutdown();
    }

//    private static String getRating(String code) {
//        DateTime startDate = DateUtil.date();
//        String template = "https://apisalesdemo8.successfactors.com/odata/v2/FormHeader?$select=formContents&$format=JSON&$filter=formSubjectId eq '{}' and formReviewStartDate eq datetime'2020-01-01T00:00:00' and formTemplateId eq '1146'";
//        String format = StrUtil.format(template, code);
//        String body = HttpRequest.get(format)
//                .basicAuth("sfadmin@SFPART062190", "123").execute().body();
//        JSONObject jsonObject = JSONUtil.parseObj(body);
//        String str = jsonObject.getJSONObject("d").getJSONArray("results").getJSONObject(0).getJSONObject("formContents")
//                .getJSONObject("__deferred").getStr("uri");
//
//        String bodyTwo = HttpRequest.get(str + "?$format=JSON&$select=lastModifiedDate,formContentId")
//                .basicAuth("sfadmin@SFPART062190", "123").execute().body();
//        JSONObject jsonObjectTwo = JSONUtil.parseObj(bodyTwo);
//        JSONArray jsonArray = jsonObjectTwo.getJSONObject("d").getJSONArray("results");
//
//        jsonArray.sort(Comparator.comparing(obj -> ((JSONObject) obj).getStr("formContentId")).reversed());
//
//        String strTwo = jsonArray.getJSONObject(0).getJSONObject("__metadata").getStr("uri");
//        String replace = strTwo.replace("FormContent", "FormSummarySection") + "/overallFormRating?$format=JSON&$select=rating";
//        String bodyThree = HttpRequest.get(replace)
//                .basicAuth("sfadmin@SFPART062190", "123").execute().body();
//        JSONObject jsonObjectT = JSONUtil.parseObj(bodyThree);
//        String rating = jsonObjectT.getJSONObject("d").getStr("rating");
//        DateTime endDate = DateUtil.date();
//        long between = DateUtil.between(startDate, endDate, DateUnit.SECOND);
//
//        log.info("员工号:{},绩效结果:{},用时(秒):{}", code, rating, between);
//        return rating;
//    }


}