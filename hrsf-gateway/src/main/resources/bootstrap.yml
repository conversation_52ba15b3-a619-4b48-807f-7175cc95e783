server:
  port: 9999
  ssl:
    enabled: false
    # 证书位置
    key-store: classpath:keystore.p12
    # 证书别名
    #key-alias: gateway
    # 秘钥库类型
    key-store-type: PKCS12
    # 秘钥库口令（密码）
    key-store-password: 123456
spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      discovery:
        group: DEFAULT_GROUP  # 默认组名，保持与路由配置一致
        server-addr: ${NACOS_HOST:hrsf-register}:${NACOS_PORT:8848}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  profiles:
    active: @profiles.active@

#logging:
#  level:
#    org.springframework.cloud.gateway: TRACE
#    reactor.netty.http.client: DEBUG
#    com.alibaba.nacos: DEBUG

