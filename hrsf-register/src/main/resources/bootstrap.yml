server:
  port: 8848
  tomcat:
    basedir: logs

nacos:
  core:
    auth:
      system.type: nacos
      default.token.secret.key: SecretKey012345678901234567890123456789012345678901234567890123456789
  security:
    ignore:
      urls: /,/error,/**/*.css,/**/*.js,/**/*.html,/**/*.map,/**/*.svg,/**/*.png,/**/*.ico,/console-fe/public/**,/v1/auth/**,/v1/console/health/**,/actuator/**,/v1/console/server/**

spring:
  security:
    enabled: true
  application:
    name: @project.artifactId@

useAddressServer: true

management:
  endpoint:
    gateway:
      enabled: false
