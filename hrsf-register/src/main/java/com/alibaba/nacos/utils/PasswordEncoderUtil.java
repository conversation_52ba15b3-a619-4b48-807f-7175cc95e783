/*
 * Copyright 1999-2018 Alibaba Group Holding Ltd.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.alibaba.nacos.utils;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * Password encoder tool.
 *
 * <AUTHOR>
 */
public class PasswordEncoderUtil {

	public static Boolean matches(String raw, String encoded) {
		return new BCryptPasswordEncoder().matches(raw, encoded);
	}

	public static String encode(String raw) {
		return new BCryptPasswordEncoder().encode(raw);
	}

	public static void main(String[] args) {
		String encode = PasswordEncoderUtil.encode("123456");
		System.out.println(encode);
		System.out.println(PasswordEncoderUtil.matches("123456",encode));
	}
}
