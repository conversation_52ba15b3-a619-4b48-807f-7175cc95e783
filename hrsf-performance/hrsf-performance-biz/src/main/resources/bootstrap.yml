server:
  port: 5001

spring:
  application:
    name: @artifactId@
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:hrsf-register}:${NACOS_PORT:8848}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        file-extension: yml
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  profiles:
    active: @profiles.active@

management:
  endpoints:
    web:
      exposure:
        include: [] # 空列表表示不暴露任何端点
  endpoint:
    health:
      enabled: false # 你也可以单独禁用特定的端点，如health
