<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.performance.mapper.HrsfCalibrationFilterMapper">

    <delete id="deleteFilterByStaffIdAndBaseId">

        delete from HRSF_CALIBRATION_FILTER
        where CALIBRATION_BASE_ID=#{calibrationId} and STAFF_ID in
        <foreach item="staffId" collection="staffIds" open="(" separator="," close=")">
            #{staffId}
        </foreach>
    </delete>
</mapper>
