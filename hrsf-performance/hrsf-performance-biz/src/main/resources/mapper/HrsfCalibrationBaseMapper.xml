<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.performance.mapper.HrsfCalibrationBaseMapper">

    <resultMap id="baseResultMap" type="com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO">
        <id column="ID" property="id"/>
        <result column="ASSESS_YEAR" property="assesYear"/>
        <result column="ASSESS_TYPE" property="assesType"/>
        <result column="USER_LEVEL" property="userLevel"/>
        <result column="ORGANIZATION" property="organization"/>
        <result column="NAME" property="name"/>
        <result column="TEMPLATE" property="template"/>
        <result column="STATUS" property="status"/>
        <result column="CURRENT_HANDLER" property="currentHandler"/>
        <result column="PROCESS_STATUS" property="processStatus"/>
        <result column="PROCESS_STEP" property="processStep"/>
        <result column="PROCESS_INSTANCE_ID" property="processInstanceId"/>
        <result column="TASK_ID" property="taskId"/>
    </resultMap>

    <select id="selectCustomPage" resultMap="baseResultMap">
        SELECT
        *
        FROM
        (
        SELECT
        RES.ID_ AS TASK_ID,RES.PROC_INST_ID_,RES.END_TIME_,RES.DELETE_REASON_,
        row_number () over (
        PARTITION BY RES.PROC_INST_ID_
        ORDER BY
        RES.END_TIME_ DESC
        ) AS row_number
        FROM
        ACT_HI_TASKINST RES
        WHERE
        RES.ASSIGNEE_ = #{userId}
        ) a
        LEFT JOIN HRSF_CALIBRATION_BASE b ON a.PROC_INST_ID_ = b.PROCESS_INSTANCE_ID
        WHERE
        row_number = 1
        AND b.id IS NOT NULL
        AND b.DEL_FLAG = '0'
        AND a.END_TIME_ IS NOT NULL
        AND (
        (a.DELETE_REASON_ = ''
        OR a.DELETE_REASON_ IS NULL) OR a.DELETE_REASON_ ='MI_END'
        )
        <if test="query.assesType != null and query.assesType != ''">
            AND b.ASSESS_TYPE = #{query.assesType }
        </if>
        <if test="query.assesYear != null and query.assesYear != ''">
            AND b.ASSESS_YEAR = #{query.assesYear }
        </if>
        <if test="query.organization != null and query.organization != ''">
            AND b.ORGANIZATION = #{query.organization }
        </if>
        <if test="query.userLevel != null and query.userLevel != ''">
            AND b.USER_LEVEL = #{query.userLevel }
        </if>
        <if test="query.processStep != null and query.processStep != ''">
            AND b.PROCESS_STEP = #{query.processStep }
        </if>
        <if test="query.status != null">
            AND b.STATUS = #{query.status }
        </if>
        <if test="query.name != null and query.name != ''">
            <bind name="usernameLike" value="'%' + query.name + '%'"/>
            AND b.NAME LIKE #{usernameLike}
        </if>
        <if test="query.calibrationBaseId!= null and query.calibrationBaseId.size()>0">
            AND b.ID in
            <foreach item="baseId" index="index" collection="query.calibrationBaseId"
                     open="(" separator="," close=")">
                #{baseId}
            </foreach>
        </if>
        ORDER BY
        END_TIME_ DESC,b.ID
    </select>
    <select id="selectCustomTodoPage" resultMap="baseResultMap">
        SELECT
        *
        FROM
        (
        SELECT
        RES.ID_ AS TASK_ID,RES.PROC_INST_ID_,
        row_number () over (
        PARTITION BY RES.PROC_INST_ID_
        ORDER BY
        RES.CREATE_TIME_ DESC
        ) AS row_number
        FROM
        ACT_RU_TASK RES
        WHERE
        RES.ASSIGNEE_ = #{userId}
        ) a
        LEFT JOIN HRSF_CALIBRATION_BASE b ON a.PROC_INST_ID_ = b.PROCESS_INSTANCE_ID
        WHERE
        row_number = 1
        AND b.id IS NOT NULL
        AND b.DEL_FLAG = '0'
        <if test="query.assesType != null and query.assesType != ''">
            AND b.ASSESS_TYPE = #{query.assesType }
        </if>
        <if test="query.assesYear != null and query.assesYear != ''">
            AND b.ASSESS_YEAR = #{query.assesYear }
        </if>
        <if test="query.organization != null and query.organization != ''">
            AND b.ORGANIZATION = #{query.organization }
        </if>
        <if test="query.userLevel != null and query.userLevel != ''">
            AND b.USER_LEVEL = #{query.userLevel }
        </if>
        <if test="query.processStep != null and query.processStep != ''">
            AND b.PROCESS_STEP = #{query.processStep }
        </if>
        <if test="query.status != null">
            AND b.STATUS = #{query.status }
        </if>
        <if test="query.name != null and query.name != ''">
            <bind name="usernameLike" value="'%' + query.name + '%'"/>
            AND b.NAME LIKE #{usernameLike}
        </if>
        <if test="query.calibrationBaseId!= null and query.calibrationBaseId.size()>0">
            AND b.ID in
            <foreach item="baseId" index="index" collection="query.calibrationBaseId"
                     open="(" separator="," close=")">
                #{baseId}
            </foreach>
        </if>
        ORDER BY
        b.UPDATE_TIME DESC,b.ID
    </select>
</mapper>
