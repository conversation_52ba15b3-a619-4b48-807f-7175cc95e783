package com.bbac.hrsf.performance.service.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessInstanceDeleteReasonEnum;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessInstanceResultEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.performance.api.entity.HrsfBpmProcessInstanceExt;
import com.bbac.hrsf.performance.api.entity.HrsfTaskExt;
import com.bbac.hrsf.performance.api.flowable.definition.vo.BpmProcessInstanceCreateReqDTO;
import com.bbac.hrsf.performance.api.flowable.task.vo.instance.*;
import com.bbac.hrsf.performance.mapper.HrsfBpmProcessInstanceExtMapper;
import com.bbac.hrsf.performance.service.definition.BpmProcessDefinitionService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;


/**
 * 流程实例 Service 实现类
 * <p>
 * ProcessDefinition & ProcessInstance & Execution & Task 的关系：
 * 1. https://blog.csdn.net/bobozai86/article/details/105210414
 * <p>
 * HistoricProcessInstance & ProcessInstance 的关系：
 * 1.https://my.oschina.net/843294669/blog/719024
 * 简单来说，前者 = 历史 + 运行中的流程实例，后者仅是运行中的流程实例
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BpmProcessInstanceServiceImpl implements BpmProcessInstanceService {

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private HrsfBpmProcessInstanceExtMapper processInstanceExtMapper;

    @Resource
    private BpmProcessDefinitionService processDefinitionService;
    @Resource
    private HistoryService historyService;

    @Resource
    private IHrsfTaskExtService hrsfTaskExtService;


    @Override
    public ProcessInstance getProcessInstance(String id) {
        return runtimeService.createProcessInstanceQuery().processInstanceId(id).singleResult();
    }

    @Override
    public List<ProcessInstance> getProcessInstances(Set<String> ids) {
        return runtimeService.createProcessInstanceQuery().processInstanceIds(ids).list();
    }

    @Override
    public IPage<BpmProcessInstancePageItemRespVO> getMyProcessInstancePage(String userId,
                                                                            BpmProcessInstanceMyPageReqVO pageReqVO) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createProcessInstance(String userId, @Valid BpmProcessInstanceCreateReqVO createReqVO) {
        // 获得流程定义
        ProcessDefinition definition = processDefinitionService.getProcessDefinition(createReqVO.getProcessDefinitionId());
        // 发起流程
        return createProcessInstance0(userId, definition, createReqVO);
    }

    @Override
    public String createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqDTO createReqDTO) {
        return null;
    }

    @Override
    public BpmProcessInstanceRespVO getProcessInstanceVO(String id) {

        return null;
    }

    @Override
    public void cancelProcessInstance(String userId, @Valid BpmProcessInstanceCancelReqVO cancelReqVO) {
        // 校验流程实例存在
        ProcessInstance instance = getProcessInstance(cancelReqVO.getId());
        if (instance == null) {
            throw new CheckedException("PROCESS_INSTANCE_CANCEL_FAIL_NOT_EXISTS");
        }
        // 只能取消自己的
        if (!Objects.equals(instance.getStartUserId(), userId)) {
            throw new CheckedException("PROCESS_INSTANCE_CANCEL_FAIL_NOT_SELF");
        }

        // 通过删除流程实例，实现流程实例的取消,
        // 删除流程实例，正则执行任务ACT_RU_TASK. 任务会被删除。通过历史表查询
        deleteProcessInstance(cancelReqVO.getId(),
                BpmProcessInstanceDeleteReasonEnum.CANCEL_TASK.format(cancelReqVO.getReason()));
    }

    @Override
    public List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids) {
        return historyService.createHistoricProcessInstanceQuery().processInstanceIds(ids).list();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessInstanceExtReject(String id, String comment, Task task) {
        /**
         * 这里固定返回第一个节点
         */
        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(id)
                .moveActivityIdTo(task.getTaskDefinitionKey(), ProcessStepEnum.HRBP.getTaskId())
                .changeState();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateProcessInstanceExtRejectLastStep(String id, String comment, Task task) {
        /**
         * 根据当前流程节点返回到上一个流程节点
         */
        runtimeService.createChangeActivityStateBuilder()
                .processInstanceId(id)
                .moveActivityIdTo(task.getTaskDefinitionKey(), ProcessStepEnum.getPreTaskKey(task.getTaskDefinitionKey()))
                .changeState();
    }

    private void deleteProcessInstance(String id, String reason) {
        runtimeService.deleteProcessInstance(id, reason);
    }

    private String createProcessInstance0(String userId, ProcessDefinition definition,
                                          BpmProcessInstanceCreateReqVO createReqVO) {
        // 校验流程定义
        if (definition == null) {
            throw new CheckedException("PROCESS_DEFINITION_NOT_EXISTS");
        }
        if (definition.isSuspended()) {
            throw new CheckedException("PROCESS_DEFINITION_IS_SUSPENDED");
        }

        // 创建流程实例
        ProcessInstance instance = runtimeService.startProcessInstanceById(definition.getId(),
                String.valueOf(createReqVO.getCalibrationBaseId()), createReqVO.getVariables());

        // 设置流程名字
        runtimeService.setProcessInstanceName(instance.getId(), definition.getName());

        processInstanceExtMapper.insert(new HrsfBpmProcessInstanceExt()
                .setProcessInstanceId(instance.getId())
                .setFormVariables(createReqVO.getVariables())
                .setStartUserId(userId)
                .setCalibrationBaseId(createReqVO.getCalibrationBaseId())
                .setProcessDefinitionId(instance.getProcessDefinitionId())
                .setName(definition.getName()));
        // 补全流程实例的拓展表

        /**
         * 新增一条任务开始的记录
         *
         */
        // 更新任务拓展表为通过
        hrsfTaskExtService.save(new HrsfTaskExt()
                .setEndTime(new Date())
                .setFullName(createReqVO.getFullName())
                .setStaffId(createReqVO.getStaffId())
                .setName("系统")
                .setTaskId("--")
                .setProcessInstanceId(instance.getId())
                .setProcessDefinitionId(instance.getProcessDefinitionId())
                .setProcessStep(ProcessStepEnum.START.getType())
                .setResult(BpmProcessInstanceResultEnum.LAUNCH.getResult())
                .setApproveComment("启动绩效考核").setCalibrationBaseId(createReqVO.getCalibrationBaseId()));

        return instance.getId();
    }

}
