package com.bbac.hrsf.performance.service.task;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.feign.RemoteUserService;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessParamEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.constant.enums.UserLevelEnum;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.entity.HrsfBpmProcessInstanceExt;
import com.bbac.hrsf.performance.api.entity.HrsfTaskExt;
import com.bbac.hrsf.performance.mapper.HrsfTaskExtMapper;
import com.bbac.hrsf.performance.service.definition.IHrsfBpmProcessInstanceExtService;
import lombok.RequiredArgsConstructor;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-25
 */
@Service
@RequiredArgsConstructor
public class HrsfTaskExtServiceImpl extends ServiceImpl<HrsfTaskExtMapper, HrsfTaskExt> implements IHrsfTaskExtService {

    private final IHrsfBpmProcessInstanceExtService processInstanceExtService;

    private final RemoteUserService remoteUserService;

    private final TaskService taskService;

    private final RuntimeService runtimeService;

    @Override
    public IPage<HrsfTaskExt> getProcessStepPage(Page page, Long calibrationBaseId) {

        Optional<HrsfBpmProcessInstanceExt> optional = processInstanceExtService.list(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                .eq(HrsfBpmProcessInstanceExt::getCalibrationBaseId, calibrationBaseId)).stream().findFirst();
        if (optional.isPresent()) {
            return baseMapper.selectPage(page, buildQueryWrapper(optional.get().getProcessInstanceId()));
        }
        return new Page<>();
    }

    @Override
    public Map<String, Object> getProcessStepOperation(HrsfCalibrationBase calibrationBase, String processStatus) {

        String username = SecurityUtils.getUser().getUsername();
        HashMap<String, Object> userBaseMap = new HashMap<>(16);
        Optional<HrsfBpmProcessInstanceExt> optional = processInstanceExtService.list(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                .orderByDesc(HrsfBpmProcessInstanceExt::getCreateTime)
                .eq(HrsfBpmProcessInstanceExt::getCalibrationBaseId, calibrationBase.getId())).stream().findFirst();
        if (optional.isPresent()) {
            HrsfBpmProcessInstanceExt processInstanceExt = optional.get();
            Map<String, Object> formVariableMap = processInstanceExt.getFormVariables();
            /**
             * 从主数据表中获取员工
             * 5个节点分别取数据
             */
            if (formVariableMap.get(BpmProcessParamEnum.TASK01.getResult()) != null) {
                R<List<HrsfUserVO>> staffIdA = remoteUserService.getUserInfoByStaffId((List<String>)
                        formVariableMap.get(BpmProcessParamEnum.TASK01.getResult()), SecurityConstants.FROM_IN);
                userBaseMap.put(ProcessStepEnum.HRBP.getSign(), staffIdA.getCode() == 0 ? staffIdA.getData() : null);
            }
            if (formVariableMap.get(BpmProcessParamEnum.TASK02.getResult()) != null) {
                R<List<HrsfUserVO>> staffIdB = remoteUserService.getUserInfoByStaffId((List<String>)
                        formVariableMap.get(BpmProcessParamEnum.TASK02.getResult()), SecurityConstants.FROM_IN);
                userBaseMap.put(ProcessStepEnum.OWNER.getSign(), staffIdB.getCode() == 0 ? staffIdB.getData() : null);
            }
            if (formVariableMap.get(BpmProcessParamEnum.TASK03.getResult()) != null) {
                R<List<HrsfUserVO>> staffIdC = remoteUserService.getUserInfoByStaffId((List<String>)
                        formVariableMap.get(BpmProcessParamEnum.TASK03.getResult()), SecurityConstants.FROM_IN);
                userBaseMap.put(ProcessStepEnum.HR_MANAGER.getSign(), staffIdC.getCode() == 0 ? staffIdC.getData() : null);
            }
            if (formVariableMap.get(BpmProcessParamEnum.TASK04.getResult()) != null) {
                R<List<HrsfUserVO>> staffIdD = remoteUserService.getUserInfoByStaffId((List<String>)
                        formVariableMap.get(BpmProcessParamEnum.TASK04.getResult()), SecurityConstants.FROM_IN);
                userBaseMap.put(ProcessStepEnum.HRL4.getSign(), staffIdD.getCode() == 0 ? staffIdD.getData() : null);
            }
            if (formVariableMap.get(BpmProcessParamEnum.TASK05.getResult()) != null) {
                R<List<HrsfUserVO>> staffIdE = remoteUserService.getUserInfoByStaffId((List<String>)
                        formVariableMap.get(BpmProcessParamEnum.TASK05.getResult()), SecurityConstants.FROM_IN);
                userBaseMap.put(ProcessStepEnum.HRL3.getSign(), staffIdE.getCode() == 0 ? staffIdE.getData() : null);
            }

            /**
             * 判断当前节点的审批人中,又没有当前登录人的ID
             * 没有的话则标识是新增的审批人,将hiddenAddButton
             */
            List<HrsfUserVO> userVOList = (List<HrsfUserVO>) userBaseMap.get(ProcessStepEnum.getSignByType(calibrationBase.getProcessStep()));
            if (CollectionUtil.isNotEmpty(userVOList)) {
                List<String> staffIdList = userVOList.stream().map(HrsfUserVO::getStaffId).collect(toList());
                if (staffIdList.contains(username)) {
                    userBaseMap.put("hiddenAddButton", false);
                } else {
                    userBaseMap.put("hiddenAddButton", true);
                }
            } else {
                userBaseMap.put("hiddenAddButton", true);
            }

            /**
             * 如果是已办的情况下,看看当前登录人
             * ,是否是属于绩效协调员
             */
            List<HrsfUserVO> hrbpList = (List<HrsfUserVO>) userBaseMap.get(ProcessStepEnum.HRBP.getSign());
            if ("1".equals(processStatus) && CollectionUtil.isNotEmpty(hrbpList)) {
                List<String> staffIdList = hrbpList.stream().map(HrsfUserVO::getStaffId).collect(toList());
                //修复问题清单322问题 -- update by 2022-07-23
                if (staffIdList.contains(username) && UserLevelEnum.L7.name().equals(calibrationBase.getUserLevel())
                        && CalibrationTemplateEnum.ONE.getType().equals(calibrationBase.getTemplate())) {
                    userBaseMap.put("hiddenFunctionButton", false);
                } else {
                    userBaseMap.put("hiddenFunctionButton", true);
                }
            } else {
                userBaseMap.put("hiddenFunctionButton", true);
            }
        }
        return userBaseMap;
    }

    @Override
    public Boolean updateApprove(HrsfCalibrationBase calibrationBase, Map<String, Object> approveMap) {
        HrsfBpmProcessInstanceExt instanceExt = processInstanceExtService.getOne(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                .eq(HrsfBpmProcessInstanceExt::getCalibrationBaseId, calibrationBase.getId()));
        /**
         * 这里会修改审批人,修改完以后更新该数据
         * 只会更新task01和task02的节点
         *
         */
        instanceExt.setFormVariables(approveMap);
        instanceExt.setChangeFlag(true);

        /**
         * 重新设置参数,用于修改非当前节点的审批人
         */
        runtimeService.setVariables(instanceExt.getProcessInstanceId(), approveMap);
        /**
         * 这里用于处理当前节点的审批人新增和修改
         */
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(instanceExt.getProcessInstanceId()).list();
        if (CollectionUtil.isNotEmpty(taskList)) {
            Map<String, String> assigneeExecutionIdMap = taskList.stream().collect(Collectors.toMap(Task::getAssignee, Task::getExecutionId));
            List<String> userListOriginal = taskList.stream().map(Task::getAssignee).collect(toList());
            if (ProcessStepEnum.HRBP.getType().equals(calibrationBase.getProcessStep())) {
                List<String> pmsUserList = (List) approveMap.get(BpmProcessParamEnum.TASK01.getResult());
                handleInstanceExecution(pmsUserList, userListOriginal, calibrationBase.getProcessInstanceId(), assigneeExecutionIdMap, "pmsUser", ProcessStepEnum.HRBP.getTaskId());
            }
            if (ProcessStepEnum.OWNER.getType().equals(calibrationBase.getProcessStep())) {
                List<String> deptHeadList = (List) approveMap.get(BpmProcessParamEnum.TASK02.getResult());
                handleInstanceExecution(deptHeadList, userListOriginal, calibrationBase.getProcessInstanceId(), assigneeExecutionIdMap, "deptHead", ProcessStepEnum.OWNER.getTaskId());
            }
        }
        return processInstanceExtService.saveOrUpdate(instanceExt);
    }

    /**
     * 调用工作流的加签减签的方法
     *
     * @param userList
     * @param userListOriginal
     * @param processInstanceId
     * @param assigneeExecutionIdMap
     * @param assignParam
     * @param stepTask
     */
    private void handleInstanceExecution(List<String> userList, List<String> userListOriginal, String processInstanceId, Map<String, String> assigneeExecutionIdMap, String assignParam, String stepTask) {
        /**
         * 比较两个List,找出需要新增的任务和需要删除的任务
         */
        // 差集 (list1 - list2)
        List<String> reduce1 = userList.stream().filter(item -> !userListOriginal.contains(item)).collect(toList());
        // 差集 (list2 - list1)
        List<String> reduce2 = userListOriginal.stream().filter(item -> !userList.contains(item)).collect(toList());

        reduce1.stream().forEach(assignee -> runtimeService.addMultiInstanceExecution(stepTask
                , processInstanceId, Collections.singletonMap(assignParam, assignee)));
        reduce2.stream().forEach(assignee -> {
            if (assigneeExecutionIdMap != null && assigneeExecutionIdMap.get(assignee) != null) {
                runtimeService.deleteMultiInstanceExecution(assigneeExecutionIdMap.get(assignee), true);
            }
        });
    }

    private LambdaQueryWrapper buildQueryWrapper(String processInstanceId) {
        LambdaQueryWrapper<HrsfTaskExt> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(processInstanceId)) {
            wrapper.eq(HrsfTaskExt::getProcessInstanceId, processInstanceId);
        }
        wrapper.orderByAsc(HrsfTaskExt::getCreateTime);
        return wrapper;
    }
}
