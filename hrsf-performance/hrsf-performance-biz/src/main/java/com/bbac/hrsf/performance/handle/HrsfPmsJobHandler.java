package com.bbac.hrsf.performance.handle;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.service.IHrsfCalibrationBaseService;
import com.bbac.hrsf.performance.service.IHrsfPmstartService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.handle.DemoJobHandler</li>
 * <li>CreateTime : 2022/06/02 13:13:39</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HrsfPmsJobHandler {

    private final IHrsfPmstartService hrsfPmstartService;

    private final IHrsfCalibrationBaseService hrsfCalibrationBaseService;

    /**
     * 7、判断校准会议的截止日期,是否需要冻结
     */
    @XxlJob("freezeCalibrationBase")
    public ReturnT<String> freezeCalibrationBase(String param) {
        /**
         * 查出截止日期小于当前日期的数据
         */
        List<HrsfPmstart> pmStartList = hrsfPmstartService.list(Wrappers.<HrsfPmstart>query().lambda().le(HrsfPmstart::getFinishDate, LocalDate.now()));
        hrsfCalibrationBaseService.freezeCalibrationBase(pmStartList);

        return ReturnT.SUCCESS;
    }
}