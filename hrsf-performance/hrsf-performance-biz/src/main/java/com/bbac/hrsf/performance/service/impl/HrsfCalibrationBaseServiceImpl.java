package com.bbac.hrsf.performance.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.CalibrationBaseAndIdDTO;
import com.bbac.hrsf.admin.api.dto.OverviewCalibrationDTO;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationFilter;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.feign.RemoteUserService;
import com.bbac.hrsf.admin.api.feign.RemoteUserTimeService;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessDefinitionEnum;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessParamEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.FormStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.LevelFlagEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.ScoreSourceEnum;
import com.bbac.hrsf.common.core.constant.enums.UserLevelEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.exception.ValidateCodeException;
import com.bbac.hrsf.common.core.pojo.ErrorMessageSubVo;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.service.HrsfLoginUser;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import com.bbac.hrsf.performance.api.dto.CalibrationQueryDTO;
import com.bbac.hrsf.performance.api.dto.HrsfCalibrationBaseDTO;
import com.bbac.hrsf.performance.api.dto.HrsfPmsuserBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfBpmProcessInstanceExt;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.api.entity.HrsfProcessDefinitionExt;
import com.bbac.hrsf.performance.api.flowable.task.vo.instance.BpmProcessInstanceCreateReqVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskApproveReqVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskRejectReqVO;
import com.bbac.hrsf.performance.convert.HrsfPmsConvert;
import com.bbac.hrsf.performance.event.CalibrationEvent;
import com.bbac.hrsf.performance.mapper.HrsfCalibrationBaseMapper;
import com.bbac.hrsf.performance.mapper.HrsfCalibrationFilterMapper;
import com.bbac.hrsf.performance.service.IHrsfCalibrationBaseService;
import com.bbac.hrsf.performance.service.IHrsfCalibrationFilterService;
import com.bbac.hrsf.performance.service.IHrsfPmstartService;
import com.bbac.hrsf.performance.service.definition.HrsfProcessDefinitionExtService;
import com.bbac.hrsf.performance.service.definition.IHrsfBpmProcessInstanceExtService;
import com.bbac.hrsf.performance.service.task.BpmProcessInstanceService;
import com.bbac.hrsf.performance.service.task.BpmTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.Collator;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HrsfCalibrationBaseServiceImpl extends ServiceImpl<HrsfCalibrationBaseMapper, HrsfCalibrationBase> implements IHrsfCalibrationBaseService {

    private final IHrsfCalibrationFilterService calibrationFilterService;

    private final RemoteUserService remoteUserService;

    private final RemoteUserTimeService remoteUserTimeService;

    private final TaskService taskService;

    private final BpmTaskService bpmTaskService;

    private final ApplicationEventPublisher publisher;

    private final IHrsfBpmProcessInstanceExtService processInstanceExtService;

    private final IHrsfPmstartService hrsfPmstartService;

    private final BpmProcessInstanceService processInstanceService;

    private final HrsfProcessDefinitionExtService processDefinitionExtService;

    private final HrsfCalibrationFilterMapper hrsfCalibrationFilterMapper;

    private final String MESSAGE_INFO_RETURN = "退回表单成功{}条";

    @Value("${template.link}")
    private String link;

    @Override
    public IPage<HrsfCalibrationBaseTaskVO> getCalibrationPage(CalibrationQueryDTO queryDTO) {

        String userId = SecurityUtils.getUser().getId();
        IPage<HrsfCalibrationBaseTaskVO> pageObj;
        if (CollectionUtil.isNotEmpty(queryDTO.getStaffIdList())) {
            Set<Long> idSet = calibrationFilterService.lambdaQuery().in(HrsfCalibrationFilter::getStaffId, queryDTO.getStaffIdList()).
                    list().stream().map(HrsfCalibrationFilter::getCalibrationBaseId).collect(Collectors.toSet());
            /**
             * 解决如果不在校准会议中
             * 直接返回空对象
             */
            if (CollectionUtil.isEmpty(idSet)) {
                return new Page<>();
            }
            queryDTO.setCalibrationBaseId(idSet);
        }
        if (ProcessStatusEnum.RUNNING.getStatus().equals(queryDTO.getProcessStatus())) {
            /**
             *
             //先去查询该用户下的所有代办事项,
             TaskQuery taskQuery = taskService.createTaskQuery()
             .taskAssignee(userId)
             .orderByTaskCreateTime().desc();
             List<Task> taskList = taskQuery.list();
             log.info("所有代办的数量:{}", taskQuery.count());
             processInstanceIds = (taskList.size() > 1000 ? taskList.subList(0, 1000) : taskList).stream().map(Task::getProcessInstanceId).collect(Collectors.toSet());
             instanceTaskIdMap = taskList.stream().collect(Collectors.toMap(Task::getProcessInstanceId, Task::getId, (oldVal, newVal) -> oldVal));
             */
            pageObj = baseMapper.selectCustomTodoPage(queryDTO, userId);
        } else {
            /**
             * HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery()
             *                     .finished()
             *                     .taskAssignee(userId)
             *                     .taskWithoutDeleteReason()
             *                     .orderByHistoricTaskInstanceEndTime().desc();
             *             List<HistoricTaskInstance> historicList = taskQuery.list();
             *             processInstanceIds = historicList.stream().map(HistoricTaskInstance::getProcessInstanceId)
             *                     .collect(Collectors.toCollection(LinkedHashSet::new));
             *
             *              * 这里满足客户需求
             *              * 已办事项只显示一条数据
             *              * 已办可能会有多条数据，只取最近的一条展示
             *              * 因为是倒序则用(oldVal, newVal) -> oldVal
             *
             *  instanceTaskIdMap = historicList.stream().collect(Collectors.
             * toMap(HistoricTaskInstance::getProcessInstanceId, HistoricTaskInstance::getId, (oldVal, newVal) -> oldVal));
             */
            /**
             * 优化获取已办的数据列表
             */
            pageObj = baseMapper.selectCustomPage(queryDTO, userId);
        }
        pageObj.setRecords(pageObj.getRecords().stream()
                .map(baseTaskVO -> {
                    /**
                     * 这里设置流程步骤状态为入参的状态
                     */
                    baseTaskVO.setProcessStatus(queryDTO.getProcessStatus());
                    String currentHandler = getCurrentHandlerStr(baseTaskVO.getProcessInstanceId());
                    baseTaskVO.setCurrentHandler(currentHandler);
                    baseTaskVO.setTotalNum(remoteUserService.getTotalNumByCalibrationBaseId(baseTaskVO.getId(), SecurityConstants.FROM_IN));
                    return baseTaskVO;
                }).collect(Collectors.toList()));
        return pageObj;
    }


    private String getCurrentHandlerStr(String processInstanceId) {
        if (StrUtil.isNotBlank(processInstanceId)) {
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
            if (CollUtil.isNotEmpty(taskList)) {
                List<String> collect = taskList.stream().map(Task::getAssignee).collect(Collectors.toList());
                R<List<HrsfUserVO>> userInfoByStaffId = remoteUserService.getUserInfoByStaffId(collect, SecurityConstants.FROM_IN);
                if (userInfoByStaffId.getCode() == 0) {
                    List<HrsfUserVO> data = userInfoByStaffId.getData();
                    if (CollUtil.isNotEmpty(data)) {
                        List<String> collect1 = data.stream().map(HrsfUserVO::getFullName).collect(Collectors.toList());
                        String join = StringUtils.join(collect1, ",");
                        return join;
                    }
                } else {
                    throw new CheckedException(userInfoByStaffId.getMsg());
                }
            }
        }
        return "";
    }

    @Override
    public IPage<HrsfCalibrationBaseTaskVO> getCalibrationBasePage(CalibrationQueryDTO queryDTO) {
        if (CollectionUtil.isNotEmpty(queryDTO.getStaffIdList())) {
            Set<Long> idSet = calibrationFilterService.lambdaQuery().in(HrsfCalibrationFilter::getStaffId, queryDTO.getStaffIdList()).
                    list().stream().map(HrsfCalibrationFilter::getCalibrationBaseId).collect(Collectors.toSet());
            /**
             * 解决如果不在校准会议中
             * 直接返回空对象
             */
            if (CollectionUtil.isEmpty(idSet)) {
                return new Page<>();
            }
            queryDTO.setCalibrationBaseId(idSet);
        }
        Page pageObj = baseMapper.selectPage(queryDTO, buildQueryWrapper(queryDTO, null));


        pageObj.setRecords((List<HrsfCalibrationBaseTaskVO>) pageObj.getRecords().stream()
                .map(p -> {
                    String processInstanceId = ((HrsfCalibrationBase) p).getProcessInstanceId();
                    HrsfCalibrationBaseTaskVO baseTaskVO = HrsfPmsConvert.INSTANCE.toHrsfCalibrationBaseTaskVO((HrsfCalibrationBase) p);
                    String currentHandler = getCurrentHandlerStr(processInstanceId);
                    baseTaskVO.setCurrentHandler(currentHandler);
                    return baseTaskVO;
                }).collect(Collectors.toList()));
        return pageObj;
    }

    @Override
    public List<String> getCalibrationSelect() {
        List<HrsfCalibrationBase> list = list(Wrappers.<HrsfCalibrationBase>query().select("DISTINCT ORGANIZATION").lambda()
                .isNotNull(HrsfCalibrationBase::getOrganization));
        if (CollUtil.isNotEmpty(list)) {
            Comparator<Object> com = Collator.getInstance(java.util.Locale.CHINA);
            list.sort((o1, o2) -> com.compare(o1.getOrganization(), o2.getOrganization()));
            return list.stream().map(HrsfCalibrationBase::getOrganization).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public R<ErrorMessageVo> updateCalibration(List<Long> userBaseIdList, Long calibrationId) {
        /**
         * 先去同步员工的最终得分、业绩等级
         * 这里的failList是那些失败不满足条件的ID
         */
        log.info("用户点击更新按钮:用户idList{},calibrationId:{}", userBaseIdList, calibrationId);
        HrsfCalibrationBase calibrationBase = getById(calibrationId);
        return remoteUserTimeService.syncScoreBySF(new CalibrationBaseAndIdDTO(calibrationBase, userBaseIdList), SecurityConstants.FROM_IN);
    }

    @Override
    public R<ErrorMessageVo> updateOverviewCalibration(List<OverviewCalibrationDTO> overviewCalibrationDTOS) {
        List<ErrorMessageSubVo> messageSubVoList = new ArrayList<>();
        AtomicReference<Integer> successList = new AtomicReference<>(0);
        if (CollUtil.isNotEmpty(overviewCalibrationDTOS)) {
            List<Long> calibrationBaseIds = overviewCalibrationDTOS.stream().filter(e -> null != e.getCalibrationBaseId()).map(OverviewCalibrationDTO::getCalibrationBaseId).distinct().collect(Collectors.toList());
            Map<Long, List<Long>> collect = overviewCalibrationDTOS.stream()
                    .filter(e -> null != e.getCalibrationBaseId())
                    .collect(Collectors.groupingBy(OverviewCalibrationDTO::getCalibrationBaseId, Collectors.mapping(OverviewCalibrationDTO::getId, Collectors.toList())));
            calibrationBaseIds.forEach(o -> {
                List<Long> longs = collect.get(o);
                R<ErrorMessageVo> errorMessageVoR = updateCalibration(longs, o);
                if (errorMessageVoR.getCode() == 0) {
                    ErrorMessageVo data = errorMessageVoR.getData();
                    successList.set(successList.get() + data.getSuccessNum());
                    messageSubVoList.addAll(data.getErrorMessage());
                }
            });
        }
        return R.ok(new ErrorMessageVo(successList.get(), null, messageSubVoList));
    }

    /**
     * 冻结已经过截止日期的校准会议
     *
     * @param pmStartList
     */
    @Override
    public void freezeCalibrationBase(List<HrsfPmstart> pmStartList) {

        pmStartList.stream().forEach(data -> {
            List<HrsfCalibrationBase> calibrationBaseList = list(Wrappers.<HrsfCalibrationBase>lambdaQuery()
                    .eq(HrsfCalibrationBase::getAssesYear, data.getAssesYear())
                    .eq(HrsfCalibrationBase::getAssesType, data.getAssesType())
                    .eq(HrsfCalibrationBase::getStatus, CalibrationStatusEnum.GONING.getType())
                    .ne(HrsfCalibrationBase::getTemplate, CalibrationTemplateEnum.THREE.getType())
                    .in(HrsfCalibrationBase::getProcessStep, Arrays.asList(ProcessStepEnum.HRBP.getType(), ProcessStepEnum.OWNER.getType()))
            );
            updateBatchById(calibrationBaseList.stream().map(calibrationBase ->
                    calibrationBase.setStatus(CalibrationStatusEnum.FREEZE.getType())).collect(Collectors.toList()));
        });

    }

    @Override
    public R<ErrorMessageVo> returnCalibration(List<Long> userBaseIdList) {
        return remoteUserTimeService.returnCalibration(userBaseIdList, SecurityConstants.FROM_IN);

    }

    @Override
    public HrsfCalibrationBaseDTO getCalibration(Long id) {
        HrsfCalibrationBase calibrationBase = getById(id);
        HrsfCalibrationBaseDTO baseDTO = HrsfPmsConvert.INSTANCE.toHrsfCalibrationBaseDTO(calibrationBase);
        if (CalibrationStatusEnum.FREEZE.getType().equals(calibrationBase.getStatus())) {
            baseDTO.setOutOfDateFlag(true);
        } else {
            baseDTO.setOutOfDateFlag(false);
        }
        return baseDTO;
    }

    /**
     * 提交表单到下一个步骤
     *
     * @param taskId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ErrorMessageVo submit(List<String> taskId) {
        ErrorMessageVo errorMessageVo = new ErrorMessageVo();
        List<ErrorMessageSubVo> errorMessageSubVoList = new ArrayList<>();
        taskId.stream().forEach(task -> {
            try {
                ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                String processInstanceId = bpmTaskService.getProcessInstanceIdByTaskId(SecurityUtils.getUser().getId(), task);
                HrsfBpmProcessInstanceExt hrsfBpmProcessInstanceExt = processInstanceExtService.getOne(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                        .eq(HrsfBpmProcessInstanceExt::getProcessInstanceId, processInstanceId));
                Long baseId = hrsfBpmProcessInstanceExt.getCalibrationBaseId();
                HrsfCalibrationBase calibrationBase = getById(baseId);
                //只有这几个节点提交时校验分数
                if (ProcessStepEnum.HR_MANAGER.getType().equals(calibrationBase.getProcessStep())
                        || ProcessStepEnum.HRL4.getType().equals(calibrationBase.getProcessStep())
                        || ProcessStepEnum.HRL3.getType().equals(calibrationBase.getProcessStep())) {
                    R<List<HrsfPmsuserBase>> listR = remoteUserService.selectByBaseId(calibrationBase.getId(), SecurityConstants.FROM_IN);
                    if (listR.getCode() == 0) {
                        long count = listR.getData().stream().filter(obj -> (FormStatusEnum.In_Rating.getType().equals(obj.getFormStatus())
                                || FormStatusEnum.Rated.getType().equals(obj.getFormStatus()))).count();
                        if (count > 0) {
                            errorMessageSubVo.setStaffId(calibrationBase.getName());
                            errorMessageSubVo.setErrorMessage("发送失败，请检查表单状态；Calibration session failed to send, please check.");
                            errorMessageSubVoList.add(errorMessageSubVo);
                            return;
                        }
                    }
                }

                /**
                 * 提交审批流任务
                 * 返回下个节点的taskDefinitionKey
                 */
                String taskDefinitionKey = bpmTaskService.approveTask(SecurityUtils.getUser().getId()
                        , new BpmTaskApproveReqVO().setId(task).setComment(""), calibrationBase, baseId);
                if (baseId != null) {
                    /**
                     * 更新校准会议基础表信息(流程步骤、表单状态、代办状态等信息)
                     */
                    lambdaUpdate().set(HrsfCalibrationBase::getProcessStep,
                            ProcessStepEnum.getNextStep(taskDefinitionKey))
                            /**
                             * 校准会议状态是根据流程节点返回对应的状态
                             */
                            .set(HrsfCalibrationBase::getStatus, CalibrationStatusEnum.getNextStep(taskDefinitionKey, calibrationBase.getTemplate()))
                            .set(HrsfCalibrationBase::getProcessStatus, ProcessStatusEnum.FINISH.getStatus())
                            .set(HrsfCalibrationBase::getUpdateTime, LocalDateTime.now())
                            .eq(HrsfCalibrationBase::getId, baseId).update();

                    /**
                     * 更新校准会议员工表
                     * 根据流程节点返回对应的状态
                     */
                    String processStep = calibrationBase.getProcessStep();
                    String template = calibrationBase.getTemplate();
                    String formStatus = FormStatusEnum.getFormStatusByCurrentStep(taskDefinitionKey, calibrationBase.getTemplate());
                    remoteUserService.updateFormStatusByBaseId(formStatus, baseId, processStep, template, SecurityConstants.FROM_IN);
                }
            } catch (CheckedException e) {
                throw new CheckedException("操作失败,请联系管理员!");
            } catch (Exception e) {
                log.error("处理任务失败:{}", e);
                throw new CheckedException("网络异常，请稍后重试；Network anomaly, please try again later.");
            }
        });
        errorMessageVo.setErrorNum(errorMessageSubVoList.size());
        errorMessageVo.setSuccessNum(taskId.size() - errorMessageSubVoList.size());
        errorMessageVo.setErrorMessage(errorMessageSubVoList);
        return errorMessageVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendBack(List<String> taskId) {
        try {
            taskId.stream().forEach(task -> {
                /**
                 * 退回到绩效协调员节点
                 * 返回下个节点的taskDefinitionKey
                 */
                String processInstanceId = bpmTaskService.getProcessInstanceIdByTaskId(SecurityUtils.getUser().getId(), task);
                HrsfBpmProcessInstanceExt hrsfBpmProcessInstanceExt = processInstanceExtService.getOne(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                        .eq(HrsfBpmProcessInstanceExt::getProcessInstanceId, processInstanceId));
                Long baseId = hrsfBpmProcessInstanceExt.getCalibrationBaseId();
                HrsfCalibrationBase calibrationBase = getById(baseId);
                String taskDefinitionKey = bpmTaskService.rejectTask(SecurityUtils.getUser().getId()
                        , new BpmTaskRejectReqVO().setId(task).setComment(""), baseId, calibrationBase);

                /**
                 * 更新校准会议基础表信息(流程步骤、表单状态、代办状态等信息)
                 */
                lambdaUpdate().set(HrsfCalibrationBase::getProcessStep,
                        ProcessStepEnum.getPreStep(taskDefinitionKey))
                        .set(HrsfCalibrationBase::getProcessStatus, ProcessStatusEnum.FINISH.getStatus())
                        .eq(HrsfCalibrationBase::getId, baseId).update();
                /**
                 * 更新校准会议员工表
                 * 根据流程节点返回对应的状态
                 */
                String formStatus = FormStatusEnum.getPreStep(calibrationBase.getTemplate());
                remoteUserService.updateFormStatusByBaseId(formStatus, baseId, null, null, SecurityConstants.FROM_IN);

            });

        } catch (CheckedException e) {
            throw new CheckedException("操作失败,请联系管理员!");
        } catch (Exception e) {
            log.error("退回任务失败:{}", e);
            throw new ValidateCodeException("处理任务失败,请稍后再试!");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean sendBackLastStep(Long calibrationBaseId) {
        HrsfBpmProcessInstanceExt hrsfBpmProcessInstanceExt = processInstanceExtService.getOne(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                .eq(HrsfBpmProcessInstanceExt::getCalibrationBaseId, calibrationBaseId));
        HrsfCalibrationBase calibrationBase = getById(calibrationBaseId);
        if (ProcessStepEnum.HRBP.getType().equals(calibrationBase.getProcessStep())) {
            log.error("该校准会议状态为劳资员或者已完成节点,无法退回到上一步,ID为:{}", calibrationBaseId);
            throw new CheckedException("该校准会议审批节点为绩效协调员,无法退回到上一步");
        }
        if (ProcessStepEnum.END.getType().equals(calibrationBase.getProcessStep())) {
            log.error("该校准会议状态为劳资员或者已完成节点,无法退回到上一步,ID为:{}", calibrationBaseId);
            throw new CheckedException("该校准会议审批节点为已完成,无法退回到上一步!");
        }
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(hrsfBpmProcessInstanceExt.getProcessInstanceId()).list();
        Optional<Task> optionalTask = taskList.stream().filter(data -> StrUtil.isNotBlank(data.getTaskDefinitionKey())).findFirst();
        if (optionalTask.isPresent()) {
            String taskId = optionalTask.get().getId();
            try {
                /**
                 * 退回到绩效协调员节点
                 * 返回下个节点的taskDefinitionKey
                 */
                String taskDefinitionKey = bpmTaskService.rejectTaskLastStep(SecurityUtils.getUser().getId()
                        , new BpmTaskRejectReqVO().setId(taskId).setComment(""), calibrationBaseId, calibrationBase);
                /**
                 * 更新校准会议基础表信息(流程步骤、表单状态、代办状态等信息)
                 */
                lambdaUpdate().set(HrsfCalibrationBase::getProcessStep,
                        ProcessStepEnum.getPreStep(taskDefinitionKey))
                        .set(HrsfCalibrationBase::getProcessStatus, ProcessStatusEnum.FINISH.getStatus())
                        .eq(HrsfCalibrationBase::getId, calibrationBaseId).update();
                /**
                 * 更新校准会议员工表
                 * 根据流程节点返回对应的状态
                 */
                String formStatus = FormStatusEnum.getFormStatusByCurrentStep(taskDefinitionKey, calibrationBase.getTemplate());
                remoteUserService.updateFormStatusByBaseId(formStatus, calibrationBaseId, null, null, SecurityConstants.FROM_IN);
            } catch (Exception e) {
                log.error("退回到上一步失败:{}", e);
                throw new CheckedException("退回到上一步流程节点失败,请稍后再试!");
            }
        } else {
            log.error("根据流程实例ID:{},未找到对应的taskID", hrsfBpmProcessInstanceExt.getProcessInstanceId());
            throw new CheckedException("退回到上一步流程节点失败,未找到对应的任务ID,请稍后再试!");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addApprove(String taskId, String userId) {
        try {
            String processInstanceId = bpmTaskService.getProcessInstanceIdByTaskId(SecurityUtils.getUser().getId(), taskId);
            HrsfBpmProcessInstanceExt hrsfBpmProcessInstanceExt = processInstanceExtService.getOne(Wrappers.<HrsfBpmProcessInstanceExt>lambdaQuery()
                    .eq(HrsfBpmProcessInstanceExt::getProcessInstanceId, processInstanceId));
            Long baseId = hrsfBpmProcessInstanceExt.getCalibrationBaseId();
            HrsfCalibrationBase calibrationBase = getById(baseId);
            taskService.delegateTask(taskId, userId);
            List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
            publisher.publishEvent(CalibrationEvent.of(link, "校准会议待办通知 Calibration Session Routing Notification", calibrationBase, taskList, "submit_template.html", PmsUpdateEventEnum.J));

        } catch (CheckedException e) {
            throw new CheckedException("操作失败,请联系管理员!");
        } catch (Exception e) {
            log.error("增加审批人失败:{}", e);
            throw new CheckedException("增加审批人失败,请联系管理员!");
        }

    }

    @Override
    public Boolean removeCalibrationById(Long id) {
        HrsfCalibrationBase byId = getById(id);
        if (Objects.nonNull(byId)) {
            if (Objects.equals(byId.getProcessStep(), ProcessStepEnum.END.getType())) {
                throw new CheckedException("已完成的校准会议不支持增加、删除、修改操作，请知悉！");
            }

            lambdaUpdate()
                    .set(HrsfCalibrationBase::getDelFlag, "1")
                    .eq(HrsfCalibrationBase::getId, id)
                    .update();

            R<List<HrsfPmsuserBase>> listR = remoteUserService.selectByBaseId(byId.getId(), SecurityConstants.FROM_IN);
            if (listR.getCode() == 0) {
                List<String> collect = listR.getData().stream().map(HrsfPmsuserBase::getStaffId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    AddCalibrationUserDTO addCalibrationUserDTO = new AddCalibrationUserDTO();
                    addCalibrationUserDTO.setCalibrationId(id);
                    addCalibrationUserDTO.setStaffIds(collect);
                    remoteUserService.removeCalibrationUser(addCalibrationUserDTO, SecurityConstants.FROM_IN);
                }
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addCalibration(HrsfCalibrationBaseDTO hrsfCalibrationBaseDTO) {

        if (StrUtil.isBlank(hrsfCalibrationBaseDTO.getName())) {
            throw new CheckedException("校准会议名称不能为空");
        }
        HrsfCalibrationBase base = new HrsfCalibrationBase();
        BeanUtils.copyProperties(hrsfCalibrationBaseDTO, base);
        if (StrUtil.isNotBlank(hrsfCalibrationBaseDTO.getTemplate())) {
            if (Objects.equals(hrsfCalibrationBaseDTO.getTemplate(), CalibrationTemplateEnum.THREE.getType())) {
                base.setProcessStep(ProcessStepEnum.HR_MANAGER.getType());
                /**
                 * 2022-07-25
                 * 如果是公司级校准会议则Organization默认赋值为全公司
                 */
                base.setOrganization("全公司");
            } else {
                base.setProcessStep(ProcessStepEnum.HRBP.getType());
            }
        }

        save(base);

        Map<String, Object> hashMap = new ConcurrentHashMap<>(16);
        HrsfLoginUser user = SecurityUtils.getUser();
        if (Objects.equals(hrsfCalibrationBaseDTO.getTemplate(), CalibrationTemplateEnum.ONE.getType())
                || Objects.equals(hrsfCalibrationBaseDTO.getTemplate(), CalibrationTemplateEnum.TWO.getType())) {
            if (CollUtil.isEmpty(hrsfCalibrationBaseDTO.getPmsStaff()) || CollUtil.isEmpty(hrsfCalibrationBaseDTO.getPmsPrincipal())) {
                throw new CheckedException("当前模板绩效协调员和校准负责人必填");
            }
            hashMap.put(BpmProcessParamEnum.TASK01.getResult(), hrsfCalibrationBaseDTO.getPmsStaff());
            hashMap.put(BpmProcessParamEnum.TASK02.getResult(), hrsfCalibrationBaseDTO.getPmsPrincipal());
            R<List<String>> hrUserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HR.getType(), SecurityConstants.FROM_IN);
            if (hrUserListR.getCode() == 0) {
                hashMap.put(BpmProcessParamEnum.TASK03.getResult(), hrUserListR.getData());
            }
            R<List<String>> hrL4UserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HRL4.getType(), SecurityConstants.FROM_IN);
            if (hrL4UserListR.getCode() == 0) {
                hashMap.put(BpmProcessParamEnum.TASK04.getResult(), hrL4UserListR.getData());
            }
            R<List<String>> hrL3UserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HRL3.getType(), SecurityConstants.FROM_IN);
            if (hrL3UserListR.getCode() == 0) {
                hashMap.put(BpmProcessParamEnum.TASK05.getResult(), hrL3UserListR.getData());
            }

            Optional<HrsfProcessDefinitionExt> optional = processDefinitionExtService.list(Wrappers.<HrsfProcessDefinitionExt>lambdaQuery()
                    .eq(HrsfProcessDefinitionExt::getType, BpmProcessDefinitionEnum.UNDER.getType())
                    .orderByDesc(HrsfProcessDefinitionExt::getUpdateTime)).stream().findFirst();


            if (optional.isPresent()) {
                HrsfProcessDefinitionExt definitionExt = optional.get();
                BpmProcessInstanceCreateReqVO reqVO = new BpmProcessInstanceCreateReqVO().setVariables(hashMap)
                        .setProcessDefinitionId(definitionExt.getProcessDefinitionId())
                        .setCalibrationBaseId(base.getId())
                        .setFullName(user.getFullName())
                        .setStaffId(user.getId());
                String processInstanceId = processInstanceService.createProcessInstance
                        (SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getId(), reqVO);

                lambdaUpdate().set(HrsfCalibrationBase::getProcessInstanceId, processInstanceId)
                        .eq(HrsfCalibrationBase::getId, base.getId()).update();
            }


        } else if (Objects.equals(hrsfCalibrationBaseDTO.getTemplate(), CalibrationTemplateEnum.THREE.getType())) {

            R<List<String>> hrUserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HR.getType(), SecurityConstants.FROM_IN);
            if (hrUserListR.getCode() == 0) {
                if (CollectionUtil.isEmpty(hrUserListR.getData())) {
                    throw new CheckedException("未找到对应的HR审批人,请联系管理员");
                }
                hashMap.put(BpmProcessParamEnum.TASK03.getResult(), hrUserListR.getData());
            }

            Optional<HrsfProcessDefinitionExt> optional = processDefinitionExtService.list(
                    Wrappers.<HrsfProcessDefinitionExt>lambdaQuery()
                            .eq(HrsfProcessDefinitionExt::getType, BpmProcessDefinitionEnum.HIGH.getType())
                            .orderByDesc(HrsfProcessDefinitionExt::getUpdateTime)).stream().findFirst();

            if (optional.isPresent()) {
                HrsfProcessDefinitionExt definitionExt = optional.get();
                BpmProcessInstanceCreateReqVO reqVO = new BpmProcessInstanceCreateReqVO().setVariables(hashMap)
                        .setProcessDefinitionId(definitionExt.getProcessDefinitionId())
                        .setCalibrationBaseId(base.getId())
                        .setFullName(user.getFullName())
                        .setStaffId(user.getId());

                String processInstanceId = processInstanceService.createProcessInstance
                        (SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getId(), reqVO);

                lambdaUpdate().set(HrsfCalibrationBase::getProcessInstanceId, processInstanceId)
                        .eq(HrsfCalibrationBase::getId, base.getId()).update();
            }
        }


        return null;
    }

    @Override
    public Boolean addCalibrationUser(AddCalibrationUserDTO addCalibrationUserDTO) {
        HrsfCalibrationBase hrsfCalibrationBase = getById(addCalibrationUserDTO.getCalibrationId());
        if (Objects.isNull(hrsfCalibrationBase)) {
            throw new CheckedException("No Data");
        }
        if (Objects.equals(hrsfCalibrationBase.getProcessStep(), ProcessStepEnum.END.getType())) {
            throw new CheckedException("已完成的校准会议不支持增加、删除、修改操作，请知悉！");
        }
        if (Objects.nonNull(hrsfCalibrationBase)) {
            if (!Objects.equals(hrsfCalibrationBase.getTemplate(), CalibrationTemplateEnum.THREE.getType())) {
                //判断若本校准会议的校准会议模板=校准会议模板1【蓝白领季度+蓝领年度】或 校准会议模板2【白领年度】，进一步判断
                R<List<HrsfPmsuserBase>> listR = remoteUserService.selectByStaffIdAndASSESS(addCalibrationUserDTO.getStaffIds(), hrsfCalibrationBase.getAssesYear(), hrsfCalibrationBase.getAssesType(), SecurityConstants.FROM_IN);
                if (listR.getCode() == 0) {
                    List<HrsfPmsuserBase> data = listR.getData().stream().filter(e -> Objects.equals(e.getLevelFlag(), LevelFlagEnum.IS_CHECK.getType()) && e.getCalibrationBaseId() != null).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(data)) {
                        List<String> collect = data.stream().map(HrsfPmsuserBase::getFullName).collect(Collectors.toList());
                        String join = StringUtils.join(collect, ",");
                        String str = StrUtil.format("员工【{}】在当前校准会议或其他校准会议中存在，请先将员工从校准会议中移除，再进行添加操作！", join);
                        throw new CheckedException(str);
                    }
                }
                Set<String> set = new HashSet<>(addCalibrationUserDTO.getStaffIds());


                remoteUserService.updateCalibrationBaseId(addCalibrationUserDTO.getCalibrationId(), hrsfCalibrationBase.getAssesType(), hrsfCalibrationBase.getAssesYear(), set, SecurityConstants.FROM_IN);
            } else if (Objects.equals(hrsfCalibrationBase.getTemplate(), CalibrationTemplateEnum.THREE.getType())) {
                R<List<HrsfPmsuserBase>> pmsUserList = remoteUserService.getPmsUserList(hrsfCalibrationBase.getAssesYear(), hrsfCalibrationBase.getAssesType(), SecurityConstants.FROM_IN);
                if (pmsUserList.getCode() == 0) {
                    List<HrsfPmsuserBase> addData = new ArrayList<>();
                    pmsUserList.getData().forEach(o -> {
                        /**
                         * 这里是复制数据,将非公司级校准会议的人员复制到公司级校准会议中
                         */
                        if (addCalibrationUserDTO.getStaffIds().contains(o.getStaffId())) {

                            if (!Objects.equals(o.getLevelFlag(), LevelFlagEnum.NO_CHECK.getType())) {
                                o.setId(null);
                            }
                            o.setLevelFlag(LevelFlagEnum.NO_CHECK.getType());
                            o.setAssessYear(hrsfCalibrationBase.getAssesYear());
                            o.setAssessType(hrsfCalibrationBase.getAssesType());
                            o.setCalibrationBaseId(hrsfCalibrationBase.getId());
                            addData.add(o);
                        }
                    });
                    R<Object> objectR = remoteUserService.saveCompanyLevelCalibration(addData, SecurityConstants.FROM_IN);
                    if (objectR.getCode() != 0) {
                        throw new CheckedException(objectR.getMsg());
                    }
                }
            }
            /**
             * 新增校准会议人员时
             * 同时新增filter表数据,用户查询校准会议信息列表数据
             */
            calibrationFilterService.saveBatch(addCalibrationUserDTO.getStaffIds().stream().map(data -> {
                HrsfCalibrationFilter calibrationFilter = new HrsfCalibrationFilter();
                calibrationFilter.setStaffId(data);
                calibrationFilter.setCalibrationBaseId(hrsfCalibrationBase.getId());
                calibrationFilter.setOrganization(hrsfCalibrationBase.getOrganization());
                calibrationFilter.setAssessYear(hrsfCalibrationBase.getAssesYear());
                calibrationFilter.setAssessType(hrsfCalibrationBase.getAssesType());
                calibrationFilter.setUserLevel(hrsfCalibrationBase.getUserLevel());
                return calibrationFilter;
            }).collect(Collectors.toList()));


        }

        return null;
    }

    @Override
    public Object removeCalibrationUser(AddCalibrationUserDTO addCalibrationUserDTO) {
        HrsfCalibrationBase hrsfCalibrationBase = getById(addCalibrationUserDTO.getCalibrationId());
        if (Objects.isNull(hrsfCalibrationBase)) {
            return null;
        }
        if (Objects.equals(hrsfCalibrationBase.getProcessStep(), ProcessStepEnum.END.getType())) {
            throw new CheckedException("已完成的校准会议不支持增加、删除、修改操作，请知悉！");
        }
        remoteUserService.removeCalibrationUser(addCalibrationUserDTO, SecurityConstants.FROM_IN);
        /**
         * 删除人员的时候,同时删除filter数据
         */
        hrsfCalibrationFilterMapper.deleteFilterByStaffIdAndBaseId(addCalibrationUserDTO.getStaffIds(), addCalibrationUserDTO.getCalibrationId());
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object dataPush(AddCalibrationUserDTO addCalibrationUserDTO) {
        R<List<HrsfPmsuserBase>> listR = remoteUserService.selectPmsUserBaseByStaffIdList(addCalibrationUserDTO, SecurityConstants.FROM_IN);
        if (listR.getCode() == 0) {
            List<HrsfPmsuserBase> data = listR.getData();
            if (CollUtil.isNotEmpty(data)) {
                HrsfPmsuserBase hrsfPmsuserBase = data.get(0);
                HrsfPmstart hrsfPmstart = hrsfPmstartService.getPmstartByYearAndType(hrsfPmsuserBase.getAssessYear(), hrsfPmsuserBase.getAssessType());
                data.forEach(o -> {
                    List<WriteDataBackDTO> writeDataBackDTOS = new ArrayList<>();
                    HrsfCalibrationBase calibrationBase = getById(o.getCalibrationBaseId());
                    //判断若员工分数来源=批量导入，不做处理；若员工分数来源=评分表，调取接口，回写业绩等级
                    if (!(Objects.equals(ProcessStepEnum.OWNER.getType(), calibrationBase.getProcessStep())
                            && Objects.equals(CalibrationTemplateEnum.ONE.getType(), calibrationBase.getProcessStep())
                            && Objects.equals(o.getScoreSource(), ScoreSourceEnum.SF.getType()))) {
                        WriteDataBackDTO writeDataBackDTO = new WriteDataBackDTO();
                        BeanUtil.copyProperties(o, writeDataBackDTO);

                        if (Objects.nonNull(hrsfPmstart)) {
                            writeDataBackDTO.setEndDate(hrsfPmstart.getEndDate());
                            writeDataBackDTO.setStartDate(hrsfPmstart.getStartDate());
                        }
                        writeDataBackDTO.setId(null);
                        writeDataBackDTOS.add(writeDataBackDTO);
                    }
                    if (CollUtil.isNotEmpty(writeDataBackDTOS)) {
                        publisher.publishEvent(CalibrationEvent.of(calibrationBase.getTemplate(), ProcessStepEnum.END.getType(), writeDataBackDTOS, PmsUpdateEventEnum.G));
                    }
                });
            }

        } else {
            throw new CheckedException(listR.getMsg());
        }
        return null;
    }

    /**
     * 校验用户权限
     *
     * @param userId
     * @param taskId
     * @return
     */
    @Override
    public Boolean checkTask(String userId, String taskId, String status) {
        try {
            bpmTaskService.checkTask(userId, taskId);
        } catch (Exception e) {
            //只有已办的情况下才校验
            if (status == null) {
                return true;
            }
            if (!"0".equals(status)) {
                try {
                    bpmTaskService.checkHistoricTask(userId, taskId);
                } catch (Exception exception) {
                    return false;
                }
            } else {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ErrorMessageSubVo> updatePmsUserBase(List<HrsfPmsuserBaseDTO> hrsfPmsuserBaseDTOList, String assessYear, String assessType, String userName, String fullName) {
        List<ErrorMessageSubVo> errorMessageList = new ArrayList<>();
        /**
         * 1、生成校准会议Filter表
         * 2、生成校准会议基础表
         * 3、开启审批流
         */
        handleCalibrationFilterUpdate(hrsfPmsuserBaseDTOList);
        //构造一个calibrationBaseId和staffID的map对象
        Map<Long, Set<String>> calibrationBaseMap = handleCalibrationBaseUpdate(hrsfPmsuserBaseDTOList, assessYear, assessType, errorMessageList);
        log.info("生成的map对象信息为:{}", JSONUtil.toJsonStr(calibrationBaseMap));
        //如果calibrationBaseMap为空则表示,修改的绩效员工不需要启动审批流程
        if (MapUtil.isNotEmpty(calibrationBaseMap)) {
            publisher.publishEvent(CalibrationEvent.of(calibrationBaseMap
                    , PmsUpdateEventEnum.K, userName
                    , fullName, hrsfPmsuserBaseDTOList));
        }

        return errorMessageList;
    }


    public Map<Long, Set<String>> handleCalibrationBaseUpdate(List<HrsfPmsuserBaseDTO> baseDTOList, String assesYear, String assesType, List<ErrorMessageSubVo> errorMessageList) {
        Map<Long, Set<String>> calibrationBaseMap = new ConcurrentHashMap<>(32);
        List<String> staffIdList = baseDTOList.stream().map(HrsfPmsuserBaseDTO::getStaffId).collect(Collectors.toList());
        //判断将职级、是否为助理、是否为SYL4以及机构属性均相同的员工生成一组
        List<HrsfCalibrationFilter> filterList = calibrationFilterService.list(Wrappers.<HrsfCalibrationFilter>lambdaQuery()
                .isNotNull(HrsfCalibrationFilter::getOrganization)
                .eq(HrsfCalibrationFilter::getAssessYear, assesYear)
                .eq(HrsfCalibrationFilter::getAssessType, assesType)
                .in(HrsfCalibrationFilter::getStaffId, staffIdList));
        Map<String, Set<String>> groupMap = filterList.stream().collect(Collectors.groupingBy(o ->
                StrUtil.join("###", Arrays.asList(o.getOrganization()
                        , o.getAssistant(), o.getSyl4(), o.getUserLevel())), Collectors.mapping(HrsfCalibrationFilter::getStaffId, Collectors.toSet())));

        //处理生成校准会议基础表
        groupMap.keySet().stream().forEach(obj -> {
            HrsfCalibrationBase base = new HrsfCalibrationBase();
            base.setAssesType(assesType);
            base.setAssesYear(assesYear);
            //o.getOrganization()_o.getAssistant()_o.getSyl4()_o.getUserLevel()
            List<String> split = StrUtil.split(obj, "###");
            if (CollectionUtil.isNotEmpty(split)) {
                base.setOrganization(StrUtil.isBlank(split.get(0)) ? null : split.get(0));
                base.setUserLevel(StrUtil.isBlank(split.get(3)) ? null : split.get(3));
            }
            //--todo 待定,模板内容待确认
            if (AssessTypeEnum.YEAR.getType().equals(assesType) && !UserLevelEnum.L7.name().equals(split.get(3))) {
                base.setTemplate(CalibrationTemplateEnum.TWO.getType());
            } else {
                base.setTemplate(CalibrationTemplateEnum.ONE.getType());
            }
            base.setName(StrUtil.format(CommonConstants.CONTENT, assesYear, assesType, split.get(3), split.get(0)));
            /**
             * 判断是否存在相同的filter
             */
            List<HrsfCalibrationBase> baseList = list(Wrappers.<HrsfCalibrationBase>lambdaQuery()
                    .eq(HrsfCalibrationBase::getAssesYear, assesYear)
                    .eq(HrsfCalibrationBase::getAssesType, assesType)
                    .eq(HrsfCalibrationBase::getOrganization, StrUtil.isBlank(split.get(0)) ? null : split.get(0))
                    .eq(HrsfCalibrationBase::getUserLevel, StrUtil.isBlank(split.get(3)) ? null : split.get(3))
                    .orderByAsc(HrsfCalibrationBase::getCreateTime)
            );

            Long baseId;
            if (CollectionUtil.isEmpty(baseList)) {
                //新增完基础表后,将校准会议基础表ID更新到Filter表中
                this.save(base);
                baseId = base.getId();
                calibrationBaseMap.put(baseId, groupMap.get(obj));
            } else {
                /**
                 * 2022-07-25
                 * 如果查出来有多条数据
                 */
                if (baseList.size() > 1) {
                    //说明这里有多条数据,需要提示给前端页面
                    groupMap.get(obj).stream().forEach(staffId -> errorMessageList.add(new ErrorMessageSubVo(staffId, "在当前考核周期中，员工所属的机构和职级下存在多个校准会议，系统已默认分配至其中一个校准会议，需要您前往校准会议总览进行二次确认！")));
                }
                HrsfCalibrationBase calibrationBase = baseList.get(0);
                baseId = calibrationBase.getId();
                if (CalibrationStatusEnum.END.getType().equals(calibrationBase.getStatus())) {
                    groupMap.get(obj).stream().forEach(staffId -> errorMessageList.add(new ErrorMessageSubVo(staffId, "该校准会议状态为已完成,不可添加!")));
                    return;
                }
            }
            calibrationFilterService.lambdaUpdate()
                    .set(HrsfCalibrationFilter::getCalibrationBaseId, baseId)
                    .eq(HrsfCalibrationFilter::getAssessType, assesType)
                    .eq(HrsfCalibrationFilter::getAssessYear, assesYear)
                    .in(HrsfCalibrationFilter::getStaffId, groupMap.get(obj)).update();
            //调用user服务更新calibrationBaseId
            remoteUserService.updateCalibrationBaseId(baseId, assesType, assesYear, groupMap.get(obj), SecurityConstants.FROM_IN);

        });
        return calibrationBaseMap;
    }

    public void handleCalibrationFilterUpdate(List<HrsfPmsuserBaseDTO> baseDTOList) {
        ArrayList<HrsfCalibrationFilter> entityList = new ArrayList<>();
        /**
         * 处理list的数据
         *
         * L3	取“系统”字段信息；若“系统”字段信息为空，写固定值“HR”；
         * L4	取“系统”字段信息；若“系统”字段信息为空，写固定值“HR”；
         * L5 L6  判断若“部门”字段有值，取“部门”字段信息；
         *   	判断若“部门”字段为空，判断若“系统”字段有值，取“系统”字段信息；
         *      若“系统”字段信息为空，写固定值“HR”；
         * L7	判断若“科室”字段有值，取“科室”字段信息；
         *      判断若“科室”字段为空，判断若“部门”字段有值，取“部门”字段信息；
         *      若“部门”字段信息为空，判断若“系统”字段有值，取“系统”字段信息；
         *      若“系统”字段信息为空，写固定值“HR”；
         */
        baseDTOList.stream().forEach(obj -> {
            HrsfCalibrationFilter filter = HrsfPmsConvert.INSTANCE.toHrsfCalibrationFilter(obj);
            if (UserLevelEnum.L3.name().equals(obj.getUserLevel())) {
                filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
            } else if (UserLevelEnum.L4.name().equals(obj.getUserLevel())) {
                filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
            } else if (UserLevelEnum.L5.name().equals(obj.getUserLevel()) || UserLevelEnum.L6.name().equals(obj.getUserLevel())) {
                if (StrUtil.isNotBlank(obj.getDepartment())) {
                    filter.setOrganization(obj.getDepartment());
                } else {
                    filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
                }
            } else if (UserLevelEnum.L7.name().equals(obj.getUserLevel())) {
                if (StrUtil.isNotBlank(obj.getSection())) {
                    filter.setOrganization(obj.getSection());
                } else {
                    if (StrUtil.isNotBlank(obj.getDepartment())) {
                        filter.setOrganization(obj.getDepartment());
                    } else {
                        filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
                    }
                }
            }
            entityList.add(filter);
        });
        calibrationFilterService.saveBatch(entityList);
    }


    private LambdaQueryWrapper<HrsfCalibrationBase> buildQueryWrapper(CalibrationQueryDTO queryDTO, Set<String> processInstanceIds) {
        LambdaQueryWrapper<HrsfCalibrationBase> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(queryDTO.getAssesType())) {
            wrapper.eq(HrsfCalibrationBase::getAssesType, queryDTO.getAssesType());
        }
        if (StrUtil.isNotBlank(queryDTO.getAssesYear())) {
            wrapper.eq(HrsfCalibrationBase::getAssesYear, queryDTO.getAssesYear());
        }
        if (StrUtil.isNotBlank(queryDTO.getOrganization())) {
            wrapper.eq(HrsfCalibrationBase::getOrganization, queryDTO.getOrganization());
        }
        if (StrUtil.isNotBlank(queryDTO.getUserLevel())) {
            wrapper.eq(HrsfCalibrationBase::getUserLevel, queryDTO.getUserLevel());
        }
        if (StrUtil.isNotBlank(queryDTO.getProcessStep())) {
            wrapper.eq(HrsfCalibrationBase::getProcessStep, queryDTO.getProcessStep());
        }
        if (queryDTO.getStatus() != null) {
            wrapper.eq(HrsfCalibrationBase::getStatus, queryDTO.getStatus());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getCalibrationBaseId())) {
            wrapper.in(HrsfCalibrationBase::getId, queryDTO.getCalibrationBaseId());
        }
        if (CollectionUtil.isNotEmpty(processInstanceIds)) {
            wrapper.in(HrsfCalibrationBase::getProcessInstanceId, processInstanceIds);
        }
        if (StrUtil.isNotBlank(queryDTO.getName())) {
            wrapper.like(HrsfCalibrationBase::getName, queryDTO.getName());
        }
        /**
         * 如果是已办的查询,则不根据校准会议的更新时间排序
         */
        if (!ProcessStatusEnum.FINISH.getStatus().equals(queryDTO.getProcessStatus())) {
            wrapper.orderByDesc(HrsfCalibrationBase::getUpdateTime);
        }


        return wrapper;
    }
}
