package com.bbac.hrsf.performance.controller;

import com.bbac.hrsf.common.core.constant.DataFileConstant;
import com.bbac.hrsf.common.core.util.ppt.PptCreateUtil;
import com.bbac.hrsf.common.security.annotation.Inner;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.poi.xslf.usermodel.*;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.awt.geom.Rectangle2D;
import java.io.*;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.performance.controller.PPTController</li>
 * <li>CreateTime : 2022/04/21 13:13:31</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@RestController
@RequestMapping("/ppt")
@RequiredArgsConstructor
public class PptController {


    public static void main(String[] args) throws Exception{
        File file = ResourceUtils.getFile("classpath:test.pptx");
        InputStream fis = new FileInputStream(file);
        XMLSlideShow ppt = new XMLSlideShow(fis);
        System.out.println(ppt);
    }
    @GetMapping("/update")
    @ApiOperation(value = "修改模版")
    public void updatePPTFile(HttpServletResponse response) {
        try {

            File file = ResourceUtils.getFile("classpath:test.pptx");
            InputStream fis = new FileInputStream(file);
            XMLSlideShow ppt = new XMLSlideShow(fis);

            for (XSLFSlide slide : ppt.getSlides()) {

                // 获取每一张幻灯片中的shape
                for (XSLFShape shape : slide.getShapes()) {

                    Rectangle2D anchor = shape.getAnchor();

                    if (shape instanceof XSLFTextShape) {

                        XSLFTextShape txShape = (XSLFTextShape) shape;

                        if (txShape.getText().contains("{time}")) {

                            // 替换文字内容
                            txShape.setText(txShape.getText().replace(

                                    "{time}", "nihao"));

                        } else if (txShape.getText().contains("{a}")) {

                            // 替换文字内容
                            txShape.setText(txShape.getText().replace(

                                    "{a}", "a"));

                        }
                    } else if (shape instanceof XSLFPictureShape) {

                        XSLFPictureShape pShape = (XSLFPictureShape) shape;

                        XSLFPictureData pData = pShape.getPictureData();

                    } else if(shape instanceof XSLFGraphicFrame){
                        XSLFGraphicFrame gShape=(XSLFGraphicFrame) shape;
                        XSLFSheet sheet = shape.getSheet();
                        System.out.println(sheet);
                    }
                    else {

                        System.out.println("Process me: " + shape.getClass());

                    }

                }

            }

            //点击下载ppt
            String name = "DownloadData.pptx";
            response = PptCreateUtil.getServletResponse(response, DataFileConstant.PPTX, name);
            PptCreateUtil.pptWirteOut(ppt, response.getOutputStream());

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}