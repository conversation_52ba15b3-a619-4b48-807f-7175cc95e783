package com.bbac.hrsf.performance.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.dto.OverviewCalibrationDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.common.core.pojo.ErrorMessageSubVo;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.dto.*;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
public interface IHrsfCalibrationBaseService extends IService<HrsfCalibrationBase> {

    IPage<HrsfCalibrationBaseTaskVO> getCalibrationPage(CalibrationQueryDTO queryDTO);

    IPage<HrsfCalibrationBaseTaskVO> getCalibrationBasePage(CalibrationQueryDTO page);


    List<String> getCalibrationSelect();

    R<ErrorMessageVo> updateCalibration(List<Long> userBaseIdList, Long calibrationId);

    R<ErrorMessageVo> returnCalibration(List<Long> userBaseIdList);

    HrsfCalibrationBaseDTO getCalibration(Long id);

    ErrorMessageVo submit(List<String> taskId);

    Boolean sendBack(List<String> taskId);

    Boolean removeCalibrationById(Long id);

    Boolean addCalibration(HrsfCalibrationBaseDTO hrsfCalibrationBaseDTO);

    Boolean addCalibrationUser(AddCalibrationUserDTO addCalibrationUserDTO);

    Object removeCalibrationUser(AddCalibrationUserDTO addCalibrationUserDTO);

    Object dataPush(AddCalibrationUserDTO addCalibrationUserDTO);


    Boolean checkTask(String userId, String taskId,String status);

    List<ErrorMessageSubVo> updatePmsUserBase(List<HrsfPmsuserBaseDTO> hrsfPmsuserBaseDTOList, String assessYear
            , String assessType, String userName, String fullName);

    R<ErrorMessageVo> updateOverviewCalibration(List<OverviewCalibrationDTO> overviewCalibrationDTOS);

    void freezeCalibrationBase(List<HrsfPmstart> pmStartList);

    Boolean sendBackLastStep(Long calibrationBaseIdList);

    void addApprove(String taskId, String userId);
}
