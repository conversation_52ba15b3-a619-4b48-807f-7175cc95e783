package com.bbac.hrsf.performance.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationFilter;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
public interface IHrsfCalibrationFilterService extends IService<HrsfCalibrationFilter> {

    Boolean deletFilterByDto(List<String> staffIds, String assessYear, String assessType);
}
