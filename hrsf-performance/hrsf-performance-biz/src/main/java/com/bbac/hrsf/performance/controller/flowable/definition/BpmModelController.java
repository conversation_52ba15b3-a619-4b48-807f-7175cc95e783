package com.bbac.hrsf.performance.controller.flowable.definition;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.pojo.CommonResult;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.core.util.io.IoUtils;
import com.bbac.hrsf.performance.api.flowable.definition.vo.model.*;
import com.bbac.hrsf.performance.convert.definition.BpmModelConvert;
import com.bbac.hrsf.performance.service.definition.BpmModelService;
import io.swagger.annotations.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;


@Api(tags = "管理后台 - 流程模型")
@RestController
@RequestMapping("/bpm/model")
@Validated
public class BpmModelController {

    @Resource
    private BpmModelService modelService;

    @GetMapping("/page")
    @ApiOperation(value = "获得模型分页")
    public R<IPage<BpmModelPageItemRespVO>> getModelPage(BpmModelPageReqVO pageVO) {
        return R.ok(modelService.getModelPage(pageVO));
    }

    @GetMapping("/get")
    @ApiOperation("获得模型")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = String.class)
    public R<BpmModelRespVO> getModel(@RequestParam("id") String id) {
        BpmModelRespVO model = modelService.getModel(id);
        return R.ok(model);
    }

    @PostMapping("/create")
    @ApiOperation(value = "新建模型")
    public R<String> createModel(@Valid @RequestBody BpmModelCreateReqVO createRetVO) {
        return R.ok(modelService.createModel(createRetVO, null));
    }

    @PutMapping("/update")
    @ApiOperation(value = "修改模型")
    public R<Boolean> updateModel(@Valid @RequestBody BpmModelUpdateReqVO modelVO) {
        modelService.updateModel(modelVO);
        return R.ok(true);
    }

    @PostMapping("/import")
    @ApiOperation(value = "导入模型")
    public R<String> importModel(@Valid BpmModeImportReqVO importReqVO) throws IOException {
        BpmModelCreateReqVO createReqVO = BpmModelConvert.INSTANCE.convert(importReqVO);
        // 读取文件
        String bpmnXml = IoUtils.readUtf8(importReqVO.getBpmnFile().getInputStream(), false);
        return R.ok(modelService.createModel(createReqVO, bpmnXml));
    }

    @PostMapping("/deploy")
    @ApiOperation(value = "部署模型")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = String.class)
    public R<Boolean> deployModel(@RequestParam("id") String id) {
        modelService.deployModel(id);
        return R.ok(true);
    }

    @PutMapping("/update-state")
    @ApiOperation(value = "修改模型的状态", notes = "实际更新的部署的流程定义的状态")
    public R<Boolean> updateModelState(@Valid @RequestBody BpmModelUpdateStateReqVO reqVO) {
        modelService.updateModelState(reqVO.getId(), reqVO.getState());
        return R.ok(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除模型")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = String.class)
    public R<Boolean> deleteModel(@RequestParam("id") String id) {
        modelService.deleteModel(id);
        return R.ok(true);
    }
}
