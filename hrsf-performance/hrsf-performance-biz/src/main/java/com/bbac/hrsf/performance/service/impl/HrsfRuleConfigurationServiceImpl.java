package com.bbac.hrsf.performance.service.impl;


import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.performance.api.entity.HrsfRuleConfiguration;
import com.bbac.hrsf.performance.mapper.HrsfRuleConfigurationMapper;
import com.bbac.hrsf.performance.service.IHrsfRuleConfigurationService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
@Service
public class HrsfRuleConfigurationServiceImpl extends ServiceImpl<HrsfRuleConfigurationMapper, HrsfRuleConfiguration> implements IHrsfRuleConfigurationService {

}
