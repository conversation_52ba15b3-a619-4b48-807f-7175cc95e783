package com.bbac.hrsf.performance.controller;

import com.bbac.hrsf.common.core.constant.CacheConstants;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.annotation.Inner;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import com.bbac.hrsf.performance.service.mail.EmailTriggerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.web.bind.annotation.*;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.performance.controller.MailControllller</li>
 * <li>CreateTime : 2022/04/06 14:14:47</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Api(tags = "邮件发送")
@RestController
@RequestMapping("/bpm/model")
@RequiredArgsConstructor
public class MailController {

    private final EmailTriggerService emailTriggerService;

    private final CacheManager cacheManager;

    @GetMapping("/mail")
    @ApiOperation(value = "获得模型分页")
    public R<Boolean> getModelPage() {
        emailTriggerService.triggerEmail("mail_template.html");
        return R.ok();
    }

    @PostMapping("/sendEmailByInfo")
    @ApiOperation(value = "发送邮件方法")
    @Inner
    public R<Boolean> sendEmailByInfo(@RequestBody EmailInfoDTO emailInfoDTO) {
        emailTriggerService.sendEmailByInfo(emailInfoDTO);
        return R.ok();
    }

    @GetMapping("/hello")
    @ApiOperation(value = "获得模型分页")
    public R<String> getTest() {
        return R.ok("nihao");
    }


    @GetMapping("/deleteCache/{cacheName}")
    @ApiOperation(value = "清空用户信息缓存")
    public R<Boolean> deleteCache(@PathVariable String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        cache.clear();
        return R.ok();
    }
}