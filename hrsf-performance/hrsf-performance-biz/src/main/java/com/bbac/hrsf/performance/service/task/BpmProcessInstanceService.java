package com.bbac.hrsf.performance.service.task;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.util.CollectionUtils;
import com.bbac.hrsf.performance.api.flowable.definition.vo.BpmProcessInstanceCreateReqDTO;
import com.bbac.hrsf.performance.api.flowable.task.vo.instance.*;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;

import javax.validation.Valid;
import java.util.*;

/**
 * 流程实例 Service 接口
 *
 * <AUTHOR>
 */
public interface BpmProcessInstanceService {

    /**
     * 获得流程实例
     *
     * @param id 流程实例的编号
     * @return 流程实例
     */
    ProcessInstance getProcessInstance(String id);

    /**
     * 获得流程实例列表
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表
     */
    List<ProcessInstance> getProcessInstances(Set<String> ids);

    /**
     * 获得流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 流程实例列表 Map
     */
    default Map<String, ProcessInstance> getProcessInstanceMap(Set<String> ids) {
        return CollectionUtils.convertMap(getProcessInstances(ids), ProcessInstance::getProcessInstanceId);
    }

    /**
     * 获得流程实例的分页
     *
     * @param userId    用户编号
     * @param pageReqVO 分页请求
     * @return 流程实例的分页
     */
    IPage<BpmProcessInstancePageItemRespVO> getMyProcessInstancePage(String userId,
                                                                     @Valid BpmProcessInstanceMyPageReqVO pageReqVO);

    /**
     * 创建流程实例（提供给前端）
     *
     * @param userId      用户编号
     * @param createReqVO 创建信息
     * @return 实例的编号
     */
    String createProcessInstance(String userId, @Valid BpmProcessInstanceCreateReqVO createReqVO);

    /**
     * 创建流程实例（提供给内部）
     *
     * @param userId       用户编号
     * @param createReqDTO 创建信息
     * @return 实例的编号
     */
    String createProcessInstance(Long userId, @Valid BpmProcessInstanceCreateReqDTO createReqDTO);

    /**
     * 获得流程实例 VO 信息
     *
     * @param id 流程实例的编号
     * @return 流程实例
     */
    BpmProcessInstanceRespVO getProcessInstanceVO(String id);

    /**
     * 取消流程实例
     *
     * @param userId      用户编号
     * @param cancelReqVO 取消信息
     */
    void cancelProcessInstance(String userId, @Valid BpmProcessInstanceCancelReqVO cancelReqVO);


    /**
     * 获得历史的流程实例列表
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表
     */
    List<HistoricProcessInstance> getHistoricProcessInstances(Set<String> ids);

    /**
     * 获得历史的流程实例 Map
     *
     * @param ids 流程实例的编号集合
     * @return 历史的流程实例列表 Map
     */
    default Map<String, HistoricProcessInstance> getHistoricProcessInstanceMap(Set<String> ids) {
        return CollectionUtils.convertMap(getHistoricProcessInstances(ids), HistoricProcessInstance::getId);
    }

    /**
     * 更新 ProcessInstance 拓展记录为不通过
     *
     * @param id      流程编号
     * @param comment 理由。例如说，审批不通过时，需要传递该值
     */
    void updateProcessInstanceExtReject(String id, String comment, Task task);

    /**
     * 更新 ProcessInstance 拓展记录为不通过
     *
     * @param id      流程编号
     * @param comment 理由。例如说，审批不通过时，需要传递该值
     */
    void updateProcessInstanceExtRejectLastStep(String id, String comment, Task task);


}
