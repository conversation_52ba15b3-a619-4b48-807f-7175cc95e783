package com.bbac.hrsf.performance.service.task;

import com.bbac.hrsf.performance.api.flowable.task.vo.activity.BpmActivityRespVO;

import java.util.List;

/**
 * BPM 活动实例 Service 接口
 *
 * <AUTHOR>
 */
public interface BpmActivityService {

    /**
     * 获得指定流程实例的活动实例列表
     *
     * @param processInstanceId 流程实例的编号
     * @return 活动实例列表
     */
    List<BpmActivityRespVO> getActivityListByProcessInstanceId(String processInstanceId);

}
