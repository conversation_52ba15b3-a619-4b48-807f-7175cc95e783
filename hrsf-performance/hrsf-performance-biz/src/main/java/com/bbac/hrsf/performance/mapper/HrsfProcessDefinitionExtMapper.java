package com.bbac.hrsf.performance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbac.hrsf.performance.api.entity.HrsfProcessDefinitionExt;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-12
 */
@Mapper
public interface HrsfProcessDefinitionExtMapper extends BaseMapper<HrsfProcessDefinitionExt> {
    default List<HrsfProcessDefinitionExt> selectListByProcessDefinitionIds(Collection<String> processDefinitionIds) {
        return selectList(Wrappers.<HrsfProcessDefinitionExt>lambdaQuery().in(HrsfProcessDefinitionExt::getProcessDefinitionId, processDefinitionIds));
    }

    default HrsfProcessDefinitionExt selectByProcessDefinitionId(String processDefinitionId) {
        return selectOne(Wrappers.<HrsfProcessDefinitionExt>lambdaQuery().eq(HrsfProcessDefinitionExt::getProcessDefinitionId, processDefinitionId));
    }

}
