package com.bbac.hrsf.performance.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.vo.HrsfPmstartVO;
import com.bbac.hrsf.performance.api.dto.HrsfPmaStartDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
public interface IHrsfPmstartService extends IService<HrsfPmstart> {

    /**
     * 开启绩效启动
     *
     * @param hrsfPmstart
     * @return
     */
    Long startPms(HrsfPmstart hrsfPmstart);

    HrsfPmstart getPmsStartInfo(HrsfPmaStartDTO hrsfPmaStartDTO);

    IPage<HrsfPmstartVO> getOverviewList(HrsfPmaStartDTO hrsfPmaStartDTO);

    HrsfPmstart getPmstartByYearAndType(String assesYear, String assesType);
}
