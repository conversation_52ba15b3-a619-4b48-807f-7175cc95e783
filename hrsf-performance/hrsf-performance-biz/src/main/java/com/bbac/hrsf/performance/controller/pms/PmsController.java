/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.controller.pms;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.dto.OverviewCalibrationDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.enums.CalibrationStatusEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.pojo.ErrorMessageSubVo;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.annotation.Inner;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import com.bbac.hrsf.performance.api.dto.CalibrationIdQueryDTO;
import com.bbac.hrsf.performance.api.dto.CalibrationQueryDTO;
import com.bbac.hrsf.performance.api.dto.HrsfCalibrationBaseDTO;
import com.bbac.hrsf.performance.api.dto.HrsfPmaStartDTO;
import com.bbac.hrsf.performance.api.dto.HrsfPmsuserBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.api.entity.HrsfRuleConfiguration;
import com.bbac.hrsf.performance.api.entity.HrsfTaskExt;
import com.bbac.hrsf.performance.api.vo.HrsfRuleConfigurationVO;
import com.bbac.hrsf.performance.convert.HrsfPmsConvert;
import com.bbac.hrsf.performance.service.IHrsfCalibrationBaseService;
import com.bbac.hrsf.performance.service.IHrsfCalibrationFilterService;
import com.bbac.hrsf.performance.service.IHrsfPmstartService;
import com.bbac.hrsf.performance.service.IHrsfRuleConfigurationService;
import com.bbac.hrsf.performance.service.task.IHrsfTaskExtService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.TaskService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
@RestController
@Slf4j
@RequiredArgsConstructor
@RequestMapping("/pms")
@Api(value = "pms", tags = "绩效管理模块")
public class PmsController {

    private final IHrsfPmstartService hrsfPmstartService;

    private final IHrsfCalibrationBaseService hrsfCalibrationBaseService;

    private final IHrsfTaskExtService hrsfTaskExtService;

    private final TaskService taskService;

    private final IHrsfCalibrationFilterService iHrsfCalibrationFilterService;

    private final IHrsfRuleConfigurationService iHrsfRuleConfigurationService;

    /**
     * 启动绩效考核步骤
     *
     * @return R
     */
    @PostMapping("/startPms")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Long> startPms(@RequestBody HrsfPmstart hrsfPmstart) {
        Long id = hrsfPmstartService.startPms(hrsfPmstart);
        if (id == null) {
            throw new CheckedException("已经存在相同考核年度和考核类型的数据,请重新选择!");
        }

        /**
         * 如果截止日期大于当前日期
         * 则把校准会议里处于冻结状态的更新成进行中
         */
        if (hrsfPmstart.getFinishDate().isAfter(LocalDate.now())) {
            List<HrsfCalibrationBase> calibrationBaseList = hrsfCalibrationBaseService.list(Wrappers.<HrsfCalibrationBase>lambdaQuery()
                    .eq(HrsfCalibrationBase::getAssesYear, hrsfPmstart.getAssesYear())
                    .eq(HrsfCalibrationBase::getAssesType, hrsfPmstart.getAssesType())
                    .eq(HrsfCalibrationBase::getStatus, CalibrationStatusEnum.FREEZE.getType()));
            hrsfCalibrationBaseService.updateBatchById(calibrationBaseList.stream().map(calibrationBase ->
                    calibrationBase.setStatus(CalibrationStatusEnum.GONING.getType())).collect(Collectors.toList()));
        }
        return R.ok(id);
    }

    /**
     * 获取启动绩效信息
     *
     * @return R
     */
    @PostMapping("/getPmsStartInfo")
    public R<HrsfPmstart> getPmsStartInfo(@RequestBody HrsfPmaStartDTO hrsfPmaStartDTO) {

        return R.ok(hrsfPmstartService.getPmsStartInfo(hrsfPmaStartDTO));
    }

    /**
     * 校准会议收件箱-获取分页数据
     *
     * @return R
     */
    @PostMapping("/getCalibrationPage")
    public R<IPage<HrsfCalibrationBaseTaskVO>> getCalibrationPage(@RequestBody CalibrationQueryDTO queryDTO) {
        return R.ok(hrsfCalibrationBaseService.getCalibrationPage(queryDTO));
    }

    /**
     * 校准会议收件箱-获取校准会议详情
     *
     * @return R
     */
    @GetMapping("/getCalibration/{id}")
    public R<HrsfCalibrationBaseDTO> getCalibration(@PathVariable("id") Long id) {
        return R.ok(hrsfCalibrationBaseService.getCalibration(id));
    }

    /**
     * 校准会议收件箱-获取校准会议详情(内部调用)
     *
     * @return R
     */
    @GetMapping("/getCalibrationInner/{id}")
    @Inner
    public R<HrsfCalibrationBaseDTO> getCalibrationInner(@PathVariable("id") Long id) {
        return R.ok(hrsfCalibrationBaseService.getCalibration(id));
    }

    /**
     * 校准会议收件箱-获取校准会议详情
     *
     * @return R
     */
    @GetMapping("/getPmsStart")
    public R<HrsfPmstart> getCalibration(@RequestParam("assessYear") String assessYear, @RequestParam("assessType") String assessType) {
        return R.ok(hrsfPmstartService.getOne(Wrappers.<HrsfPmstart>lambdaQuery().eq(HrsfPmstart::getAssesYear, assessYear)
                .eq(HrsfPmstart::getAssesType, assessType)));
    }

    /**
     * 校准会议收件箱-获取校准会议详情(内部调用)
     *
     * @return R
     */
    @GetMapping("/getPmsStartInner")
    @Inner
    public R<HrsfPmstart> getCalibrationInner(@RequestParam("assessYear") String assessYear, @RequestParam("assessType") String assessType) {
        return R.ok(hrsfPmstartService.getOne(Wrappers.<HrsfPmstart>lambdaQuery().eq(HrsfPmstart::getAssesYear, assessYear)
                .eq(HrsfPmstart::getAssesType, assessType)));
    }

    /**
     * 校准会议收件箱-获取机构下拉框信息
     *
     * @return R
     */
    @PostMapping("/getCalibrationSelect")
    public R<List<String>> getCalibrationSelect() {
        return R.ok(hrsfCalibrationBaseService.getCalibrationSelect());
    }

    /**
     * 校准会议收件箱详情-更新操作
     *
     * @return R
     */
    @PostMapping("/updateCalibration/{calibrationId}")
    public R<ErrorMessageVo> updateCalibration(@RequestBody List<Long> userBaseIdList, @PathVariable("calibrationId") Long calibrationId) {
        return hrsfCalibrationBaseService.updateCalibration(userBaseIdList, calibrationId);
    }

    @PostMapping("/updateOverviewCalibration")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<ErrorMessageVo> updateOverviewCalibration(@RequestBody List<OverviewCalibrationDTO> overviewCalibrationDTOS) {
        return hrsfCalibrationBaseService.updateOverviewCalibration(overviewCalibrationDTOS);
    }


    /**
     * 校准会议收件箱详情-退回操作
     *
     * @return R
     */
    @PostMapping("/returnCalibration")
    public R<ErrorMessageVo> returnCalibration(@RequestBody List<Long> userBaseIdList) {
        return hrsfCalibrationBaseService.returnCalibration(userBaseIdList);
    }

    /**
     * 校准会议收件箱详情-退回操作
     *
     * @return R
     */
    @PostMapping("/overview/returnCalibration")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<ErrorMessageVo> returnCalibrationOverview(@RequestBody List<Long> userBaseIdList) {
        return hrsfCalibrationBaseService.returnCalibration(userBaseIdList);
    }


    /**
     * 校准会议收件箱-流程日志
     *
     * @return R
     */
    @GetMapping("/getProcessStepPage/{calibrationBaseId}")
    public R<IPage<HrsfTaskExt>> getProcessStepPage(Page page, @PathVariable Long calibrationBaseId, @RequestParam(value = "taskId", required = false) String taskId
            , @RequestParam(value = "sign", required = false) String sign, @RequestParam(value = "processStatus",required = false)String processStatus, HttpServletResponse response) {
        if (StrUtil.isNotBlank(sign) && CommonConstants.MEETING_INBOX.equals(sign)) {
            if (StrUtil.isNotBlank(taskId) && hrsfCalibrationBaseService.checkTask(SecurityUtils.getUser().getUsername(), taskId,processStatus)) {
                return R.ok(hrsfTaskExtService.getProcessStepPage(page, calibrationBaseId));
            }
            return R.restResult(null,CommonConstants.NO_PERMISSION,"该用户没有权限查看数据,请联系管理员");
        } else {
            return R.ok(hrsfTaskExtService.getProcessStepPage(page, calibrationBaseId));
        }

    }

    /**
     * 校准会议收件箱-流程日志返回流程节点
     *
     * @return R
     */
    @GetMapping("/getProcessStep/{calibrationBaseId}")
    public R<String> getProcessStep(@PathVariable Long calibrationBaseId, @RequestParam(value = "taskId", required = false) String taskId
            , @RequestParam(value = "sign", required = false) String sign,@RequestParam(value = "processStatus",required = false)String processStatus) {
        if (StrUtil.isNotBlank(sign) && CommonConstants.MEETING_INBOX.equals(sign)) {
            if (StrUtil.isNotBlank(taskId) && hrsfCalibrationBaseService.checkTask(SecurityUtils.getUser().getUsername(), taskId,processStatus)) {
                HrsfCalibrationBase calibrationBase = hrsfCalibrationBaseService.getById(calibrationBaseId);
                return R.ok(calibrationBase.getProcessStep());
            }
            return R.restResult(null,CommonConstants.NO_PERMISSION,"该用户没有权限查看数据,请联系管理员");
        } else {
            HrsfCalibrationBase calibrationBase = hrsfCalibrationBaseService.getById(calibrationBaseId);
            return R.ok(calibrationBase.getProcessStep());
        }
    }

    /**
     * 校准会议收件箱-流程日志返回流程节点
     *
     * @return R
     */
    @GetMapping("/getProcessStepOperation/{calibrationBaseId}")
    public R<Map<String, Object>> getProcessStepOperation(@PathVariable Long calibrationBaseId, @RequestParam(value = "taskId", required = false) String taskId
            , @RequestParam(value = "sign", required = false) String sign,@RequestParam(value = "processStatus",required = false)String processStatus) {
        HrsfCalibrationBase calibrationBase = hrsfCalibrationBaseService.getById(calibrationBaseId);
        if (StrUtil.isNotBlank(sign) && CommonConstants.MEETING_INBOX.equals(sign)) {
            if (StrUtil.isNotBlank(taskId) && hrsfCalibrationBaseService.checkTask(SecurityUtils.getUser().getUsername(), taskId,processStatus)) {
                return R.ok(hrsfTaskExtService.getProcessStepOperation(calibrationBase,processStatus));
            }
            return R.restResult(null,CommonConstants.NO_PERMISSION,"该用户没有权限查看数据,请联系管理员");
        } else {
            return R.ok(hrsfTaskExtService.getProcessStepOperation(calibrationBase,processStatus));
        }
    }


    /**
     * HR审批-修改绩效协调员、机构负责人的审批人
     *
     * @return R
     */
    @PostMapping("/updateApprove/{calibrationBaseId}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> updateApprove(@PathVariable Long calibrationBaseId, @RequestBody Map<String, Object> approveMap) {

        HrsfCalibrationBase calibrationBase = hrsfCalibrationBaseService.getById(calibrationBaseId);
        return R.ok(hrsfTaskExtService.updateApprove(calibrationBase, approveMap));
    }

    /**
     * 校准会议收件箱-提交表单
     *
     * @return
     */
    @PostMapping("/submit")
    public R<ErrorMessageVo> submit(@RequestBody List<String> taskId) {

            ErrorMessageVo errorMessageVo = hrsfCalibrationBaseService.submit(taskId);
            return R.ok(errorMessageVo);

    }

    /**
     * 校准会议收件箱-退回劳资员
     *
     * @return R
     */
    @PostMapping("/sendBack")
    public R<Boolean> sendBack(@RequestBody List<String> taskId) {
        return R.ok(hrsfCalibrationBaseService.sendBack(taskId));
    }

    /**
     * 校准会议收件箱-退回到上一步
     *
     * @return R
     */
    @PostMapping("/sendBackLastStep")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> sendBackLastStep(@RequestBody CalibrationIdQueryDTO calibrationIdQueryDTO) {
        return R.ok(hrsfCalibrationBaseService.sendBackLastStep(calibrationIdQueryDTO.getCalibrationBaseId()));
    }

    /**
     * 校准会议收件箱-增加审批人节点
     *
     * @return R
     */
    @GetMapping("/addApprove/{taskId}/{userId}")
    public R<Boolean> addApprove(@PathVariable String taskId, @PathVariable String userId) {
        hrsfCalibrationBaseService.addApprove(taskId,userId);
        return R.ok();
    }

    @PostMapping("/dataPush")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Object> dataPush(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(hrsfCalibrationBaseService.dataPush(addCalibrationUserDTO));
    }


    /**
     * 校验该taskId是否属于当前用户
     *
     * @return R
     */
    @PostMapping("/checkTaskById")
    @Inner
    Boolean checkTaskById(@RequestParam("taskId") String taskId,@RequestParam(value = "status",required = false) String status, @RequestParam("userId") String userId) {
        return hrsfCalibrationBaseService.checkTask(userId, taskId,status);
    }


    /**
     *
     *
     * @return R
     */
    @PostMapping("/updatePmsUserBase")
    @Inner
    List<ErrorMessageSubVo> updatePmsUserBase(@RequestBody List<HrsfPmsuserBaseDTO> hrsfPmsuserBaseDTOList
            , @RequestParam("assessYear") String assessYear, @RequestParam("assessType")
                                           String assessType, @RequestParam("userName") String userName, @RequestParam("fullName")
                                           String fullName) {
        return hrsfCalibrationBaseService.updatePmsUserBase(hrsfPmsuserBaseDTOList, assessYear, assessType
                , userName, fullName);
    }

    @PostMapping("/deleteFilter")
    @Inner
    public R<Boolean> deleteFilter(@RequestBody List<String> staffIds,
                                   @RequestParam("assessYear") String assessYear,
                                   @RequestParam("assessType") String assessType) {
        return R.ok(iHrsfCalibrationFilterService.deletFilterByDto(staffIds, assessYear, assessType));
    }


    @GetMapping("/freezeCalibrationBase")
    @Inner
    public void freezeCalibrationBase() {
        /**
         * 查出截止日期小于当前日期的数据
         */
        List<HrsfPmstart> pmStartList = hrsfPmstartService.list(Wrappers.<HrsfPmstart>query().lambda().le(HrsfPmstart::getFinishDate, LocalDate.now()));
        hrsfCalibrationBaseService.freezeCalibrationBase(pmStartList);
    }


    /**
     * 保存均分、等级规则变更
     *
     * @return R
     */
    @PostMapping("/saveRuleConfiguration")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> saveRuleConfiguration(@RequestBody List<HrsfRuleConfiguration> dataList) {
        log.info("saveRuleConfiguration|dataList={}", JSONUtil.toJsonStr(dataList));
        return R.ok(iHrsfRuleConfigurationService.saveOrUpdateBatch(dataList));
    }

    /**
     * 查询均分、等级规则
     *
     * @return R
     */
    @GetMapping("/ruleConfigurationList")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Map<String, List<HrsfRuleConfiguration>>> ruleConfigurationList() {
        return R.ok(iHrsfRuleConfigurationService.list().stream().collect(Collectors.groupingBy(HrsfRuleConfiguration::getGroupBy)));
    }

    /**
     * 查询均分、等级规则
     *
     * @return R
     */
    @GetMapping("/ruleConfigurationMap")
    public R<Map<String, HrsfRuleConfigurationVO>> ruleConfigurationMap() {

        return R.ok(iHrsfRuleConfigurationService.list().stream().collect(Collectors.toMap(HrsfRuleConfiguration::getType
                , data -> HrsfPmsConvert.INSTANCE.toHrsfRuleConfigurationVO(data))));
    }
}
