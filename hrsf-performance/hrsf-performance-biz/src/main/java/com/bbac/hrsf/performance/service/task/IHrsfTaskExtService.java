package com.bbac.hrsf.performance.service.task;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.performance.api.entity.HrsfTaskExt;

import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-25
 */
public interface IHrsfTaskExtService extends IService<HrsfTaskExt> {

    IPage<HrsfTaskExt> getProcessStepPage(Page page, Long calibrationBaseId);

    Map<String, Object> getProcessStepOperation(HrsfCalibrationBase hrsfCalibrationBase, String status);

    Boolean updateApprove(HrsfCalibrationBase hrsfCalibrationBase, Map<String, Object> approveMap);
}
