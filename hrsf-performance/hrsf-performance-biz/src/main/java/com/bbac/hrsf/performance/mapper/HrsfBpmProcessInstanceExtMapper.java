package com.bbac.hrsf.performance.mapper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.performance.api.entity.HrsfBpmProcessInstanceExt;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-21
 */
@Mapper
public interface HrsfBpmProcessInstanceExtMapper extends BaseMapper<HrsfBpmProcessInstanceExt> {


    default void updateByProcessInstanceId(HrsfBpmProcessInstanceExt updateObj) {
        update(updateObj, new QueryWrapper<HrsfBpmProcessInstanceExt>()
                .eq("process_instance_id", updateObj.getProcessInstanceId()));
    }
}
