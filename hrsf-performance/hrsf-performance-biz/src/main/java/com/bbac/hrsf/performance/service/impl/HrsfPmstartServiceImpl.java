package com.bbac.hrsf.performance.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.feign.RemoteUserService;
import com.bbac.hrsf.admin.api.feign.RemoteUserTimeService;
import com.bbac.hrsf.admin.api.vo.HrsfPmstartVO;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.HrsfPmaStartDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.event.CalibrationEvent;
import com.bbac.hrsf.performance.mapper.HrsfPmstartMapper;
import com.bbac.hrsf.performance.service.IHrsfPmstartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Service
@Slf4j
public class HrsfPmstartServiceImpl extends ServiceImpl<HrsfPmstartMapper, HrsfPmstart> implements IHrsfPmstartService {

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private RemoteUserTimeService remoteUserTimeService;

    @Resource
    private ApplicationEventPublisher publisher;

    private static final String CALIBRATION_NAME_TEMPLATE = "{}年{}考核 {} {} Review";


    /**
     * 开启绩效启动
     *
     * @param hrsfPmstart
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long startPms(HrsfPmstart hrsfPmstart) {

        /**
         * 1、新增绩效考核表
         * 2、新增员工绩效基础表
         */
        if (hrsfPmstart.getId() != null) {
            baseMapper.updateById(hrsfPmstart);
            /**
             *
             * 获取当前年份和类别并且绩效截止日期大于当前日期的数据
             * 然后件HrsfCalibrationBase表的状态更新为进行中
             *
             */

            return hrsfPmstart.getId();
        } else {
            //插入之前先判断一下时候存在相同的考核年度和考核类型
            HrsfPmstart pmstart = baseMapper.selectOne(Wrappers.<HrsfPmstart>lambdaQuery().eq(HrsfPmstart::getAssesType, hrsfPmstart.getAssesType())
                    .eq(HrsfPmstart::getAssesYear, hrsfPmstart.getAssesYear()));
            if (pmstart != null) {
                return null;
            }
            baseMapper.insert(hrsfPmstart);
        }
        /**
         * 启动绩效后，生成校准会议Filter和校准会议基础表
         */
        log.info("获取当前登录用户数据:{}", JSONUtil.toJsonStr(SecurityUtils.getUser()));
        publisher.publishEvent(CalibrationEvent.of(hrsfPmstart, hrsfPmstart.getAssesYear(), hrsfPmstart.getAssesType()
                , PmsUpdateEventEnum.E, SecurityUtils.getUser().getUsername(), SecurityUtils.getUser().getFullName()));
        return hrsfPmstart.getId();
    }

    @Override
    public HrsfPmstart getPmsStartInfo(HrsfPmaStartDTO hrsfPmaStartDTO) {
        return getOne(Wrappers.<HrsfPmstart>lambdaQuery()
                .eq(HrsfPmstart::getAssesType, hrsfPmaStartDTO.getAssesType())
                .eq(HrsfPmstart::getAssesYear, hrsfPmaStartDTO.getAssesYear()));
    }

    @Override
    public IPage<HrsfPmstartVO> getOverviewList(HrsfPmaStartDTO hrsfPmaStartDTO) {
        LambdaQueryWrapper<HrsfPmstart> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(hrsfPmaStartDTO.getAssesType())) {
            wrapper.eq(HrsfPmstart::getAssesType, hrsfPmaStartDTO.getAssesType());
        }
        if (StrUtil.isNotBlank(hrsfPmaStartDTO.getAssesYear())) {
            wrapper.eq(HrsfPmstart::getAssesYear, hrsfPmaStartDTO.getAssesYear());
        }
        wrapper.orderByDesc(HrsfPmstart::getCreateTime);
        Page pageObj = baseMapper.selectPage(hrsfPmaStartDTO, wrapper);


        List<HrsfPmstart> list = pageObj.getRecords();
        List<HrsfPmstartVO> hrsfPmstartVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            for (HrsfPmstart hrsfPmstart : list) {
                HrsfPmstartVO hrsfPmstartVO = new HrsfPmstartVO();
                BeanUtils.copyProperties(hrsfPmstart, hrsfPmstartVO);
                String descByType = AssessTypeEnum.getDescByType(hrsfPmstart.getAssesType());
                String descEnByType = AssessTypeEnum.getDescEnByType(hrsfPmstart.getAssesType());
                String overviewName = StrUtil.format(CALIBRATION_NAME_TEMPLATE, hrsfPmstart.getAssesYear()
                        , descByType, hrsfPmstart.getAssesYear(), descEnByType);
                hrsfPmstartVO.setOverviewName(overviewName);
                hrsfPmstartVOList.add(hrsfPmstartVO);
            }
        }
        pageObj.setRecords(hrsfPmstartVOList);
        return pageObj;
    }

    @Override
    public HrsfPmstart getPmstartByYearAndType(String assesYear, String assesType) {
        HrsfPmstart hrsfPmstart = getOne(Wrappers.<HrsfPmstart>query().lambda()
                .eq(HrsfPmstart::getAssesYear, assesYear)
                .eq(HrsfPmstart::getAssesType, assesType));
        return hrsfPmstart;
    }


}
