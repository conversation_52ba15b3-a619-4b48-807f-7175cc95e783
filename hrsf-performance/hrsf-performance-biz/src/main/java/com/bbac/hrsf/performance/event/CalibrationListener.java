/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.event;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONString;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationFilter;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.feign.RemoteUserService;
import com.bbac.hrsf.admin.api.feign.RemoteUserTimeService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessDefinitionEnum;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessParamEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.UserLevelEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import com.bbac.hrsf.performance.api.entity.HrsfProcessDefinitionExt;
import com.bbac.hrsf.performance.api.feign.RemoteEmailService;
import com.bbac.hrsf.performance.api.flowable.task.vo.instance.BpmProcessInstanceCreateReqVO;
import com.bbac.hrsf.performance.convert.HrsfPmsConvert;
import com.bbac.hrsf.performance.mapper.HrsfCalibrationFilterMapper;
import com.bbac.hrsf.performance.service.IHrsfCalibrationBaseService;
import com.bbac.hrsf.performance.service.IHrsfCalibrationFilterService;
import com.bbac.hrsf.performance.service.definition.HrsfProcessDefinitionExtService;
import com.bbac.hrsf.performance.service.task.BpmProcessInstanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 异步监听日志事件
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class CalibrationListener {


    private final IHrsfCalibrationFilterService hrsfCalibrationFilterService;

    private final HrsfCalibrationFilterMapper hrsfCalibrationFilterMapper;

    private final IHrsfCalibrationBaseService hrsfCalibrationBaseService;

    private final RemoteUserService remoteUserService;

    private final BpmProcessInstanceService processInstanceService;

    private final HrsfProcessDefinitionExtService processDefinitionExtService;

    private final RemoteEmailService remoteEmailService;

    private final RemoteUserTimeService remoteUserTimeService;

    @Value("${sf.sync.operation.emailHR}")
    private String operationEmailHR;


    @Async("batchTaskExecutor")
    @Order
    @TransactionalEventListener(CalibrationEvent.class)
    public void handlePmsUpdate(CalibrationEvent event) {
        switch (event.getEventEnum()) {
            case E:
                handlePmsUpdateEvent(event);
                break;
            case G:
                handlePmsUpdateEventG(event);
                break;
            case H:
                handlePmsUpdateEventH(event);
                break;
            case J:
                handlePmsUpdateEventJ(event);
                break;
            case K:
                handlePmsUpdateEventK(event);
                break;
            default:
                break;
        }
    }


    private void handlePmsUpdateEventK(CalibrationEvent event) {
        startFlowable(event.getCalibrationBaseMap(), event);
    }

    private void handlePmsUpdateEventJ(CalibrationEvent event) {
        List<Task> taskList = event.getTaskList();
        Map<String, String> taskMap = taskList.stream().collect(Collectors.toMap(Task::getAssignee, Task::getId));
        List<Task> pendingTaskList = taskList.stream().filter(obj -> obj.getDelegationState() != null
                && DelegationState.PENDING.equals(obj.getDelegationState())).collect(Collectors.toList());
        Set<String> collect;
        if (CollectionUtil.isNotEmpty(pendingTaskList)) {
            /**
             * 说明是委托给其他人的任务,这里就只发给委托人的代办邮件
             */
            collect = pendingTaskList.stream().map(Task::getAssignee).collect(Collectors.toSet());
            log.info("这里发生了委托人事件:{}", JSONUtil.toJsonStr(pendingTaskList));
        } else {
            collect = taskList.stream().map(Task::getAssignee).collect(Collectors.toSet());
        }

        R<List<HrsfUserBase>> listR = remoteUserService.selectEmailAddressByStaffIdList(collect, SecurityConstants.FROM_IN);
        HrsfCalibrationBase calibrationBase = event.getCalibrationBase();
        if (listR.getCode() == 0) {
            List<HrsfUserBase> data = listR.getData();
            for (HrsfUserBase hrsfUserBase : data) {
                EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
                emailInfoDTO.setTemplate(event.getTemplate());
                emailInfoDTO.setSubject(event.getSubject());
                emailInfoDTO.setSendTo(hrsfUserBase.getEmail());
                Map<String, Object> model = new HashMap<>(2);
                //http://**************:9527/#/meetingInbox/detailFolder?id=1534478156078997506&taskId=b75829a7-ede3-11ec-ad0b-acde48001122
                String taskId = taskMap.get(hrsfUserBase.getStaffId());
                model.put("calibrationName", calibrationBase.getName());
                model.put("link", StrUtil.format(event.getLink(), calibrationBase.getId(), taskId));
                emailInfoDTO.setModel(model);
                remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
            }
        }
    }

    /**
     * 触发数据回写
     *
     * @param event
     */
    void handlePmsUpdateEventG(CalibrationEvent event) {
        List<WriteDataBackDTO> writeDataBackDTOS = event.getWriteDataBackDTOS();
        String processStep = event.getProcessStep();
        String template = event.getTemplate();
        if (Objects.equals(ProcessStepEnum.HR_MANAGER.getType(), processStep)) {
            //机构负责人校准结束后，数据回写
            if (Objects.equals(CalibrationTemplateEnum.ONE.getType(), template)) {
                //蓝白领季度+蓝领年度
                remoteUserService.writeDataBack(writeDataBackDTOS, SecurityConstants.FROM_IN);
            } else if (Objects.equals(CalibrationTemplateEnum.TWO.getType(), template)) {
                //白领年
                remoteUserService.whiteYearWriteDataBack(writeDataBackDTOS, SecurityConstants.FROM_IN);
            }
        } else if (Objects.equals(ProcessStepEnum.END.getType(), processStep)) {
            //HR L3审批结束后，数据回写
            if (Objects.equals(CalibrationTemplateEnum.ONE.getType(), template)) {
                //蓝白领季度+蓝领年度
                remoteUserService.finalWriteDataBack(writeDataBackDTOS, SecurityConstants.FROM_IN);
            } else if (Objects.equals(CalibrationTemplateEnum.TWO.getType(), template)) {
                //白领年
                remoteUserService.whiteYearFinalWriteDataBack(writeDataBackDTOS, SecurityConstants.FROM_IN);
            }
        }


    }

    /**
     * 触发数据回写
     *
     * @param event
     */
    void handlePmsUpdateEventH(CalibrationEvent event) {
        List<WriteDataBackDTO> writeDataBackDTOS = event.getWriteDataBackDTOS();
        remoteUserService.finalWriteDataBack(writeDataBackDTOS, SecurityConstants.FROM_IN);
    }

    /**
     * 开启工作流
     *
     * @param calibrationBaseMap
     */
    void startFlowable(Map<Long, Set<String>> calibrationBaseMap, CalibrationEvent event) {
        Optional<HrsfProcessDefinitionExt> optional = processDefinitionExtService.list(
                Wrappers.<HrsfProcessDefinitionExt>lambdaQuery()
                        .eq(HrsfProcessDefinitionExt::getType, BpmProcessDefinitionEnum.UNDER.getType())
                        .orderByDesc(HrsfProcessDefinitionExt::getUpdateTime)).stream().findFirst();
        if (optional.isPresent()) {
            calibrationBaseMap.entrySet().stream().forEach(obj -> {
                HrsfProcessDefinitionExt definitionExt = optional.get();
                Map<String, Object> hashMap = new ConcurrentHashMap<>(16);

                Long calibrationBaseId = obj.getKey();
                /**
                 * 根据员工号去找对应的审批人
                 *
                 */
                Set<String> staffIdList = obj.getValue();
                R<List<String>> pmsListR = remoteUserService.selectByStaffIdList(staffIdList, SecurityConstants.FROM_IN);
                log.info("baseID:{},返回的绩效协调员信息:{}", calibrationBaseId, JSONUtil.toJsonStr(pmsListR));
                if (pmsListR.getCode() == 0) {
                    hashMap.put(BpmProcessParamEnum.TASK01.getResult(), pmsListR.getData());
                }
                /**
                 * 根据员工号去找对应的机构负责人信息
                 *
                 * -- todo
                 */
                String organization = hrsfCalibrationFilterMapper.getDistinctOrganization(calibrationBaseId);
                if (StrUtil.isNotBlank(organization) && organization.contains("(") && organization.contains(")")) {
                    R<List<String>> deptHeadR = remoteUserService.selectDeptHeadList(
                            organization.substring(organization.lastIndexOf("(") + 1, organization.lastIndexOf(")")), SecurityConstants.FROM_IN);
                    log.info("baseID:{},返回的机构负责人信息:{}", calibrationBaseId, JSONUtil.toJsonStr(deptHeadR));
                    if (deptHeadR.getCode() == 0) {
                        hashMap.put(BpmProcessParamEnum.TASK02.getResult(), deptHeadR.getData());
                    }
                }
                R<List<String>> hrUserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HR.getType(), SecurityConstants.FROM_IN);
                if (hrUserListR.getCode() == 0) {
                    hashMap.put(BpmProcessParamEnum.TASK03.getResult(), hrUserListR.getData());
                }
                R<List<String>> hrL4UserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HRL4.getType(), SecurityConstants.FROM_IN);
                if (hrL4UserListR.getCode() == 0) {
                    hashMap.put(BpmProcessParamEnum.TASK04.getResult(), hrL4UserListR.getData());
                }
                R<List<String>> hrL3UserListR = remoteUserService.selectByRoleCode(RoleTypeEnum.HRL3.getType(), SecurityConstants.FROM_IN);
                if (hrL3UserListR.getCode() == 0) {
                    hashMap.put(BpmProcessParamEnum.TASK05.getResult(), hrL3UserListR.getData());
                }
                BpmProcessInstanceCreateReqVO reqVO = new BpmProcessInstanceCreateReqVO().setVariables(hashMap)
                        .setProcessDefinitionId(definitionExt.getProcessDefinitionId())
                        .setCalibrationBaseId(obj.getKey())
                        .setFullName(event.getFullName())
                        .setStaffId(event.getUserName());
                String processInstanceId = processInstanceService.createProcessInstance
                        (SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getId(), reqVO);
                hrsfCalibrationBaseService.lambdaUpdate().set(HrsfCalibrationBase::getProcessInstanceId, processInstanceId)
                        .eq(HrsfCalibrationBase::getId, obj.getKey()).update();
            });
        }
    }

    private void handlePmsUpdateEvent(CalibrationEvent event) {
        /**
         * 这里做出修改
         * 先同步一遍主数据然后在走之后的逻辑
         */
        R<Boolean> booleanR = remoteUserTimeService.syncPmsUser(SecurityConstants.FROM_IN);
        if (booleanR.getCode() != 0) {
            throw new CheckedException("同步员工主数据失败,请稍后再试!");
        }
        R<Boolean> startPmsUserR = remoteUserTimeService.startPmsUser(event.getHrsfPmstart(), SecurityConstants.FROM_IN);
        if (startPmsUserR.getCode() == 0 && startPmsUserR.getData()) {
            /**
             * 1、生成校准会议Filter表
             * 2、生成校准会议基础表
             * 3、开启审批流
             * 这里线程等待20分钟，防止上面的流程没走完，造成数据库死锁
             */
            try {
                Thread.sleep(25 * 60 * 1000);
            } catch (InterruptedException e) {
                log.error("线程睡眠失败:{}", e);
            }
            handleCalibrationFilter(event);
            //构造一个calibrationBaseId和staffID的map对象
            Map<Long, Set<String>> calibrationBaseMap = handleCalibrationBase(event);
            log.info("生成的map对象信息为:{}", JSONUtil.toJsonStr(calibrationBaseMap));
            startFlowable(calibrationBaseMap, event);
            /**
             * 全部执行完毕后发送邮件
             */
            // --todo
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setTemplate("launch_success_template.html");
            emailInfoDTO.setSubject("开发平台绩效考核启动成功通知");
            log.info("启动成功后,获取到的HR邮箱账号:{}", operationEmailHR);
            emailInfoDTO.setSendTo(operationEmailHR);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
        } else {
            throw new CheckedException("生成绩效员工基础表失败,请稍后再试!");
        }
    }

    /**
     * 生成校准会议基础表
     *
     * @param event
     */
    Map handleCalibrationBase(CalibrationEvent event) {
        log.info("生成校准会议基础表.....");
        String assesYear = event.getAssessYear();
        String assesType = event.getAssessType();
        Map<Long, Set<String>> calibrationBaseMap = new ConcurrentHashMap<>(32);
        List<HrsfCalibrationFilter> filterList = hrsfCalibrationFilterService.list(Wrappers.<HrsfCalibrationFilter>lambdaQuery()
                .isNotNull(HrsfCalibrationFilter::getOrganization)
                .eq(HrsfCalibrationFilter::getAssessYear, assesYear)
                .eq(HrsfCalibrationFilter::getAssessType, assesType));
        //判断将职级、是否为助理、是否为SYL4以及机构属性均相同的员工生成一组
        Map<String, Set<String>> groupMap = filterList.stream().collect(Collectors.groupingBy(o ->
                StrUtil.join("###", Arrays.asList(o.getOrganization()
                        , o.getAssistant(), o.getSyl4(), o.getUserLevel())), Collectors.mapping(HrsfCalibrationFilter::getStaffId, Collectors.toSet())));

        //处理生成校准会议基础表
        groupMap.keySet().stream().forEach(obj -> {
            HrsfCalibrationBase base = new HrsfCalibrationBase();
            base.setAssesType(assesType);
            base.setAssesYear(assesYear);
            //o.getOrganization()_o.getAssistant()_o.getSyl4()_o.getUserLevel()
            List<String> split = StrUtil.split(obj, "###");
            if (CollectionUtil.isNotEmpty(split)) {
                base.setOrganization(StrUtil.isBlank(split.get(0)) ? null : split.get(0));
                base.setUserLevel(StrUtil.isBlank(split.get(3)) ? null : split.get(3));
            }
            //--todo 待定,模板内容待确认
            if (AssessTypeEnum.YEAR.getType().equals(assesType) && !UserLevelEnum.L7.name().equals(split.get(3))) {
                base.setTemplate(CalibrationTemplateEnum.TWO.getType());
            } else {
                base.setTemplate(CalibrationTemplateEnum.ONE.getType());
            }
            base.setName(StrUtil.format(CommonConstants.CONTENT, assesYear, assesType, split.get(3), split.get(0)));
            //新增完基础表后,将校准会议基础表ID更新到Filter表中
            hrsfCalibrationBaseService.save(base);
            hrsfCalibrationFilterService.lambdaUpdate()
                    .set(HrsfCalibrationFilter::getCalibrationBaseId, base.getId())
                    .eq(HrsfCalibrationFilter::getAssessType, assesType)
                    .eq(HrsfCalibrationFilter::getAssessYear, assesYear)
                    .in(HrsfCalibrationFilter::getStaffId, groupMap.get(obj)).update();
            calibrationBaseMap.put(base.getId(), groupMap.get(obj));
            //调用user服务更新calibrationBaseId
            remoteUserService.updateCalibrationBaseId(base.getId(), assesType, assesYear, groupMap.get(obj), SecurityConstants.FROM_IN);

        });
        return calibrationBaseMap;
    }

    /**
     * 生成校准会议Filter表
     *
     * @param event
     */
    void handleCalibrationFilter(CalibrationEvent event) {
        log.info("生成校准会议Filter表.....");
        String assesYear = event.getAssessYear();
        String assesType = event.getAssessType();
        R<List<HrsfPmsuserBase>> returnR = remoteUserService.getPmsUserList(assesYear, assesType, SecurityConstants.FROM_IN);
        if (returnR.getCode() == 1) {
            log.error("获取员工考核基础表信息失败。。。。。");
            return;
        }
        ArrayList<HrsfCalibrationFilter> entityList = new ArrayList<>();
        /**
         * 处理list的数据
         *
         * L3	取“系统”字段信息；若“系统”字段信息为空，写固定值“HR”；
         * L4	取“系统”字段信息；若“系统”字段信息为空，写固定值“HR”；
         * L5 L6  判断若“部门”字段有值，取“部门”字段信息；
         *   	判断若“部门”字段为空，判断若“系统”字段有值，取“系统”字段信息；
         *      若“系统”字段信息为空，写固定值“HR”；
         * L7	判断若“科室”字段有值，取“科室”字段信息；
         *      判断若“科室”字段为空，判断若“部门”字段有值，取“部门”字段信息；
         *      若“部门”字段信息为空，判断若“系统”字段有值，取“系统”字段信息；
         *      若“系统”字段信息为空，写固定值“HR”；
         */
        returnR.getData().stream().forEach(obj -> {
            HrsfCalibrationFilter filter = HrsfPmsConvert.INSTANCE.toHrsfCalibrationFilter(obj);
            if (UserLevelEnum.L3.name().equals(obj.getUserLevel())) {
                filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
            } else if (UserLevelEnum.L4.name().equals(obj.getUserLevel())) {
                filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
            } else if (UserLevelEnum.L5.name().equals(obj.getUserLevel()) || UserLevelEnum.L6.name().equals(obj.getUserLevel())) {
                if (StrUtil.isNotBlank(obj.getDepartment())) {
                    filter.setOrganization(obj.getDepartment());
                } else {
                    filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
                }
            } else if (UserLevelEnum.L7.name().equals(obj.getUserLevel())) {
                if (StrUtil.isNotBlank(obj.getSection())) {
                    filter.setOrganization(obj.getSection());
                } else {
                    if (StrUtil.isNotBlank(obj.getDepartment())) {
                        filter.setOrganization(obj.getDepartment());
                    } else {
                        filter.setOrganization(StrUtil.isNotBlank(obj.getSystem()) ? obj.getSystem() : CommonConstants.HR);
                    }
                }
            }
            entityList.add(filter);
        });
        hrsfCalibrationFilterService.saveBatch(entityList);
    }

}
