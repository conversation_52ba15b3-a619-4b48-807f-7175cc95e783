package com.bbac.hrsf.performance.controller.flowable.definition;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.flowable.definition.vo.process.*;
import com.bbac.hrsf.performance.service.definition.BpmProcessDefinitionService;
import io.swagger.annotations.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Api(tags = "管理后台 - 流程定义")
@RestController
@RequestMapping("/bpm/process-definition")
@Validated
public class BpmProcessDefinitionController {

    @Resource
    private BpmProcessDefinitionService bpmDefinitionService;

    @GetMapping("/page")
    @ApiOperation(value = "获得流程定义分页")
    public R<IPage<BpmProcessDefinitionPageItemRespVO>> getProcessDefinitionPage(
            BpmProcessDefinitionPageReqVO pageReqVO) {
        return R.ok(bpmDefinitionService.getProcessDefinitionPage(pageReqVO));
    }

    @GetMapping("/list")
    @ApiOperation(value = "获得流程定义列表")
    public R<List<BpmProcessDefinitionRespVO>> getProcessDefinitionList(
            BpmProcessDefinitionListReqVO listReqVO) {
        return R.ok(bpmDefinitionService.getProcessDefinitionList(listReqVO));
    }

    @GetMapping("/get-bpmn-xml")
    @ApiOperation(value = "获得流程定义的 BPMN XML")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = String.class)
    public R<String> getProcessDefinitionBpmnXML(@RequestParam("id") String id) {
        String bpmnXML = bpmDefinitionService.getProcessDefinitionBpmnXML(id);
        return R.ok(bpmnXML);
    }
}
