package com.bbac.hrsf.performance.service.definition;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.util.CollectionUtils;
import com.bbac.hrsf.performance.api.flowable.definition.vo.definition.BpmFormDO;
import com.bbac.hrsf.performance.api.flowable.definition.vo.form.*;

import javax.validation.Valid;
import java.util.*;

/**
 * 动态表单 Service 接口
 *
 * <AUTHOR>
 */
public interface BpmFormService {

    /**
     * 创建动态表单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createForm(@Valid BpmFormCreateReqVO createReqVO);

    /**
     * 更新动态表单
     *
     * @param updateReqVO 更新信息
     */
    void updateForm(@Valid BpmFormUpdateReqVO updateReqVO);

    /**
     * 删除动态表单
     *
     * @param id 编号
     */
    void deleteForm(Long id);

    /**
     * 获得动态表单
     *
     * @param id 编号
     * @return 动态表单
     */
    BpmFormDO getForm(Long id);

    /**
     * 获得动态表单列表
     *
     * @return 动态表单列表
     */
    List<BpmFormDO> getFormList();

    /**
     * 获得动态表单列表
     *
     * @param ids 编号
     * @return 动态表单列表
     */
    List<BpmFormDO> getFormList(Collection<Long> ids);

    /**
     * 获得动态表单 Map
     *
     * @param ids 编号
     * @return 动态表单 Map
     */
    default Map<Long, BpmFormDO> getFormMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        return CollectionUtils.convertMap(this.getFormList(ids), BpmFormDO::getId);
    }

    /**
     * 获得动态表单分页
     *
     * @param pageReqVO 分页查询
     * @return 动态表单分页
     */
    IPage<BpmFormDO> getFormPage(BpmFormPageReqVO pageReqVO);

    /**
     * 校验流程表单已配置
     *
     * @param configStr  configStr 字段
     * @return 流程表单
     */
    BpmFormDO checkFormConfig(String  configStr);

}
