package com.bbac.hrsf.performance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationFilter;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Mapper
public interface HrsfCalibrationFilterMapper extends BaseMapper<HrsfCalibrationFilter> {

    @Select("select ORGANIZATION from HRSF_CALIBRATION_FILTER where CALIBRATION_BASE_ID=#{calibrationBaseId} and ROWNUM=1")
    String getDistinctOrganization(Long calibrationBaseId);

    @Delete("delete from HRSF_CALIBRATION_FILTER where ASSESS_YEAR=#{assessYear} and ASSESS_TYPE=#{assessType} and STAFF_ID=#{staffId} ")
    void deletFilterByDto(@Param("assessYear") String assessYear, @Param("assessType") String assessType, @Param("staffId") String staffId);

    void deleteFilterByStaffIdAndBaseId(@Param("staffIds") List<String> staffIds, @Param("calibrationId") Long calibrationId);
}
