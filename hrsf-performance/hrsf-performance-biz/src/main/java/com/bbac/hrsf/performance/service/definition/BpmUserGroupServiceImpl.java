package com.bbac.hrsf.performance.service.definition;

import cn.hutool.core.collection.CollUtil;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.performance.api.flowable.definition.vo.definition.BpmUserGroupDO;
import com.bbac.hrsf.performance.api.flowable.definition.vo.group.*;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;


/**
 * 用户组 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BpmUserGroupServiceImpl implements BpmUserGroupService {
//
//    @Resource
//    private BpmUserGroupMapper userGroupMapper;

    @Override
    public Long createUserGroup(BpmUserGroupCreateReqVO createReqVO) {
        // 插入
//        BpmUserGroupDO userGroup = BpmUserGroupConvert.INSTANCE.convert(createReqVO);
//        userGroupMapper.insert(userGroup);
        // 返回
//        return userGroup.getId();
        return null;
    }

    @Override
    public void updateUserGroup(BpmUserGroupUpdateReqVO updateReqVO) {
        // 校验存在
//        this.validateUserGroupExists(updateReqVO.getId());
//        // 更新
//        BpmUserGroupDO updateObj = BpmUserGroupConvert.INSTANCE.convert(updateReqVO);
//        userGroupMapper.updateById(updateObj);
        return;
    }

    @Override
    public void deleteUserGroup(Long id) {
        // 校验存在
        this.validateUserGroupExists(id);
        // 删除
//        userGroupMapper.deleteById(id);
    }

    private void validateUserGroupExists(Long id) {
//        if (userGroupMapper.selectById(id) == null) {
//            throw ServiceExceptionUtil.exception(USER_GROUP_NOT_EXISTS);
//        }
    }

    @Override
    public BpmUserGroupDO getUserGroup(Long id) {

//        return userGroupMapper.selectById(id);
        return null;
    }

    @Override
    public List<BpmUserGroupDO> getUserGroupList(Collection<Long> ids) {

//        return userGroupMapper.selectBatchIds(ids);
        return null;
    }


    @Override
    public List<BpmUserGroupDO> getUserGroupListByStatus(Integer status) {
//        return userGroupMapper.selectListByStatus(status);
        return null;
    }

    @Override
    public IPage<BpmUserGroupDO> getUserGroupPage(BpmUserGroupPageReqVO pageReqVO) {

        return null;
//        return userGroupMapper.selectPage(pageReqVO);
    }

    @Override
    public void validUserGroups(Set<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得用户组信息
////        List<BpmUserGroupDO> userGroups = userGroupMapper.selectBatchIds(ids);
//        Map<Long, BpmUserGroupDO> userGroupMap = CollectionUtils.convertMap(userGroups, BpmUserGroupDO::getId);
//        // 校验
//        ids.forEach(id -> {
//            BpmUserGroupDO userGroup = userGroupMap.get(id);
//            if (userGroup == null) {
//                throw ServiceExceptionUtil.exception(USER_GROUP_NOT_EXISTS);
//            }
//            if (!CommonStatusEnum.ENABLE.getStatus().equals(userGroup.getStatus())) {
//                throw exception(USER_GROUP_IS_DISABLE, userGroup.getName());
//            }
//        });
    }

}
