package com.bbac.hrsf.performance.service.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.feign.RemoteUserService;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.BpmProcessInstanceResultEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.constant.enums.ScoreSourceEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.util.PageUtils;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.api.entity.HrsfTaskExt;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskApproveReqVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskDonePageItemRespVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskDonePageReqVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskRejectReqVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskTodoPageItemRespVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskTodoPageReqVO;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.BpmTaskUpdateAssigneeReqVO;
import com.bbac.hrsf.performance.convert.task.BpmTaskConvert;
import com.bbac.hrsf.performance.event.CalibrationEvent;
import com.bbac.hrsf.performance.service.IHrsfPmstartService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.DelegationState;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.api.history.HistoricTaskInstanceQuery;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.bbac.hrsf.common.core.util.CollectionUtils.convertSet;


/**
 * 流程任务实例 Service 实现类
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BpmTaskServiceImpl implements BpmTaskService {

    private final TaskService taskService;
    private final HistoryService historyService;

    private final BpmProcessInstanceService processInstanceService;

    private final IHrsfTaskExtService taskExtService;

    private final RemoteUserService remoteUserService;

    private final ApplicationEventPublisher publisher;

    private final IHrsfPmstartService hrsfPmstartService;

    @Value("${template.link}")
    private String link;


    @Override
    public IPage<BpmTaskTodoPageItemRespVO> getTodoTaskPage(String userId, BpmTaskTodoPageReqVO pageVO) {
        // 查询待办任务
        TaskQuery taskQuery = taskService.createTaskQuery()
                .taskAssignee(userId) // 分配给自己
                .orderByTaskCreateTime().desc(); // 创建时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            taskQuery.taskCreatedAfter(pageVO.getBeginCreateTime());
        }
        if (pageVO.getEndCreateTime() != null) {
            taskQuery.taskCreatedBefore(pageVO.getEndCreateTime());
        }
        // 执行查询
        List<Task> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            Page<BpmTaskTodoPageItemRespVO> page = new Page<>();
            page.setTotal(taskQuery.count());
            page.setRecords(new ArrayList<>());
            return page;
        }

        // 获得 ProcessInstance Map
        Map<String, ProcessInstance> processInstanceMap = processInstanceService.getProcessInstanceMap(
                convertSet(tasks, Task::getProcessInstanceId));
        // 拼接结果
        Page<BpmTaskTodoPageItemRespVO> respReturn = new Page();
        respReturn.setRecords(BpmTaskConvert.INSTANCE.convertList1(tasks, processInstanceMap));
        respReturn.setTotal(taskQuery.count());
        return respReturn;
    }

    @Override
    public IPage<BpmTaskDonePageItemRespVO> getDoneTaskPage(String userId, BpmTaskDonePageReqVO pageVO) {
        // 查询已办任务
        HistoricTaskInstanceQuery taskQuery = historyService.createHistoricTaskInstanceQuery()
                .finished() // 已完成
                .taskAssignee(userId) // 分配给自己
                .orderByHistoricTaskInstanceEndTime().desc(); // 审批时间倒序
        if (StrUtil.isNotBlank(pageVO.getName())) {
            taskQuery.taskNameLike("%" + pageVO.getName() + "%");
        }
        if (pageVO.getBeginCreateTime() != null) {
            taskQuery.taskCreatedAfter(pageVO.getBeginCreateTime());
        }
        if (pageVO.getEndCreateTime() != null) {
            taskQuery.taskCreatedBefore(pageVO.getEndCreateTime());
        }
        // 执行查询
        List<HistoricTaskInstance> tasks = taskQuery.listPage(PageUtils.getStart(pageVO), pageVO.getPageSize());
        if (CollUtil.isEmpty(tasks)) {
            Page<BpmTaskDonePageItemRespVO> page = new Page<>();
            page.setTotal(taskQuery.count());
            page.setRecords(new ArrayList<>());
            return page;
        }


        // 获得 ProcessInstance Map
        Map<String, HistoricProcessInstance> historicProcessInstanceMap = processInstanceService.getHistoricProcessInstanceMap(
                convertSet(tasks, HistoricTaskInstance::getProcessInstanceId));

        Page<BpmTaskDonePageItemRespVO> respReturn = new Page<>();
        respReturn.setRecords(BpmTaskConvert.INSTANCE.convertList2(tasks, historicProcessInstanceMap));
        respReturn.setTotal(taskQuery.count());
        return respReturn;
    }

    @Override
    public List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds) {
        if (CollUtil.isEmpty(processInstanceIds)) {
            return Collections.emptyList();
        }
        return taskService.createTaskQuery().processInstanceIdIn(processInstanceIds).list();
    }

    @Override
    public List<HistoricTaskInstance> getTaskListByProcessInstanceId(String processInstanceId) {


        // 获得任务列表
        return historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricTaskInstanceStartTime().desc() // 创建时间倒序
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String approveTask(String userId, @Valid BpmTaskApproveReqVO reqVO, HrsfCalibrationBase calibrationBase, Long baseId) {
        String taskDefinitionKey = null;
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw new CheckedException("PROCESS_INSTANCE_NOT_EXISTS");
        }

        DelegationState delegationState = task.getDelegationState();
        boolean delegationFlag = false;
        //如果是委托人委托,则用resolveTask解决
        if (delegationState != null && DelegationState.PENDING.name().equals(delegationState.name())) {
            // 完成任务，审批通过
            delegationFlag = true;
            taskService.resolveTask(task.getId(), instance.getProcessVariables());
        } else {
            taskService.complete(task.getId(), instance.getProcessVariables());
        }
        // 新增一条任务拓展表为通过
        taskExtService.save(new HrsfTaskExt()
                .setTaskId(task.getId())
                .setEndTime(new Date())
                .setFullName(SecurityUtils.getUser().getFullName())
                .setStaffId(SecurityUtils.getUser().getId())
                .setName(task.getName())
                .setProcessInstanceId(task.getProcessInstanceId())
                .setProcessDefinitionId(task.getProcessDefinitionId())
                .setProcessStep(ProcessStepEnum.getTypeByTaskId(task.getTaskDefinitionKey()))
                .setResult(BpmProcessInstanceResultEnum.APPROVE.getResult())
                .setApproveComment(reqVO.getComment()).setCalibrationBaseId(baseId));

        List<Task> taskList = taskService.createTaskQuery().processInstanceId(instance.getProcessInstanceId()).list();
        Optional<Task> optionalTask = taskList.stream().filter(data -> StrUtil.isNotBlank(data.getTaskDefinitionKey())).findFirst();
        if (optionalTask.isPresent()) {
            taskDefinitionKey = optionalTask.get().getTaskDefinitionKey();
        }


        /**
         *
         */

        /**
         * 只有机构负责人和HR管理员才发送邮件
         * 并且如果上一个节点和当前节点不一致才发送邮件
         * 否则不发送(解决会签节点多次发送问题)
         */
        if (ProcessStepEnum.OWNER.getTaskId().equals(taskDefinitionKey) || ProcessStepEnum.HR_MANAGER.getTaskId().equals(taskDefinitionKey)) {
            if (delegationFlag) {
                publisher.publishEvent(CalibrationEvent.of(link, "校准会议待办通知 Calibration Session Routing Notification", calibrationBase, taskList, "submit_template.html", PmsUpdateEventEnum.J));
            } else {
                if (StrUtil.isNotBlank(task.getTaskDefinitionKey()) && StrUtil.isNotBlank(taskDefinitionKey)
                        && !taskDefinitionKey.equals(task.getTaskDefinitionKey())) {
                    publisher.publishEvent(CalibrationEvent.of(link, "校准会议待办通知 Calibration Session Routing Notification", calibrationBase, taskList, "submit_template.html", PmsUpdateEventEnum.J));
                }
            }
        }

        if (calibrationBase != null && baseId != null) {
            R<List<HrsfPmsuserBase>> listR = remoteUserService.selectByBaseId(baseId, SecurityConstants.FROM_IN);
            List<HrsfPmsuserBase> data = listR.getData();
            List<WriteDataBackDTO> writeDataBackDTOS = new ArrayList<>();
            List<HrsfPmstart> hrsfPmstarts = hrsfPmstartService.list(Wrappers.<HrsfPmstart>query().lambda().eq(HrsfPmstart::getAssesYear, calibrationBase.getAssesYear())
                    .eq(HrsfPmstart::getAssesType, calibrationBase.getAssesType()));
            if (CollUtil.isNotEmpty(data)) {
                data.forEach(o -> {
                    //判断若员工分数来源=批量导入，不做处理；若员工分数来源=评分表，调取接口，回写业绩等级
                    if (!(Objects.equals(ProcessStepEnum.OWNER.getType(), calibrationBase.getProcessStep())
                            && Objects.equals(CalibrationTemplateEnum.ONE.getType(), calibrationBase.getProcessStep())
                            && Objects.equals(o.getScoreSource(), ScoreSourceEnum.IMPORT.getType()))) {
                        WriteDataBackDTO writeDataBackDTO = new WriteDataBackDTO();
                        BeanUtil.copyProperties(o, writeDataBackDTO);
                        writeDataBackDTO.setTaskId(reqVO.getId());

                        if (CollUtil.isNotEmpty(hrsfPmstarts)) {
                            HrsfPmstart hrsfPmstart = hrsfPmstarts.get(0);
                            writeDataBackDTO.setEndDate(hrsfPmstart.getEndDate());
                            writeDataBackDTO.setStartDate(hrsfPmstart.getStartDate());
                        }
                        writeDataBackDTO.setId(null);
                        writeDataBackDTO.setName(calibrationBase.getName());
                        writeDataBackDTOS.add(writeDataBackDTO);
                    }
                });
            }

            if (CollUtil.isNotEmpty(writeDataBackDTOS)) {
                String nextStep = ProcessStepEnum.getNextStep(taskDefinitionKey);
                publisher.publishEvent(CalibrationEvent.of(calibrationBase.getTemplate(), nextStep, writeDataBackDTOS, PmsUpdateEventEnum.G));
            }
        }
        return taskDefinitionKey;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvetest(String userId, @Valid BpmTaskApproveReqVO reqVO, HrsfCalibrationBase calibrationBase, Long baseId) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw new CheckedException("PROCESS_INSTANCE_NOT_EXISTS");
        }

        // 完成任务，审批通过
        taskService.complete(task.getId(), instance.getProcessVariables());
        // 更新任务拓展表为通过
        taskExtService.save(new HrsfTaskExt()
                .setTaskId(task.getId())
                .setEndTime(new Date())
                .setFullName(SecurityUtils.getUser().getFullName())
                .setStaffId(SecurityUtils.getUser().getId())
                .setName(task.getName())
                .setProcessInstanceId(task.getProcessInstanceId())
                .setProcessDefinitionId(task.getProcessDefinitionId())
                .setProcessStep(ProcessStepEnum.getTypeByTaskId(task.getTaskDefinitionKey()))
                .setResult(BpmProcessInstanceResultEnum.APPROVE.getResult())
                .setApproveComment(reqVO.getComment()).setCalibrationBaseId(baseId));

    }

    /**
     * 根据TaskID获取实例ID
     *
     * @param taskId
     * @return
     */
    @Override
    public String getProcessInstanceIdByTaskId(String userId, String taskId) {
        Task task = checkTask(userId, taskId);
        return task.getProcessInstanceId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String rejectTask(String userId, @Valid BpmTaskRejectReqVO reqVO, Long baseId, HrsfCalibrationBase calibrationBase) {
        String taskDefinitionKey = null;
        Task task = checkTask(userId, reqVO.getId());
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw new CheckedException("PROCESS_INSTANCE_NOT_EXISTS");
        }

        // 更新流程实例为不通过
        processInstanceService.updateProcessInstanceExtReject(instance.getProcessInstanceId(), reqVO.getComment(), task);

        // 更新任务拓展表为不通过
        taskExtService.save(new HrsfTaskExt()
                .setTaskId(task.getId())
                .setEndTime(new Date())
                .setFullName(SecurityUtils.getUser().getFullName())
                .setStaffId(SecurityUtils.getUser().getId())
                .setName(task.getName())
                .setProcessInstanceId(task.getProcessInstanceId())
                .setProcessDefinitionId(task.getProcessDefinitionId())
                .setProcessStep(ProcessStepEnum.getTypeByTaskId(task.getTaskDefinitionKey()))
                .setResult(BpmProcessInstanceResultEnum.REJECT.getResult())
                .setApproveComment(reqVO.getComment()).setCalibrationBaseId(baseId));

        List<Task> taskList = taskService.createTaskQuery().processInstanceId(instance.getProcessInstanceId()).list();
        Optional<Task> optionalTask = taskList.stream().filter(data -> StrUtil.isNotBlank(data.getTaskDefinitionKey())).findFirst();
        if (optionalTask.isPresent()) {
            taskDefinitionKey = optionalTask.get().getTaskDefinitionKey();
        }
        publisher.publishEvent(CalibrationEvent.of(link, "校准会议回退通知 Calibration Session Send Back Notification", calibrationBase, taskList, "reject_template.html", PmsUpdateEventEnum.J));
        return taskDefinitionKey;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String rejectTaskLastStep(String userId, @Valid BpmTaskRejectReqVO reqVO, Long baseId, HrsfCalibrationBase calibrationBase) {
        String taskDefinitionKey = null;
        /**
         * 这里不校验审批人和当前用户
         * 这里数据管理员的操作
         */
        Task task = getTask(reqVO.getId());
        if (task == null) {
            throw new CheckedException("当前任务不存在");
        }
        // 校验流程实例存在
        ProcessInstance instance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
        if (instance == null) {
            throw new CheckedException("PROCESS_INSTANCE_NOT_EXISTS");
        }

        // 更新流程实例为不通过
        processInstanceService.updateProcessInstanceExtRejectLastStep(instance.getProcessInstanceId(), reqVO.getComment(), task);

        // 更新任务拓展表为不通过
        taskExtService.save(new HrsfTaskExt()
                .setTaskId(task.getId())
                .setEndTime(new Date())
                .setFullName(SecurityUtils.getUser().getFullName())
                .setStaffId(SecurityUtils.getUser().getId())
                .setName(task.getName())
                .setProcessInstanceId(task.getProcessInstanceId())
                .setProcessDefinitionId(task.getProcessDefinitionId())
                .setProcessStep(ProcessStepEnum.getTypeByTaskId(task.getTaskDefinitionKey()))
                .setResult(BpmProcessInstanceResultEnum.REJECT_LAST_STEP.getResult())
                .setApproveComment(reqVO.getComment()).setCalibrationBaseId(baseId));

        List<Task> taskList = taskService.createTaskQuery().processInstanceId(instance.getProcessInstanceId()).list();
        Optional<Task> optionalTask = taskList.stream().filter(data -> StrUtil.isNotBlank(data.getTaskDefinitionKey())).findFirst();
        if (optionalTask.isPresent()) {
            taskDefinitionKey = optionalTask.get().getTaskDefinitionKey();
        }
        /**
         * 只有机构负责人和HR管理员节点
         * 才发送邮件
         */
        if (ProcessStepEnum.HRBP.getTaskId().equals(taskDefinitionKey)
                || ProcessStepEnum.OWNER.getTaskId().equals(taskDefinitionKey) || ProcessStepEnum.HR_MANAGER.getTaskId().equals(taskDefinitionKey)) {
            publisher.publishEvent(CalibrationEvent.of(link, "校准会议回退通知 Calibration Session Send Back Notification", calibrationBase, taskList, "reject_template_by_admin.html", PmsUpdateEventEnum.J));
        }
        return taskDefinitionKey;
    }

    @Override
    public void updateTaskAssignee(String userId, BpmTaskUpdateAssigneeReqVO reqVO) {
        // 校验任务存在
        Task task = checkTask(userId, reqVO.getId());
        // 更新负责人
        updateTaskAssignee(task.getId(), reqVO.getAssigneeUserId());
    }

    @Override
    public void updateTaskAssignee(String id, String userId) {
        taskService.setAssignee(id, String.valueOf(userId));
    }


    @Override
    public void createTaskExt(Task task) {
//        BpmTaskExtDO taskExtDO = BpmTaskConvert.INSTANCE.convert2TaskExt(task)
//                .setResult(BpmProcessInstanceResultEnum.PROCESS.getResult());
//        taskExtMapper.insert(taskExtDO);
    }

    @Override
    public void updateTaskExtComplete(Task task) {
//        BpmTaskExtDO taskExtDO = BpmTaskConvert.INSTANCE.convert2TaskExt(task)
//                .setEndTime(new Date());
//        taskExtMapper.updateByTaskId(taskExtDO);
    }

    @Override
    public void updateTaskExtAssign(Task task) {
//        BpmTaskExtDO taskExtDO = new BpmTaskExtDO()
//                .setAssigneeUserId(NumberUtils.parseLong(task.getAssignee()))
//                .setTaskId(task.getId());
//        taskExtMapper.updateByTaskId(taskExtDO);
//        // 发送通知。在事务提交时，批量执行操作，所以直接查询会无法查询到 ProcessInstance，所以这里是通过监听事务的提交来实现。
//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
//            @Override
//            public void afterCommit() {
//                ProcessInstance processInstance = processInstanceService.getProcessInstance(task.getProcessInstanceId());
//                AdminUserRespDTO startUser = adminUserApi.getUser(Long.valueOf(processInstance.getStartUserId()));
//                messageService.sendMessageWhenTaskAssigned(BpmTaskConvert.INSTANCE.convert(processInstance, startUser, task));
//            }
//        });
    }

    /**
     * 校验任务是否存在， 并且是否是分配给自己的任务
     *
     * @param userId 用户 id
     * @param taskId task id
     */
    @Override
    public Task checkTask(String userId, String taskId) {
        Task task = getTask(taskId);
        if (task == null) {
            throw new CheckedException("当前任务不存在");
        }
        if (!Objects.equals(userId, task.getAssignee())) {
            throw new CheckedException("当前用户没有权限操作");
        }
        return task;
    }

    /**
     * 校验任务是否存在， 并且是否是分配给自己的任务
     *
     * @param userId 用户 id
     * @param taskId task id
     */
    @Override
    public HistoricTaskInstance checkHistoricTask(String userId, String taskId) {
        HistoricTaskInstance task = getHistoricTask(taskId);
        if (task == null) {
            throw new CheckedException("当前任务不存在");
        }
        if (!Objects.equals(userId, task.getAssignee())) {
            throw new CheckedException("当前用户没有权限操作");
        }
        return task;
    }

    private Task getTask(String id) {
        taskService.createTaskQuery().taskId(id).singleResult();
        return taskService.createTaskQuery().taskId(id).singleResult();
    }

    private HistoricTaskInstance getHistoricTask(String id) {
        return historyService.createHistoricTaskInstanceQuery().taskId(id).singleResult();
    }

}
