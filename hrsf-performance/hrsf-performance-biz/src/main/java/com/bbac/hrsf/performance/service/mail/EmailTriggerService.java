package com.bbac.hrsf.performance.service.mail;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.bbac.hrsf.admin.api.entity.HrsfPmsSendEmailLog;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import com.bbac.hrsf.performance.service.IHrsfPmsSendEmailLogService;
import freemarker.template.Template;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import javax.mail.internet.MimeMessage;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.performance.service.mail.EmailTriggerService</li>
 * <li>CreateTime : 2022/04/01 17:17:17</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailTriggerService {
    @Value("${spring.mail.open}")
    private String mailOpen;

    @Value("${spring.mail.username}")
    private String username;

    @Value("${spring.mail.fromName}")
    private String fromName;

    @Value("${spring.mail.nickName}")
    private String nickName;


    private final JavaMailSender mailSender;

    private final FreeMarkerConfigurer freeMarkerConfigurer;

    private final IHrsfPmsSendEmailLogService hrsfPmsSendEmailLogService;

    public void triggerEmail(String templateStr) {
        try {
            // 获得模板
            Template template = freeMarkerConfigurer.getConfiguration().getTemplate(templateStr);
            // 使用Map作为数据模型，定义属性和值
            Map<String, Object> model = new HashMap<>(16);
            model.put("myName", "ZYF");
            // 传入数据模型到模板，替代模板中的占位符，并将模板转化为html字符串
            String templateHtml = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
            // 该方法本质上还是发送html邮件，调用之前发送html邮件的方法
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setFrom("<EMAIL>");
            helper.setTo("<EMAIL>");
            helper.setSubject("test mail send");
            //第二个参数：格式是否为html
            helper.setText(templateHtml, true);
            mailSender.send(message);
        } catch (Exception e) {
            log.error("send Email error:{}", e);
        }


    }

    public void sendEmailByInfo(EmailInfoDTO emailInfoDTO) {
        String email = null;
        try {
            if (Objects.equals(mailOpen, "open")) {
                // 获得模板
                Template template = freeMarkerConfigurer.getConfiguration().getTemplate(emailInfoDTO.getTemplate());
                // 传入数据模型到模板，替代模板中的占位符，并将模板转化为html字符串
                String templateHtml;
                if (Objects.nonNull(emailInfoDTO.getModel())) {
                    templateHtml = FreeMarkerTemplateUtils.processTemplateIntoString(template, emailInfoDTO.getModel());
                } else {
                    templateHtml = template.toString();
                }
                // 该方法本质上还是发送html邮件，调用之前发送html邮件的方法
                MimeMessage message = mailSender.createMimeMessage();
                MimeMessageHelper helper = new MimeMessageHelper(message, true);
                helper.setFrom(fromName, nickName);
                helper.setSubject(emailInfoDTO.getSubject());
                //第二个参数：格式是否为html
                helper.setText(templateHtml, true);
                if (CollUtil.isNotEmpty(emailInfoDTO.getSendToList())) {
                    for (String emailAddress : emailInfoDTO.getSendToList()) {
                        if (StringUtils.isNotBlank(emailAddress)) {
                            helper.setTo(emailAddress);
                            email = emailAddress;
                            mailSender.send(message);
                            saveEmailLog(emailInfoDTO, emailAddress, "success");
                        }
                    }
                }
                if (StringUtils.isNotBlank(emailInfoDTO.getSendTo())) {
                    helper.setTo(emailInfoDTO.getSendTo());
                    email = emailInfoDTO.getSendTo();
                    mailSender.send(message);
                    saveEmailLog(emailInfoDTO, emailInfoDTO.getSendTo(), "success");
                }
            }
        } catch (Exception e) {
            saveEmailLog(emailInfoDTO, email, "failed");
            throw new CheckedException(e);
        }
    }

    private void saveEmailLog(EmailInfoDTO emailInfoDTO, String emailAddress, String status) {
        HrsfPmsSendEmailLog hrsfPmsSendEmailLog = new HrsfPmsSendEmailLog();
        hrsfPmsSendEmailLog.setEmailAddress(emailAddress);
        hrsfPmsSendEmailLog.setSubject(emailInfoDTO.getSubject());
        hrsfPmsSendEmailLog.setTemplate(emailInfoDTO.getSubject());
        hrsfPmsSendEmailLog.setModelJson(JSONUtil.toJsonStr(emailInfoDTO));
        hrsfPmsSendEmailLog.setStatus(status);
        hrsfPmsSendEmailLogService.save(hrsfPmsSendEmailLog);
    }
}