package com.bbac.hrsf.performance.convert.task;


import com.bbac.hrsf.common.core.util.CollectionUtils;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.*;
import org.flowable.common.engine.impl.db.SuspensionState;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;

/**
 * Bpm 任务 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface BpmTaskConvert {

    BpmTaskConvert INSTANCE = Mappers.getMapper(BpmTaskConvert.class);

    default List<BpmTaskTodoPageItemRespVO> convertList1(List<Task> tasks, Map<String, ProcessInstance> processInstanceMap
                                                         ) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmTaskTodoPageItemRespVO respVO = convert1(task);
            ProcessInstance processInstance = processInstanceMap.get(task.getProcessInstanceId());
            return respVO;
        });
    }

    @Mapping(source = "suspended", target = "suspensionState", qualifiedByName = "convertSuspendedToSuspensionState")
    BpmTaskTodoPageItemRespVO convert1(Task bean);

    @Named("convertSuspendedToSuspensionState")
    default Integer convertSuspendedToSuspensionState(boolean suspended) {
        return suspended ? SuspensionState.SUSPENDED.getStateCode() :
                SuspensionState.ACTIVE.getStateCode();
    }

    default List<BpmTaskDonePageItemRespVO> convertList2(List<HistoricTaskInstance> tasks,
                                                         Map<String, HistoricProcessInstance> historicProcessInstanceMap) {
        return CollectionUtils.convertList(tasks, task -> {
            BpmTaskDonePageItemRespVO respVO = convert2(task);
            HistoricProcessInstance processInstance = historicProcessInstanceMap.get(task.getProcessInstanceId());

            return respVO;
        });
    }

    BpmTaskDonePageItemRespVO convert2(HistoricTaskInstance bean);
//
//    @Mappings({
//            @Mapping(source = "processInstance.id", target = "id"),
//            @Mapping(source = "processInstance.name", target = "name"),
//            @Mapping(source = "processInstance.startUserId", target = "startUserId"),
//            @Mapping(source = "processInstance.processDefinitionId", target = "processDefinitionId"),
//            @Mapping(source = "startUser.nickname", target = "startUserNickname")
//    })
//    BpmTaskTodoPageItemRespVO.ProcessInstance convert(ProcessInstance processInstance, AdminUserRespDTO startUser);
//
//    default List<BpmTaskRespVO> convertList3(List<HistoricTaskInstance> tasks, Map<String, BpmTaskExtDO> bpmTaskExtDOMap,
//                                             HistoricProcessInstance processInstance, Map<Long, AdminUserRespDTO> userMap,
//                                             Map<Long, DeptRespDTO> deptMap) {
//        return CollectionUtils.convertList(tasks, task -> {
//            BpmTaskRespVO respVO = convert3(task);
//            BpmTaskExtDO taskExtDO = bpmTaskExtDOMap.get(task.getId());
//            copyTo(taskExtDO, respVO);
//            if (processInstance != null) {
//                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(processInstance.getStartUserId()));
//                respVO.setProcessInstance(convert(processInstance, startUser));
//            }
//            AdminUserRespDTO assignUser = userMap.get(NumberUtils.parseLong(task.getAssignee()));
//            if (assignUser != null) {
//                respVO.setAssigneeUser(convert3(assignUser));
//                DeptRespDTO dept = deptMap.get(assignUser.getDeptId());
//                if (dept != null) {
//                    respVO.getAssigneeUser().setDeptName(dept.getName());
//                }
//            }
//            return respVO;
//        });
//    }
//
//    @Mapping(source = "taskDefinitionKey", target = "definitionKey")
//    BpmTaskRespVO convert3(HistoricTaskInstance bean);
//
//    BpmTaskRespVO.User convert3(AdminUserRespDTO bean);
//
//    @Mapping(target = "id", ignore = true)
//    void copyTo(BpmTaskExtDO from, @MappingTarget BpmTaskDonePageItemRespVO to);
//
//    @Mappings({
//            @Mapping(source = "processInstance.id", target = "id"),
//            @Mapping(source = "processInstance.name", target = "name"),
//            @Mapping(source = "processInstance.startUserId", target = "startUserId"),
//            @Mapping(source = "processInstance.processDefinitionId", target = "processDefinitionId"),
//            @Mapping(source = "startUser.nickname", target = "startUserNickname")
//    })
//    BpmTaskTodoPageItemRespVO.ProcessInstance convert(HistoricProcessInstance processInstance, AdminUserRespDTO startUser);
//
//    default BpmTaskExtDO convert2TaskExt(Task task){
//        BpmTaskExtDO taskExtDO = new BpmTaskExtDO()
//                .setTaskId(task.getId())
//                .setAssigneeUserId(NumberUtils.parseLong(task.getAssignee()))
//                .setName(task.getName())
//                .setProcessDefinitionId(task.getProcessDefinitionId())
//                .setProcessInstanceId(task.getProcessInstanceId());
//        taskExtDO.setCreateTime(task.getCreateTime());
//        return taskExtDO;
//    }
//
//    default BpmMessageSendWhenTaskCreatedReqDTO convert(ProcessInstance processInstance, AdminUserRespDTO startUser, Task task) {
//        BpmMessageSendWhenTaskCreatedReqDTO reqDTO = new BpmMessageSendWhenTaskCreatedReqDTO();
//        reqDTO.setProcessInstanceId(processInstance.getProcessInstanceId())
//                .setProcessInstanceName(processInstance.getName())
//                .setStartUserId(startUser.getId())
//                .setStartUserNickname(startUser.getNickname())
//                .setTaskId(task.getId())
//                .setTaskName(task.getName())
//                .setAssigneeUserId(NumberUtils.parseLong(task.getAssignee()));
//        return reqDTO;
//    }
}


