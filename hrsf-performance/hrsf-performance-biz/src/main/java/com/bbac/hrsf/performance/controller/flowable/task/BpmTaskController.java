package com.bbac.hrsf.performance.controller.flowable.task;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.*;
import com.bbac.hrsf.performance.service.IHrsfCalibrationBaseService;
import com.bbac.hrsf.performance.service.task.BpmTaskService;
import io.swagger.annotations.*;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * <AUTHOR>
 */
@Api(tags = "管理后台 - 流程任务实例")
@RestController
@RequestMapping("/bpm/task")
@Validated
public class BpmTaskController {

    @Resource
    private BpmTaskService taskService;
    @Resource
    private IHrsfCalibrationBaseService calibrationBaseService;


    @GetMapping("todo-page")
    @ApiOperation("获取 Todo 待办任务分页")
    public R<IPage<BpmTaskTodoPageItemRespVO>> getTodoTaskPage(@Valid BpmTaskTodoPageReqVO pageVO) {
        return R.ok(taskService.getTodoTaskPage(SecurityUtils.getUser().getId(), pageVO));
    }

    @GetMapping("done-page")
    @ApiOperation("获取 Done 已办任务分页")
    public R<IPage<BpmTaskDonePageItemRespVO>> getDoneTaskPage(@Valid BpmTaskDonePageReqVO pageVO) {
        return R.ok(taskService.getDoneTaskPage(SecurityUtils.getUser().getId(), pageVO));
    }

    @GetMapping("/list-by-process-instance-id")
    @ApiOperation(value = "获得指定流程实例的任务列表", notes = "包括完成的、未完成的")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    public R<List<HistoricTaskInstance>> getTaskListByProcessInstanceId(
            @RequestParam("processInstanceId") String processInstanceId) {
        return R.ok(taskService.getTaskListByProcessInstanceId(processInstanceId));
    }

    @PutMapping("/approve")
    @ApiOperation("通过任务")
    public R<Boolean> approveTask(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        taskService.approveTask(SecurityUtils.getUser().getId(), reqVO, null, null);
        return R.ok(true);
    }

    @PutMapping("/approvetest")
    @ApiOperation("通过任务")
    public R<Boolean> approvetest(@Valid @RequestBody BpmTaskApproveReqVO reqVO) {
        taskService.approvetest(SecurityUtils.getUser().getId(), reqVO, null, null);
        return R.ok(true);
    }

    @PutMapping("/reject")
    @ApiOperation("不通过任务")
    public R<Boolean> rejectTask(@Valid @RequestBody BpmTaskRejectReqVO reqVO) {

        taskService.rejectTask(SecurityUtils.getUser().getId(), reqVO, null, null);
        return R.ok(true);
    }

    @PutMapping("/update-assignee")
    @ApiOperation(value = "更新任务的负责人", notes = "用于【流程详情】的【转派】按钮")
    public R<Boolean> updateTaskAssignee(@Valid @RequestBody BpmTaskUpdateAssigneeReqVO reqVO) {
        taskService.updateTaskAssignee(SecurityUtils.getUser().getId(), reqVO);
        return R.ok(true);
    }
}
