/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.event;

import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.performance.api.dto.HrsfPmsuserBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import lombok.*;
import lombok.experimental.Accessors;
import org.flowable.task.api.Task;

import java.util.*;

/**
 * 启动绩效基础表更新部分字段内容事件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CalibrationEvent {


    private String url;
    private String template;
    private String processStep;
    private String assessYear;
    private String assessType;
    private Long calibrationId;
    private List<WriteDataBackDTO> writeDataBackDTOS;
    private List<String> taskIds;
    private List<Task> taskList;
    /**
     * 事件类别
     */
    private PmsUpdateEventEnum eventEnum;
    /**
     * 添加员工ID和员工姓名
     * 为了解决eventListener直接
     * 从SecurityUtil中获取不到用户信息
     */
    private String userName;

    private String fullName;

    private HrsfCalibrationBase calibrationBase;

    private String subject;

    private String link;

    /**
     * 员工工号List
     */
    private List<HrsfPmsuserBaseDTO> pmsUserBaseList;

    private Map<Long, Set<String>> calibrationBaseMap;

    private HrsfPmstart hrsfPmstart;

    public static CalibrationEvent of(HrsfPmstart hrsfPmstart, String assessYear, String assessType, PmsUpdateEventEnum eventEnum, String userName, String fullName) {
        return new CalibrationEvent().setHrsfPmstart(hrsfPmstart).setAssessYear(assessYear).setAssessType(assessType)
                .setEventEnum(eventEnum).setUserName(userName).setFullName(fullName);
    }

    public static CalibrationEvent of(String template, String processStep, List<WriteDataBackDTO> writeDataBackDTOS, PmsUpdateEventEnum eventEnum) {
        return new CalibrationEvent().setTemplate(template).setProcessStep(processStep).setWriteDataBackDTOS(writeDataBackDTOS).setEventEnum(eventEnum);
    }

    public static CalibrationEvent of(String link,String subject, HrsfCalibrationBase calibrationBase, List<Task> taskList, String template, PmsUpdateEventEnum eventEnum) {
        return new CalibrationEvent().setLink(link).setSubject(subject).setCalibrationBase(calibrationBase).setTaskList(taskList).setTemplate(template).setEventEnum(eventEnum);
    }

    public static CalibrationEvent of(String assessYear, String assessType, PmsUpdateEventEnum eventEnum, String userName, String fullName, List<HrsfPmsuserBaseDTO> pmsUserBaseList) {
        return new CalibrationEvent().setAssessYear(assessYear).setAssessType(assessType).setEventEnum(eventEnum)
                .setUserName(userName).setFullName(fullName).setPmsUserBaseList(pmsUserBaseList);
    }

    public static CalibrationEvent of(Map<Long, Set<String>> calibrationBaseMap, PmsUpdateEventEnum eventEnum, String userName, String fullName, List<HrsfPmsuserBaseDTO> pmsUserBaseList) {
        return new CalibrationEvent().setCalibrationBaseMap(calibrationBaseMap).setEventEnum(eventEnum)
                .setUserName(userName).setFullName(fullName).setPmsUserBaseList(pmsUserBaseList);
    }
}
