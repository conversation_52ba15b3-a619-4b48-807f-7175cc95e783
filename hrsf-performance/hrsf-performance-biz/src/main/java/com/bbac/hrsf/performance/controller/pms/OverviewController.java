package com.bbac.hrsf.performance.controller.pms;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.admin.api.vo.HrsfPmstartVO;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.dto.*;
import com.bbac.hrsf.performance.service.IHrsfCalibrationBaseService;
import com.bbac.hrsf.performance.service.IHrsfPmstartService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * @author: liu, jie
 * @create: 2022-06-10
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/overview")
@Api(value = "OverviewController", tags = "总览")
public class OverviewController {

    private final IHrsfCalibrationBaseService calibrationBaseService;
    private final IHrsfPmstartService hrsfPmstartService;


    @PostMapping("/getOverviewList")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<HrsfPmstartVO>> getOverviewList(@RequestBody HrsfPmaStartDTO hrsfPmaStartDTO) {
        return R.ok(hrsfPmstartService.getOverviewList(hrsfPmaStartDTO));
    }


    @PostMapping("/getCalibrationBasePage")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<HrsfCalibrationBaseTaskVO>> getCalibrationBasePage(@RequestBody CalibrationQueryDTO queryDTO) {
        return R.ok(calibrationBaseService.getCalibrationBasePage(queryDTO));
    }

    /**
     * 删除校准会议
     *
     * @return
     */
    @PostMapping("/removeCalibration")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> removeCalibration(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(calibrationBaseService.removeCalibrationById(addCalibrationUserDTO.getCalibrationId()));
    }

    /**
     * 添加校准会议
     *
     * @return
     */
    @PostMapping("/addCalibration")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> addCalibration(@RequestBody HrsfCalibrationBaseDTO hrsfCalibrationBaseDTO) {
        return R.ok(calibrationBaseService.addCalibration(hrsfCalibrationBaseDTO));
    }

    /**
     * 增加校准会议人员
     *
     * @return
     */
    @PostMapping("/addCalibrationUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> addCalibrationUser(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(calibrationBaseService.addCalibrationUser(addCalibrationUserDTO));
    }

    /**
     * 删除校准会议人员
     *
     * @return
     */
    @PostMapping("/removeCalibrationUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Object> removeCalibrationUser(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(calibrationBaseService.removeCalibrationUser(addCalibrationUserDTO));
    }

}
