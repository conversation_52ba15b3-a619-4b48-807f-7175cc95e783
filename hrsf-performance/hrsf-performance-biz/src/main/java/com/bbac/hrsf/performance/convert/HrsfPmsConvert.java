package com.bbac.hrsf.performance.convert;

import com.bbac.hrsf.admin.api.entity.*;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.performance.api.dto.HrsfCalibrationBaseDTO;
import com.bbac.hrsf.performance.api.dto.HrsfPmsuserBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfRuleConfiguration;
import com.bbac.hrsf.performance.api.vo.HrsfRuleConfigurationVO;
import org.flowable.task.api.Task;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.convert.HrsfOrganizaConvert</li>
 * <li>CreateTime : 2022/05/07 17:17:50</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface HrsfPmsConvert {
    HrsfPmsConvert INSTANCE = Mappers.getMapper(HrsfPmsConvert.class);


    HrsfCalibrationFilter toHrsfCalibrationFilter(HrsfPmsuserBase hrsfPmsuserBase);

    HrsfCalibrationBaseTaskVO toHrsfCalibrationBaseTaskVO(HrsfCalibrationBase calibrationBase);

    HrsfCalibrationBaseDTO toHrsfCalibrationBaseDTO(HrsfCalibrationBase byId);

    HrsfCalibrationFilter toHrsfCalibrationFilter(HrsfPmsuserBaseDTO obj);

    HrsfRuleConfigurationVO toHrsfRuleConfigurationVO(HrsfRuleConfiguration data);
}