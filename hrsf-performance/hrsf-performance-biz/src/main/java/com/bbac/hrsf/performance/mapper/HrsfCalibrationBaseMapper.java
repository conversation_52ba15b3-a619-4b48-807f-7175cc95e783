package com.bbac.hrsf.performance.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.performance.api.dto.CalibrationQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Mapper
public interface HrsfCalibrationBaseMapper extends BaseMapper<HrsfCalibrationBase> {

    /**
     * @param queryDTO
     * @param userId
     * @return
     */
    IPage<HrsfCalibrationBaseTaskVO> selectCustomPage(@Param("query") CalibrationQueryDTO queryDTO, @Param("userId")String userId);

    IPage<HrsfCalibrationBaseTaskVO> selectCustomTodoPage(@Param("query") CalibrationQueryDTO queryDTO, @Param("userId")String userId);
}
