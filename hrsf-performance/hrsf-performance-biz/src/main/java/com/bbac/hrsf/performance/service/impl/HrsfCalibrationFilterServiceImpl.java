package com.bbac.hrsf.performance.service.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationFilter;
import com.bbac.hrsf.performance.mapper.HrsfCalibrationFilterMapper;
import com.bbac.hrsf.performance.service.IHrsfCalibrationFilterService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Service
public class HrsfCalibrationFilterServiceImpl extends ServiceImpl<HrsfCalibrationFilterMapper, HrsfCalibrationFilter> implements IHrsfCalibrationFilterService {

    @Resource
    private HrsfCalibrationFilterMapper hrsfCalibrationFilterMapper;

    @Override
    public Boolean deletFilterByDto(List<String> staffIds, String assessYear, String assessType) {
        if (CollUtil.isNotEmpty(staffIds)) {
            for (String staffId : staffIds) {
                hrsfCalibrationFilterMapper.deletFilterByDto(assessYear, assessType, staffId);
            }
        }
        return true;
    }
}
