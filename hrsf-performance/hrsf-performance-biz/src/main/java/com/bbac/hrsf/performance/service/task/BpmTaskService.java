package com.bbac.hrsf.performance.service.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.common.core.util.CollectionUtils;
import com.bbac.hrsf.performance.api.flowable.task.vo.task.*;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 流程任务实例 Service 接口
 *
 * <AUTHOR>
 * <AUTHOR>
 */
public interface BpmTaskService {
    /**
     * 获得待办的流程任务分页
     *
     * @param userId    用户编号
     * @param pageReqVO 分页请求
     * @return 流程任务分页
     */
    IPage<BpmTaskTodoPageItemRespVO> getTodoTaskPage(String userId, BpmTaskTodoPageReqVO pageReqVO);

    /**
     * 获得已办的流程任务分页
     *
     * @param userId    用户编号
     * @param pageReqVO 分页请求
     * @return 流程任务分页
     */
    IPage<BpmTaskDonePageItemRespVO> getDoneTaskPage(String userId, BpmTaskDonePageReqVO pageReqVO);

    /**
     * 获得流程任务 Map
     *
     * @param processInstanceIds 流程实例的编号数组
     * @return 流程任务 Map
     */
    default Map<String, List<Task>> getTaskMapByProcessInstanceIds(List<String> processInstanceIds) {
        return CollectionUtils.convertMultiMap(getTasksByProcessInstanceIds(processInstanceIds),
                Task::getProcessInstanceId);
    }

    /**
     * 获得流程任务列表
     *
     * @param processInstanceIds 流程实例的编号数组
     * @return 流程任务列表
     */
    List<Task> getTasksByProcessInstanceIds(List<String> processInstanceIds);

    /**
     * 获得指令流程实例的流程任务列表，包括所有状态的
     *
     * @param processInstanceId 流程实例的编号
     * @return 流程任务列表
     */
    List<HistoricTaskInstance> getTaskListByProcessInstanceId(String processInstanceId);

    /**
     * 通过任务
     *
     * @param userId          用户编号
     * @param reqVO           通过请求
     * @param calibrationBase
     * @param baseId
     */
    String approveTask(String userId, @Valid BpmTaskApproveReqVO reqVO, HrsfCalibrationBase calibrationBase, Long baseId);

    /**
     * 不通过任务
     * @param userId 用户编号
     * @param reqVO  不通过请求
     * @param baseId
     * @param calibrationBase
     */
    String rejectTask(String userId, @Valid BpmTaskRejectReqVO reqVO, Long baseId, HrsfCalibrationBase calibrationBase);

    /**
     * 退回到上一步
     * @param userId 用户编号
     * @param reqVO  不通过请求
     * @param baseId
     * @param calibrationBase
     */
    String rejectTaskLastStep(String userId, @Valid BpmTaskRejectReqVO reqVO, Long baseId, HrsfCalibrationBase calibrationBase);

    /**
     * 将流程任务分配给指定用户
     *
     * @param userId 用户编号
     * @param reqVO  分配请求
     */
    void updateTaskAssignee(String userId, BpmTaskUpdateAssigneeReqVO reqVO);

    /**
     * 将流程任务分配给指定用户
     *
     * @param id     流程任务编号
     * @param userId 用户编号
     */
    void updateTaskAssignee(String id, String userId);

    /**
     * 创建 Task 拓展记录
     *
     * @param task 任务实体
     */
    void createTaskExt(Task task);

    /**
     * 更新 Task 拓展记录为完成
     *
     * @param task 任务实体
     */
    void updateTaskExtComplete(Task task);

    /**
     * 更新 Task 拓展记录，并发送通知
     *
     * @param task 任务实体
     */
    void updateTaskExtAssign(Task task);

    void approvetest(String id, BpmTaskApproveReqVO reqVO, HrsfCalibrationBase calibrationBase, Long baseId);

    /**
     * 根据TaskID获取实例ID
     * @param taskId
     * @return
     */
    String getProcessInstanceIdByTaskId(String userId,String taskId);

    /**
     * 根据TaskID获取实例ID
     * @param taskId
     * @return
     */
    Task checkTask(String userId,String taskId);

    /**
     * 获取已办的任务和员工ID校验
     * @param taskId
     * @return
     */
    HistoricTaskInstance checkHistoricTask(String userId,String taskId);
}
