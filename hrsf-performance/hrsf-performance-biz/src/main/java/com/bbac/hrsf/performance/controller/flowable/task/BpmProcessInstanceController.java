package com.bbac.hrsf.performance.controller.flowable.task;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.flowable.task.vo.instance.*;
import com.bbac.hrsf.performance.service.task.BpmProcessInstanceService;
import io.swagger.annotations.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Api(tags = "管理后台 - 流程实例")
@RestController
@RequestMapping("/bpm/process-instance")
@Validated
public class BpmProcessInstanceController {
    @Resource
    private BpmProcessInstanceService processInstanceService;

    @GetMapping("/my-page")
    @ApiOperation(value = "获得我的实例分页列表", notes = "在【我的流程】菜单中，进行调用")
    public R<IPage<BpmProcessInstancePageItemRespVO>> getMyProcessInstancePage(
            @Valid BpmProcessInstanceMyPageReqVO pageReqVO) {
        return R.ok(processInstanceService.getMyProcessInstancePage(SecurityUtils.getUser().getId(), pageReqVO));
    }

    @PostMapping("/create")
    @ApiOperation("新建流程实例")
    public R<String> createProcessInstance(@Valid @RequestBody BpmProcessInstanceCreateReqVO createReqVO) {
        return R.ok(processInstanceService.createProcessInstance(SecurityUtils.getUser().getId(), createReqVO));
    }

    @GetMapping("/get")
    @ApiOperation(value = "获得指定流程实例", notes = "在【流程详细】界面中，进行调用")
    @ApiImplicitParam(name = "id", value = "流程实例的编号", required = true, dataTypeClass = String.class)
    public R<BpmProcessInstanceRespVO> getProcessInstance(@RequestParam("id") String id) {
        return R.ok(processInstanceService.getProcessInstanceVO(id));
    }

    @DeleteMapping("/cancel")
    @ApiOperation(value = "取消流程实例", notes = "撤回发起的流程")
    public R<Boolean> cancelProcessInstance(@Valid @RequestBody BpmProcessInstanceCancelReqVO cancelReqVO) {
        processInstanceService.cancelProcessInstance(SecurityUtils.getUser().getId(), cancelReqVO);
        return R.ok(true);
    }
}
