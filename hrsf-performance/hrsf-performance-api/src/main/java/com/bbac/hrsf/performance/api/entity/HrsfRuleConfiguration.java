package com.bbac.hrsf.performance.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_RULE_CONFIGURATION")
@ApiModel(value = "HrsfRuleConfiguration对象", description = "")
public class HrsfRuleConfiguration extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("类型")
    @TableField("TYPE")
    private String type;

    @ApiModelProperty("名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("最小值")
    @TableField("MIN_VALUE")
    private BigDecimal minValue;

    @ApiModelProperty("最大值")
    @TableField("MAX_VALUE")
    private BigDecimal maxValue;

    @ApiModelProperty("组别")
    @TableField("GROUP_BY")
    private String groupBy;

    @ApiModelProperty("排序")
    @TableField("ORDER_BY")
    private String orderBy;


}
