package com.bbac.hrsf.performance.api.feign;

import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.ServiceNameConstants;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @author: liu, jie
 * @create: 2022-06-08
 **/
@FeignClient(contextId = "remoteEmailService", value = ServiceNameConstants.PMS_SERVICE)
public interface RemoteEmailService {

    @PostMapping("/bpm/model/sendEmailByInfo")
    R<Boolean> sendEmailByInfo(@RequestBody EmailInfoDTO emailInfoDTO, @RequestHeader(SecurityConstants.FROM) String from);

}
