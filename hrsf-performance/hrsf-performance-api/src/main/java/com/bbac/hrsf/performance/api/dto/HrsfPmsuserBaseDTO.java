package com.bbac.hrsf.performance.api.dto;

import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Data
public class HrsfPmsuserBaseDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("工号")
    private String staffId;

    @ApiModelProperty("员工状态")
    private String status;

    @ApiModelProperty("员工姓名")
    private String fullName;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("科室")
    private String section;

    @ApiModelProperty("工段/组")
    private String userGroup;

    @ApiModelProperty("岗位")
    private String position;

    @ApiModelProperty("职称")
    private String jobTitle;

    @ApiModelProperty("职级")
    private String userLevel;

    @ApiModelProperty("用工形式")
    private String employmentType;

    @ApiModelProperty("蓝领/白领")
    private String bcWc;

    @ApiModelProperty("是否是顺义L4")
    private String syl4;

    @ApiModelProperty("是否为助理人员")
    private String assistant;

    @ApiModelProperty("考核年度")
    private String assessYear;

    @ApiModelProperty("考核类型")
    private String assessType;


}
