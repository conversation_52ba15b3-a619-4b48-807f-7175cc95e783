package com.bbac.hrsf.performance.api.dto;

import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Data
public class HrsfCalibrationBaseDTO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("考核年度")
    private String assesYear;

    @ApiModelProperty("考核类型")
    private String assesType;

    @ApiModelProperty("职级")
    private String userLevel;

    @ApiModelProperty("机构")
    private String organization;

    @ApiModelProperty("校准会议名称")
    private String name;

    @ApiModelProperty("校准会议模板")
    private String template;

    @ApiModelProperty("校准会议状态")
    private Byte status;

    @ApiModelProperty("当前处理人")
    private String currentHandler;

    @ApiModelProperty("代办状态")
    private String processStatus;

    @ApiModelProperty("流程步骤")
    private String processStep;

    @ApiModelProperty("流程ID")
    private String processInstanceId;

    @ApiModelProperty("绩效协调员")
    private List<String> pmsStaff;

    @ApiModelProperty("校准负责人")
    private List<String> pmsPrincipal;

    @ApiModelProperty("是否超过截止日期")
    private Boolean outOfDateFlag;


}
