package com.bbac.hrsf.performance.api.flowable.definition.vo.definition;


import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.performance.api.flowable.BaseDO;
import lombok.*;

import java.util.Set;

/**
 * Bpm 任务分配的规则表，用于自定义配置每个任务的负责人、候选人的分配规则。
 * 也就是说，废弃 BPMN 原本的 UserTask 设置的 assignee、candidateUsers 等配置，而是通过使用该规则进行计算对应的负责人。
 * <p>
 * 1. 默认情况下，{@link #processDefinitionId} 为 {@link #PROCESS_DEFINITION_ID_NULL} 值，表示贵改则与流程模型关联
 * 2. 在流程模型部署后，会将他的所有规则记录，复制出一份新部署出来的流程定义，通过设置 {@link #processDefinitionId} 为新的流程定义的编号进行关联
 *
 * <AUTHOR>
 */
@TableName(value = "bpm_task_assign_rule", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpmTaskAssignRuleDO extends BaseDO {

    /**
     * {@link #processDefinitionId} 空串，用于标识属于流程模型，而不属于流程定义
     */
    public static final String PROCESS_DEFINITION_ID_NULL = "";

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 流程模型编号
     * <p>
     * 关联 Model 的 id 属性
     */
    private String modelId;
    /**
     * 流程定义编号
     * <p>
     * 关联 ProcessDefinition 的 id 属性
     */
    private String processDefinitionId;
    /**
     * 流程任务的定义 Key
     * <p>
     * 关联 Task 的 taskDefinitionKey 属性
     */
    private String taskDefinitionKey;


    @TableField("`type`")
    private Integer type;

    private Set<Long> options;

}
