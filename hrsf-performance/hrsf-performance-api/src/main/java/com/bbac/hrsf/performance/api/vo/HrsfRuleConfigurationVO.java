package com.bbac.hrsf.performance.api.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-15
 */
@Data
public class HrsfRuleConfigurationVO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("最小值")
    @TableField("MIN_VALUE")
    private BigDecimal minValue;

    @ApiModelProperty("最大值")
    @TableField("MAX_VALUE")
    private BigDecimal maxValue;


}
