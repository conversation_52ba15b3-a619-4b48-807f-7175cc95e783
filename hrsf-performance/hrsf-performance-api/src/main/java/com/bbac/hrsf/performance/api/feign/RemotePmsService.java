/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.api.feign;

import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.ServiceNameConstants;
import com.bbac.hrsf.common.core.pojo.ErrorMessageSubVo;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.dto.HrsfCalibrationBaseDTO;
import com.bbac.hrsf.performance.api.dto.HrsfPmsuserBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
@FeignClient(contextId = "remotePmsService", value = ServiceNameConstants.PMS_SERVICE)
public interface RemotePmsService {


    @GetMapping("/pms/getCalibrationInner/{id}")
    R<HrsfCalibrationBaseDTO> getCalibrationInner(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM) String from);

    @GetMapping("/pms/getPmsStartInner")
    R<HrsfPmstart> getPmsStartInfoInner(@RequestParam("assessYear") String assessYear, @RequestParam("assessType") String assessType, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/pms/checkTaskById")
    Boolean checkTaskById(@RequestParam("taskId") String taskId,@RequestParam(value = "status",required = false) String status, @RequestParam("userId") String userId, @RequestHeader(SecurityConstants.FROM) String fromIn);

    /**
     * 绩效员工修改
     *
     * @param hrsfPmsuserBaseDTOList
     * @param assessYear
     * @param assessType
     * @param fromIn
     * @return
     */
    @PostMapping("/pms/updatePmsUserBase")
    List<ErrorMessageSubVo> updatePmsUserBase(@RequestBody List<HrsfPmsuserBaseDTO> hrsfPmsuserBaseDTOList,
                                              @RequestParam("assessYear") String assessYear,
                                              @RequestParam("assessType") String assessType,
                                              @RequestParam("userName") String userName,
                                              @RequestParam("fullName") String fullName,
                                              @RequestHeader(SecurityConstants.FROM) String fromIn);

    @PostMapping("/pms/deleteFilter")
    R<Boolean> deleteFilter(@RequestBody List<String> staffIds, @RequestParam("assessYear") String assessYear, @RequestParam("assessType")
            String assessType, @RequestHeader(SecurityConstants.FROM) String fromIn);

    @GetMapping("/pms/freezeCalibrationBase")
    void freezeCalibrationBase();
}
