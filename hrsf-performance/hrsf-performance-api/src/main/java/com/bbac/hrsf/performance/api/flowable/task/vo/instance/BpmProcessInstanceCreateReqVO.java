package com.bbac.hrsf.performance.api.flowable.task.vo.instance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.Map;

/**
 * <AUTHOR>
 */
@ApiModel("管理后台 - 流程实例的创建 Request VO")
@Data
@Accessors(chain = true)
public class BpmProcessInstanceCreateReqVO {

    @ApiModelProperty(value = "流程定义的编号", required = true)
    @NotEmpty(message = "流程定义编号不能为空")
    private String processDefinitionId;

    @ApiModelProperty(value = "校准会议基础表ID", required = true)
    private Long calibrationBaseId;

    @ApiModelProperty(value = "变量实例")
    private Map<String, Object> variables;

    private String fullName;

    private String staffId;

}
