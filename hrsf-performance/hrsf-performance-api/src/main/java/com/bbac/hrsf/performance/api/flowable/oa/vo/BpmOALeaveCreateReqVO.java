package com.bbac.hrsf.performance.api.flowable.oa.vo;

import io.swagger.annotations.ApiModel;
import lombok.*;

import javax.validation.constraints.AssertTrue;

@ApiModel("管理后台 - 请假申请创建 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmOALeaveCreateReqVO extends BpmOALeaveBaseVO {

    @AssertTrue(message = "结束时间，需要在开始时间之后")
    public boolean isEndTimeValid() {
        return !getEndTime().before(getStartTime());
    }

}
