package com.bbac.hrsf.performance.api.flowable.definition.vo.form;

import com.bbac.hrsf.common.core.pojo.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@ApiModel("管理后台 - 动态表单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class BpmFormPageReqVO extends PageParam {

    @ApiModelProperty(value = "表单名称", example = "芋道")
    private String name;

}
