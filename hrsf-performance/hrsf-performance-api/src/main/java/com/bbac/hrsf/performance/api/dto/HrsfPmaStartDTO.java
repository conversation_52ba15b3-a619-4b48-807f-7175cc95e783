/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.api.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Data
public class HrsfPmaStartDTO extends Page {


	private static final long serialVersionUID = 2562602032397367855L;
	@ApiModelProperty(value = "考核类型")
	private String assesYear;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "考核年度")
	private String assesType;

}
