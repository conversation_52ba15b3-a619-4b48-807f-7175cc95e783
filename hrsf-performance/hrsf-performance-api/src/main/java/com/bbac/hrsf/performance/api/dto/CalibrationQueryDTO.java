/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.api.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Data
public class CalibrationQueryDTO extends Page {


    private static final long serialVersionUID = 8934798857546758136L;
    @ApiModelProperty(value = "考核年度")
    private String assesYear;

    @ApiModelProperty(value = "考核类型")
    private String assesType;

    @ApiModelProperty("机构")
    private String organization;

    @ApiModelProperty("校准会议名称")
    private String name;

    @ApiModelProperty("职级")
    private String userLevel;

    @ApiModelProperty("流程步骤")
    private String processStep;

    @ApiModelProperty("代办状态")
    private String processStatus;

    @ApiModelProperty("校准会议状态")
    private Integer status;

    @ApiModelProperty("员工ID")
    private List<String> staffIdList;

    @ApiModelProperty("基础表ID主键")
    private Set<Long> calibrationBaseId;
}
