package com.bbac.hrsf.performance.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: liu, jie
 * @create: 2022-06-17
 **/
@Data
public class EmailInfoDTO {

    @ApiModelProperty(value = "收件人List")
    private List<String> sendToList;

    private String sendTo;

    @ApiModelProperty(value = "模板")
    private String template;

    private Map<String,Object> model;

    private String subject;
}
