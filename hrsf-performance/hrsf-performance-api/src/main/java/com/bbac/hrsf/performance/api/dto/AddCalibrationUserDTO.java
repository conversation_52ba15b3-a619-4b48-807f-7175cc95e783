package com.bbac.hrsf.performance.api.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * @author: liu, jie
 * @create: 2022-06-10
 **/
@Data
public class AddCalibrationUserDTO extends Page {

    private static final long serialVersionUID = 2719570806317473770L;
    private List<String> staffIds;

    private Long calibrationId;

    @ApiModelProperty(value = "考核年度")
    private String assesYear;

    @ApiModelProperty(value = "考核类型")
    private String assesType;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("科室")
    private String section;

    @ApiModelProperty("工段/组")
    private String userGroup;

    @ApiModelProperty("职级")
    private String userLevel;
    @ApiModelProperty("用工形式")
    private String employmentType;
    @ApiModelProperty("表单状态")
    private String formStatus;

}
