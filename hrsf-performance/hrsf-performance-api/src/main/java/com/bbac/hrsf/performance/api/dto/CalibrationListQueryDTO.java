/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.performance.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Data
public class CalibrationListQueryDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("基础表ID主键")
    private Long calibrationBaseId;

    @ApiModelProperty("系统")
    private Set<String> system;
    @ApiModelProperty("部门")
    private Set<String> department;
    @ApiModelProperty("科室")
    private Set<String> section;
    @ApiModelProperty("工段/组")
    private Set<String> userGroup;
    @ApiModelProperty("表单状态")
    private String formStatus;

    @ApiModelProperty("用工形式")
    private String employmentType;

    @ApiModelProperty("任务ID")
    private String taskId;

    @ApiModelProperty("员工ID")
    private List<String> staffIdList;

    @ApiModelProperty("是否已调整")
    private Boolean adjustedFlag;

    @ApiModelProperty("是否已调整")
    private String status;

    @ApiModelProperty("请求菜单标识")
    private String sign;



}
