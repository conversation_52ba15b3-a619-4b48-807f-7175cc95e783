package com.bbac.hrsf.performance.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PROCESS_DEFINITION_EXT")
@ApiModel(value = "HrsfProcessDefinitionExt对象", description = "")
public class HrsfProcessDefinitionExt extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("PROCESS_DEFINITION_ID")
    private String processDefinitionId;

    @TableField("MODEL_ID")
    private String modelId;

    @TableField("DESCRIPTION")
    private String description;
    /**
     * 流程定义类别
     * 0 普通审批流程
     * 1 公司级校准流程
     */
    @TableField("TYPE")
    private String type;


}
