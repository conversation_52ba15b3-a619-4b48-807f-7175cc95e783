package com.bbac.hrsf.performance.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-21
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName(value = "HRSF_PROCESS_INSTANCE_EXT", autoResultMap = true)
@ApiModel(value = "HrsfBpmProcessInstanceExt对象", description = "")
public class HrsfBpmProcessInstanceExt extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("编号")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("发起流程的用户编号")
    @TableField("START_USER_ID")
    private String startUserId;

    @ApiModelProperty("流程实例的名字")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("流程实例的编号")
    @TableField("PROCESS_INSTANCE_ID")
    private String processInstanceId;

    @ApiModelProperty("流程定义的编号")
    @TableField("PROCESS_DEFINITION_ID")
    private String processDefinitionId;

    @ApiModelProperty("业务ID")
    @TableField("CALIBRATION_BASE_ID")
    private Long calibrationBaseId;

    @ApiModelProperty("表单值")
    @TableField(typeHandler = JacksonTypeHandler.class, value = "FORM_VARIABLES")
    private Map<String, Object> formVariables;

    @ApiModelProperty("是否修改过审批人")
    @TableField("CHANGE_FLAG")
    private Boolean changeFlag;


}
