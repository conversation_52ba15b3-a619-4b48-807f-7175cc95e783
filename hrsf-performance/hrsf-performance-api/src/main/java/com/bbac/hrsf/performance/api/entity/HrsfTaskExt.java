package com.bbac.hrsf.performance.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-25
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PROCESS_TASK_EXT")
@ApiModel(value = "HrsfTaskExt对象", description = "")
public class HrsfTaskExt extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("员工姓名")
    @TableField("FULL_NAME")
    private String fullName;

    @ApiModelProperty("员工工号")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty("任务名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("任务ID")
    @TableField("TASK_ID")
    private String taskId;

    @ApiModelProperty("任务结果")
    @TableField("RESULT")
    private Integer result;

    @ApiModelProperty("审批建议")
    @TableField("APPROVE_COMMENT")
    private String approveComment;

    @ApiModelProperty("流程实例的编号")
    @TableField("PROCESS_INSTANCE_ID")
    private String processInstanceId;

    @ApiModelProperty("流程定义的编号")
    @TableField("PROCESS_DEFINITION_ID")
    private String processDefinitionId;

    @ApiModelProperty("任务的结束时间")
    @TableField("END_TIME")
    private Date endTime;

    @ApiModelProperty("流程步骤")
    @TableField("PROCESS_STEP")
    private String processStep;

    @ApiModelProperty("绩效员工基础表ID")
    @TableField("CALIBRATION_BASE_ID")
    private Long calibrationBaseId;


}
