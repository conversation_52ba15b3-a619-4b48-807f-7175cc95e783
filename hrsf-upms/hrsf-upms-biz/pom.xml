<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bbac</groupId>
        <artifactId>hrsf-upms</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hrsf-upms-biz</artifactId>
    <packaging>jar</packaging>

    <description>hrsf 通用用户权限管理系统业务处理模块</description>

    <dependencies>
        <!--upms api、model 模块-->
        <dependency>
            <groupId>com.bbac</groupId>
            <artifactId>hrsf-upms-api</artifactId>
        </dependency>
        <!--文件管理-->
        <dependency>
            <groupId>com.pig4cloud.plugin</groupId>
            <artifactId>oss-spring-boot-starter</artifactId>
        </dependency>
        <!--安全模块-->
        <dependency>
            <groupId>com.bbac</groupId>
            <artifactId>hrsf-common-security</artifactId>
        </dependency>
        <!--日志处理-->
        <dependency>
            <groupId>com.bbac</groupId>
            <artifactId>hrsf-common-log</artifactId>
        </dependency>
        <!--接口文档-->
        <dependency>
            <groupId>com.bbac</groupId>
            <artifactId>hrsf-common-swagger</artifactId>
        </dependency>
        <!--mybatis 模块-->
        <dependency>
            <groupId>com.bbac</groupId>
            <artifactId>hrsf-common-mybatis</artifactId>
        </dependency>
        <!--注册中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!--配置中心客户端-->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 阿里云短信下发 -->
        <dependency>
            <groupId>io.springboot.sms</groupId>
            <artifactId>aliyun-sms-spring-boot-starter</artifactId>
        </dependency>
        <!--undertow容器-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <!--单元测试-->
        <dependency>
            <groupId>com.bbac</groupId>
            <artifactId>hrsf-common-test</artifactId>
        </dependency>



        <!-- 浏览器驱动调用 -->
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
        </dependency>

        <!-- 图片转pdf使用 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13.3</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>**/*.xlsx</exclude>
                    <exclude>**/*.xls</exclude>
                    <exclude>**/*.pptx</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <includes>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                    <include>**/*.pptx</include>
                </includes>
            </resource>
        </resources>
    </build>

</project>
