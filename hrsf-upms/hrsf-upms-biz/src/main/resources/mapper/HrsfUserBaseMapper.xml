<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.HrsfUserBaseMapper">

    <update id="updatePhotoBatchById">
        update HRSF_USER_BASE
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="photo =case" suffix="end,">
                <foreach collection="userBaseList" item="item" index="index">
                    <if test="item.photo!=null and item.photo!= ''">
                        when staff_id=#{item.staffId} then #{item.photo}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="userBaseList" separator="or" item="item" index="index">
            staff_id=#{item.staffId}
        </foreach>
    </update>
</mapper>
