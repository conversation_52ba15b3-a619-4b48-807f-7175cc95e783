<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.SysDeptRelationMapper">
	<!-- 删除部门节点关系	-->
	<delete id="deleteDeptRelations">
		DELETE
		FROM sys_dept_relation
		WHERE descendant IN (SELECT temp.descendant
							 FROM (SELECT descendant FROM sys_dept_relation WHERE ancestor = #{descendant}) temp)
		  AND ancestor IN (SELECT temp.ancestor
						   FROM (SELECT ancestor
								 FROM sys_dept_relation
								 WHERE descendant = #{descendant}
								   AND ancestor != descendant) temp)
	</delete>

	<!--删除部门节点关系,同时删除所有关联此部门子节点的部门关系-->
	<delete id="deleteDeptRelationsById">
		DELETE
		FROM sys_dept_relation
		WHERE descendant IN (
				SELECT temp.descendant
				FROM (
							 SELECT descendant
							 FROM sys_dept_relation
							 WHERE ancestor = #{id}
							 ) temp
				)
	</delete>

	<!-- 新增部门节点关系	-->
	<insert id="insertDeptRelations">
		INSERT INTO sys_dept_relation (ancestor, descendant)
		SELECT a.ancestor, b.descendant
		FROM sys_dept_relation a
					 CROSS JOIN sys_dept_relation b
		WHERE a.descendant = #{ancestor}
		  AND b.ancestor = #{descendant}
	</insert>
</mapper>
