<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.SysRoleMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.bbac.hrsf.admin.api.entity.SysRole">
		<id column="role_id" property="roleId"/>
		<result column="role_name" property="roleName"/>
		<result column="role_desc" property="roleDesc"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="del_flag" property="delFlag"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="create_by" property="createBy"/>
		<result column="update_by" property="updateBy"/>
	</resultMap>

	<!-- 通过用户ID，查询角色信息-->
	<select id="listRolesByUserId" resultMap="BaseResultMap">
		SELECT r.role_id,
			   r.role_name,
			   r.role_desc,
			   r.del_flag,
			   r.create_time,
			   r.update_time,
			   r.update_by,
			   r.create_by
		FROM sys_role r,
			 sys_user_role ur
		WHERE r.role_id = ur.role_id
		  AND r.del_flag = 0
		  and ur.user_id = #{userId}
	</select>
</mapper>
