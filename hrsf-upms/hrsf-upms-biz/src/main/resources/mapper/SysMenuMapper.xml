<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.SysMenuMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.bbac.hrsf.admin.api.entity.SysMenu">
		<id column="menu_id" property="menuId"/>
		<result column="name" property="name"/>
		<result column="permission" property="permission"/>
		<result column="path" property="path"/>
		<result column="parent_id" property="parentId"/>
		<result column="icon" property="icon"/>
		<result column="sort_order" property="sortOrder"/>
		<result column="type" property="type"/>
		<result column="keep_alive" property="keepAlive"/>
		<result column="create_by" property="createBy"/>
		<result column="create_time" property="createTime"/>
		<result column="update_by" property="updateBy"/>
		<result column="update_time" property="updateTime"/>
		<result column="del_flag" property="delFlag"/>
	</resultMap>

	<!--通过角色查询菜单信息-->
	<select id="listMenusByRoleId" resultMap="BaseResultMap">
		SELECT sys_menu.menu_id,
			   sys_menu.name,
			   sys_menu.permission,
			   sys_menu.path,
			   sys_menu.parent_id,
			   sys_menu.icon,
			   sys_menu.sort_order,
			   sys_menu.keep_alive,
			   sys_menu.type,
			   sys_menu.del_flag,
			   sys_menu.create_by,
			   sys_menu.create_time,
			   sys_menu.update_by,
			   sys_menu.update_time,
			   sys_menu.hidden
		FROM sys_menu
					 LEFT JOIN sys_role_menu ON sys_menu.menu_id = sys_role_menu.menu_id
		WHERE sys_menu.del_flag = 0
		  AND sys_role_menu.role_id = #{roleId}
		ORDER BY sys_menu.sort_order DESC
	</select>
</mapper>
