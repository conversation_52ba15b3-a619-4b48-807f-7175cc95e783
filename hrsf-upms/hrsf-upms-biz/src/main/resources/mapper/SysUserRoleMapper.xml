<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.SysUserRoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.bbac.hrsf.admin.api.entity.SysUserRole">
        <id column="user_id" property="userId"/>
        <result column="role_id" property="roleId"/>
    </resultMap>

    <!--根据用户Id删除该用户的角色关系-->
    <delete id="deleteByUserId">
		DELETE FROM sys_user_role WHERE user_id = #{userId}
	</delete>

    <delete id="deleteByRoleId">
		DELETE FROM sys_user_role WHERE role_id = #{roleId}
	</delete>

    <select id="selectByRoleCode" resultType="java.lang.String">
		select a.USER_ID from SYS_USER_ROLE a LEFT JOIN SYS_ROLE b on a.ROLE_ID=b.ROLE_ID
		 where b.ROLE_ID=#{roleCode}
	</select>

</mapper>
