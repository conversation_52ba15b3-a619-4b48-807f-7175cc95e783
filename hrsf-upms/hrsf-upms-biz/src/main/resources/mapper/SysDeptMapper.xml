<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.SysDeptMapper">
	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.bbac.hrsf.admin.api.entity.SysDept">
		<id column="dept_id" property="deptId"/>
		<result column="name" property="name"/>
		<result column="sort_order" property="sortOrder"/>
		<result column="parent_id" property="parentId"/>
		<result column="create_time" property="createTime"/>
		<result column="create_by" property="createBy"/>
		<result column="update_time" property="updateTime"/>
		<result column="update_by" property="updateBy"/>
		<result column="del_flag" property="delFlag"/>
	</resultMap>

	<!--关联查询部门列表-->
	<select id="listDepts" resultMap="BaseResultMap">
		SELECT t.dept_id,
			   t.name,
			   t.sort_order,
			   t.del_flag,
			   t.parent_id,
			   t.create_time,
			   t.create_by,
			   t.update_time,
			   t.update_by
		FROM sys_dept t
					 LEFT JOIN sys_dept_relation dr ON t.dept_id = dr.descendant
		WHERE dr.ancestor = 0
	</select>
</mapper>
