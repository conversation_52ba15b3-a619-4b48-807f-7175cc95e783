<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.HrsfPmsuserBaseMapper">

    <update id="updatePmsLevelById">
        update HRSF_PMSUSER_BASE
        set PMS_LEVEL = NULL
        where
        ID in
        <foreach item="id" collection="cachedDataListTwo" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateFormIdBatchById">
        update HRSF_PMSUSER_BASE
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="user_deeplink =case" suffix="end,">
                <foreach collection="pmsUserBaseList" item="item" index="index">
                    <if test="item.userDeeplink!=null and item.userDeeplink!= ''">
                        when staff_id=#{item.staffId} and assess_year=#{item.assessYear} and assess_type=#{item.assessType} then #{item.userDeeplink}
                    </if>
                </foreach>
            </trim>
            <trim prefix="form_id =case" suffix="end,">
                <foreach collection="pmsUserBaseList" item="item" index="index">
                    <if test="item.formId!=null and item.formId!= ''">
                        when staff_id=#{item.staffId} and assess_year=#{item.assessYear} and assess_type=#{item.assessType} then #{item.formId}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="pmsUserBaseList" separator="or" item="item" index="index">
            staff_id=#{item.staffId} and assess_year=#{item.assessYear} and assess_type=#{item.assessType}
        </foreach>
    </update>
</mapper>
