<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bbac.hrsf.admin.mapper.HrsfPmsUserScoreTaskMapper">
    <!-- 初始化任务明细 -->
    <insert id="initTaskDetail">
        INSERT INTO HRSF_PMS_USER_SCORE_TASK_DETAIL(ID, TASK_ID, STAFF_ID, STAFF_TYPE, STATUS, CREATE_BY, CREATE_TIME)
        SELECT HRSF_PMS_USER_SCORE_TASK_DETAIL_SEQ.NEXTVAL, u.*
        FROM (SELECT #{taskId,jdbcType=BIGINT}, STAFF_ID, BC_WC, 0, #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=DATE} FROM HRSF_USER_BASE
        ) u
    </insert>

    <!-- 批量插入绩效考核复制任务明细 -->
    <insert id="batchInsertTaskDetail">
        INSERT INTO HRSF_PMS_USER_SCORE_TASK_DETAIL(ID, TASK_ID, STAFF_ID, STAFF_TYPE, FORM_DATA_ID, STATUS, CREATE_BY, CREATE_TIME)
        SELECT HRSF_PMS_USER_SCORE_TASK_DETAIL_SEQ.NEXTVAL, t.* FROM (
        <foreach collection="taskDetailList" item="item"  separator="union all">
            select
            #{item.taskId,jdbcType=VARCHAR},
            #{item.staffId,jdbcType=VARCHAR},
            #{item.staffType,jdbcType=INTEGER},
            #{item.formDataId,jdbcType=VARCHAR},
            #{item.taskStatus,jdbcType=INTEGER},
            #{createBy,jdbcType=VARCHAR}, #{createDate,jdbcType=DATE}
            from dual
        </foreach>
        ) t
    </insert>
    <!-- 更新阻塞任务明细状态 -->
    <update id="batchUpdatePendingTaskDetail">
        UPDATE HRSF_PMS_USER_SCORE_TASK_DETAIL
        <set>
            STATUS = 0,
            ERROR_MESSAGE = null,
            UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME = #{updateDate,jdbcType=DATE}
        </set>
        WHERE TASK_ID = #{taskId,jdbcType=BIGINT} AND STATUS = 1
    </update>
    <!-- 更新失败任务明细状态 -->
    <update id="batchUpdateFailedTaskDetail">
        UPDATE HRSF_PMS_USER_SCORE_TASK_DETAIL
        <set>
            STATUS = #{status,jdbcType=INTEGER},
            <if test="status == 0" >
                ERROR_MESSAGE = null,
            </if>
            UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            UPDATE_TIME = #{updateDate,jdbcType=DATE}
        </set>
        WHERE TASK_ID = #{taskId,jdbcType=BIGINT} AND STATUS = 3
    </update>

    <!--查询需要的模板数据-->
    <select id="queryProcessingDetailList" resultMap="ProcessingDetailResultMap">
        SELECT td.ID, td.STAFF_ID, td.STAFF_TYPE, td.FORM_DATA_ID, td.STATUS as TASK_STATUS, td.ERROR_MESSAGE as TASK_ERROR_MESSAGE, td.SF_UPLOAD_TIME
        , bu.bc_wc, bu.FULL_NAME, bu.POSITION, bu.SYSTEM, bu.DEPARTMENT, bu.SECTION, bu.USER_GROUP
        FROM HRSF_PMS_USER_SCORE_TASK_DETAIL td
        LEFT JOIN HRSF_USER_BASE bu ON bu.STAFF_ID=td.STAFF_ID
        WHERE td.STATUS IN
        <foreach item="id" collection="statusArr" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY td.ID
        FETCH FIRST 10 ROWS ONLY
    </select>

    <!-- 分页查询需要的模板数据-->
    <select id="queryScoreTaskDetailListPage" resultMap="ProcessingDetailResultMap">
        SELECT td.ID, td.STAFF_ID, td.STAFF_TYPE, td.FORM_DATA_ID, td.STATUS as TASK_STATUS, td.ERROR_MESSAGE as TASK_ERROR_MESSAGE, td.SF_UPLOAD_TIME
        , bu.bc_wc, bu.FULL_NAME, bu.POSITION, bu.SYSTEM, bu.DEPARTMENT, bu.SECTION, bu.USER_GROUP
        FROM HRSF_PMS_USER_SCORE_TASK_DETAIL td
        LEFT JOIN HRSF_USER_BASE bu ON bu.STAFF_ID=td.STAFF_ID
        ${ew.customSqlSegment}
    </select>
    <!-- 通用查询映射结果 -->
    <resultMap id="ProcessingDetailResultMap" type="com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo">
        <id column="ID" property="id"/>
        <result column="STAFF_TYPE" property="staffType"/>
        <result column="STAFF_ID" property="staffId"/>
        <result column="FORM_DATA_ID" property="formDataId"/>
        <result column="FULL_NAME" property="staffName"/>
        <result column="POSITION" property="position"/>
        <result column="SYSTEM" property="system"/>
        <result column="DEPARTMENT" property="department"/>
        <result column="SECTION" property="section"/>
        <result column="USER_GROUP" property="group"/>
        <!-- 任务辅助信息 -->
        <result column="TASK_STATUS" property="taskStatus"/>
        <result column="TASK_ERROR_MESSAGE" property="taskErrorMessage"/>
        <result column="SF_UPLOAD_TIME" property="sfUploadTime"/>
    </resultMap>

    <!--查询需要的模板数据 季度分数-->
    <select id="queryUserScoreQuarterList" resultMap="UserScoreQuarterResultMap">
        SELECT pu.ID, pu.STAFF_ID, pu.BC_WC, pu.FULL_NAME, pu.ASSESS_YEAR, pu.ASSESS_TYPE, pu.TARGET1_SCORE, pu.TARGET2_SCORE, pu.TARGET3_SCORE, pu.TARGET4_SCORE, pu.TARGET5_SCORE, pu.TARGET6_SCORE, pu.TARGET7_SCORE, pu.TARGET8_SCORE, pu.TARGET9_SCORE, pu.TARGET10_SCORE
        , pu.TOTAL_SCORE, pu.PMS_LEVEL, pu.PMS_LEVEL_DESC, pu.POTENTIAL_LEVEL_DESC, pu.VARIABLE_DESC, pu.USER_LEVEL, pu.FORM_ID
        , CASE WHEN INSTR(pu.EVALUATORS, '|') = 0 THEN pu.EVALUATORS ELSE SUBSTR(pu.EVALUATORS, 1, INSTR(pu.EVALUATORS, '|') - 1) END AS EVALUATOR_1,
        CASE WHEN INSTR(pu.EVALUATORS, '|') > 0 THEN SUBSTR(pu.EVALUATORS, INSTR(pu.EVALUATORS, '|') + 1) END AS EVALUATOR_2, pu.EVALUATORS
        ,bu.bc_wc, bu.STAFF_ID, bu.FULL_NAME, bu.POSITION, bu.SYSTEM, bu.DEPARTMENT, bu.SECTION, bu.USER_GROUP FROM hrsf_pmsuser_base pu
        LEFT JOIN hrsf_user_base bu ON bu.STAFF_ID=pu.STAFF_ID
        WHERE pu.STAFF_ID IN
        <foreach item="id" collection="staffIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND pu.ASSESS_TYPE != 'YEAR'
        <if test="year != 'All' and year != '5Years'" >
            AND pu.ASSESS_YEAR = #{year,jdbcType=VARCHAR}
        </if>
        <if test="assessType != null" >
            AND pu.ASSESS_TYPE = #{assessType,jdbcType=VARCHAR}
        </if>
        ORDER BY pu.STAFF_ID, pu.ASSESS_YEAR, pu.ASSESS_TYPE
    </select>
    <!-- 通用查询映射结果 季度分数 -->
    <resultMap id="UserScoreQuarterResultMap" type="com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTemplateVo">
        <result column="ID" property="id"/>
        <result column="STAFF_ID" property="staffId"/>
        <result column="BC_WC" property="staffType"/>
        <result column="ASSESS_YEAR" property="assessYear"/>
        <result column="ASSESS_TYPE" property="assessType"/>
        <result column="TARGET1_SCORE" property="workQualityScore"/>
        <result column="TARGET2_SCORE" property="costControlScore"/>
        <result column="TARGET3_SCORE" property="completionOfWorkScore"/>
        <result column="TARGET4_SCORE" property="productionSafetyScore"/>
        <result column="TARGET5_SCORE" property="attendanceScore"/>
        <!--<result column="regulation_deducted_remark" property="regulationDeductedRemark"/>-->
        <result column="TOTAL_SCORE" property="finalResultScore"/>
        <result column="PMS_LEVEL" property="performanceCategory"/>
        <result column="PMS_LEVEL_DESC" property="performanceCategoryDesc"/>
        <result column="POTENTIAL_LEVEL_DESC" property="potentialCategory"/>
        <result column="VARIABLE_DESC" property="availability"/>
        <!--<result column="annual_major_accomplishments" property="annualMajorAccomplishments"/>-->
        <result column="USER_LEVEL" property="staffLevel"/>
        <result column="FORM_ID" property="formId"/>
        <result column="EVALUATOR_1" property="evaluator1"/>
        <result column="EVALUATOR_2" property="evaluator2"/>
    </resultMap>

    <!--查询需要的模板数据 年度分数-->
    <select id="queryUserScoreYearList" resultMap="UserScoreYearResultMap">
        SELECT pu.ID, pu.STAFF_ID, pu.BC_WC, pu.FULL_NAME, pu.ASSESS_YEAR, pu.ASSESS_TYPE, pu.TARGET1_SCORE, pu.TARGET2_SCORE, pu.TARGET3_SCORE, pu.TARGET4_SCORE, pu.TARGET5_SCORE, pu.TARGET6_SCORE, pu.TARGET7_SCORE, pu.TARGET8_SCORE, pu.TARGET9_SCORE, pu.TARGET10_SCORE
        , pu.TOTAL_SCORE, pu.PMS_LEVEL, pu.PMS_LEVEL_DESC, pu.POTENTIAL_LEVEL_DESC, pu.VARIABLE_DESC, pu.USER_LEVEL, pu.FORM_ID
        , CASE WHEN INSTR(pu.EVALUATORS, '|') = 0 THEN pu.EVALUATORS ELSE SUBSTR(pu.EVALUATORS, 1, INSTR(pu.EVALUATORS, '|') - 1) END AS EVALUATOR_1,
        CASE WHEN INSTR(pu.EVALUATORS, '|') > 0 THEN SUBSTR(pu.EVALUATORS, INSTR(pu.EVALUATORS, '|') + 1) END AS EVALUATOR_2, pu.EVALUATORS
        ,bu.bc_wc, bu.STAFF_ID, bu.FULL_NAME, bu.POSITION, bu.SYSTEM, bu.DEPARTMENT, bu.SECTION, bu.USER_GROUP FROM hrsf_pmsuser_base pu
        LEFT JOIN hrsf_user_base bu ON bu.STAFF_ID=pu.STAFF_ID
        WHERE pu.STAFF_ID IN
        <foreach item="id" collection="staffIdList" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND pu.ASSESS_TYPE = 'YEAR'
        <if test="year != 'All' and year != '5Years'" >
            AND pu.ASSESS_YEAR = #{year,jdbcType=VARCHAR}
        </if>
        ORDER BY pu.STAFF_ID, pu.ASSESS_YEAR, pu.ASSESS_TYPE
    </select>
    <!-- 通用查询映射结果 年度分数 -->
    <resultMap id="UserScoreYearResultMap" type="com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTemplateVo">
        <result column="ID" property="id"/>
        <result column="STAFF_ID" property="staffId"/>
        <result column="BC_WC" property="staffType"/>
        <result column="ASSESS_YEAR" property="assessYear"/>
        <result column="ASSESS_TYPE" property="assessType"/>
        <result column="TARGET2_SCORE" property="workQualityScore"/>
        <result column="TARGET5_SCORE" property="costControlScore"/>
        <result column="TARGET1_SCORE" property="completionOfWorkScore"/>
        <result column="TARGET3_SCORE" property="productionSafetyScore"/>
        <result column="TARGET4_SCORE" property="attendanceScore"/>
        <result column="TARGET6_SCORE" property="communicationScore"/>
        <result column="TARGET7_SCORE" property="teamWorkScore"/>
        <result column="TARGET8_SCORE" property="professionalScore"/>
        <result column="TARGET9_SCORE" property="responsibilityScore"/>
        <result column="TARGET10_SCORE" property="disciplineScore"/>
        <!--<result column="regulation_deducted_remark" property="regulationDeductedRemark"/>-->
        <result column="TOTAL_SCORE" property="finalResultScore"/>
        <result column="PMS_LEVEL" property="performanceCategory"/>
        <result column="PMS_LEVEL_DESC" property="performanceCategoryDesc"/>
        <result column="POTENTIAL_LEVEL_DESC" property="potentialCategory"/>
        <result column="VARIABLE_DESC" property="availability"/>
        <!--<result column="annual_major_accomplishments" property="annualMajorAccomplishments"/>-->
        <result column="USER_LEVEL" property="staffLevel"/>
        <result column="FORM_ID" property="formId"/>
        <result column="EVALUATOR_1" property="evaluator1"/>
        <result column="EVALUATOR_2" property="evaluator2"/>
    </resultMap>

    <!--获取任务详情计数-->
    <select id="obtainTaskDetailCounting" resultMap="ObtainTaskDetailCountingResultMap">
        SELECT COUNT(1) as taskDetailCount, MAX(ID) as taskDetailMaxId
        FROM HRSF_PMS_USER_SCORE_TASK_DETAIL
        WHERE STATUS=0 AND TASK_ID=#{taskId,jdbcType=BIGINT}
    </select>
    <!-- 通用查询映射结果 -->
    <resultMap id="ObtainTaskDetailCountingResultMap" type="com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskCountingVo">
        <result column="taskDetailCount" property="taskDetailCount"/>
        <result column="taskDetailMaxId" property="taskDetailMaxId"/>
    </resultMap>

    <!-- 批量更新任务状态 -->
    <update id="batchUpdateStatus" >
        <foreach collection="taskDetailList" item="item" index="index" open="begin" close=";end;" separator=";">
            UPDATE HRSF_PMS_USER_SCORE_TASK_DETAIL
            <set >
                <if test="item.sfUploadTime != null" >
                    SF_UPLOAD_TIME = #{item.sfUploadTime,jdbcType=DATE},
                </if>
                <if test="item.taskErrorMessage != null" >
                    ERROR_MESSAGE = #{item.taskErrorMessage,jdbcType=VARCHAR},
                </if>
                <if test="item.taskStatus != null" >
                    STATUS = #{item.taskStatus,jdbcType=INTEGER},
                </if>
                <if test="updateBy != null" >
                    UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
                </if>
                <if test="updateDate != null" >
                    UPDATE_TIME = #{updateDate,jdbcType=DATE},
                </if>
            </set>
            WHERE ID = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <!-- 更新任务状态 -->
    <update id="updateStatus" >
        UPDATE HRSF_PMS_USER_SCORE_TASK_DETAIL
        <set>
            <if test="taskDetail.sfUploadTime != null" >
                SF_UPLOAD_TIME = #{taskDetail.sfUploadTime,jdbcType=DATE},
            </if>
            <if test="taskDetail.taskErrorMessage != null" >
                ERROR_MESSAGE = #{taskDetail.taskErrorMessage,jdbcType=VARCHAR},
            </if>
            <if test="taskDetail.taskStatus != null" >
                STATUS = #{taskDetail.taskStatus,jdbcType=INTEGER},
            </if>
            <if test="updateBy != null" >
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                UPDATE_TIME = #{updateDate,jdbcType=DATE},
            </if>
        </set>
        WHERE ID = #{taskDetail.id,jdbcType=BIGINT}
    </update>

    <!-- 更新能力评估的年度小分 -->
    <update id="updateCompetenciesYearScore" >
        UPDATE HRSF_PMSUSER_BASE
        <set>
            <if test="templateVo.completionOfWorkScore != null" >
                TARGET1_SCORE = #{templateVo.completionOfWorkScore},
            </if>
            <if test="templateVo.workQualityScore != null" >
                TARGET2_SCORE = #{templateVo.workQualityScore},
            </if>
            <if test="templateVo.productionSafetyScore != null" >
                TARGET3_SCORE = #{templateVo.productionSafetyScore},
            </if>
            <if test="templateVo.attendanceScore != null" >
                TARGET4_SCORE = #{templateVo.attendanceScore},
            </if>
            <if test="templateVo.costControlScore != null" >
                TARGET5_SCORE = #{templateVo.costControlScore},
            </if>
            <if test="templateVo.communicationScore != null" >
                TARGET6_SCORE = #{templateVo.communicationScore},
            </if>
            <if test="templateVo.teamWorkScore != null" >
                TARGET7_SCORE = #{templateVo.teamWorkScore},
            </if>
            <if test="templateVo.professionalScore != null" >
                TARGET8_SCORE = #{templateVo.professionalScore},
            </if>
            <if test="templateVo.responsibilityScore != null" >
                TARGET9_SCORE = #{templateVo.responsibilityScore},
            </if>
            <if test="templateVo.disciplineScore != null" >
                TARGET10_SCORE = #{templateVo.disciplineScore},
            </if>
            <if test="updateBy != null" >
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                UPDATE_TIME = #{updateDate,jdbcType=DATE},
            </if>
        </set>
        WHERE ID = #{templateVo.id,jdbcType=BIGINT}
    </update>

    <!-- 更新能力评估的季度小分 -->
    <update id="updateCompetenciesQuarterScore" >
        UPDATE HRSF_PMSUSER_BASE
        <set>
            <if test="templateVo.workQualityScore != null" >
                TARGET1_SCORE = #{templateVo.workQualityScore},
            </if>
            <if test="templateVo.costControlScore != null" >
                TARGET2_SCORE = #{templateVo.costControlScore},
            </if>
            <if test="templateVo.completionOfWorkScore != null" >
                TARGET3_SCORE = #{templateVo.completionOfWorkScore},
            </if>
            <if test="templateVo.productionSafetyScore != null" >
                TARGET4_SCORE = #{templateVo.productionSafetyScore},
            </if>
            <if test="templateVo.attendanceScore != null" >
                TARGET5_SCORE = #{templateVo.attendanceScore},
            </if>
            <if test="updateBy != null" >
                UPDATE_BY = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateDate != null" >
                UPDATE_TIME = #{updateDate,jdbcType=DATE},
            </if>
        </set>
        WHERE ID = #{templateVo.id,jdbcType=BIGINT}
    </update>

</mapper>
