<script type="text/javascript">
    // Basic Information
    let basicInformation = ###basicInformation###

    let basicInformationHtml = `
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                员工姓名
            </div>
            <div class="information-msg-left-en">
                Name
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.name}
            </div>
            <div class="information-msg-right-en">
                ${basicInformation.nameEn}
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                工号
            </div>
            <div class="information-msg-left-en">
                Employee ID
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.userId}
            </div>
            <div class="information-msg-right-en">
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                年龄
            </div>
            <div class="information-msg-left-en">
                Age
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.age}
            </div>
            <div class="information-msg-right-en">
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                最高学位
            </div>
            <div class="information-msg-left-en">
                Highest Degree
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.levelNav}
            </div>
            <div class="information-msg-right-en">
                ${basicInformation.levelNavEn}
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                主管姓名
            </div>
            <div class="information-msg-left-en">
                Manager
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.manager}
            </div>
            <div class="information-msg-right-en">
                ${basicInformation.managerEn}
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                现职位工作时长(年)
            </div>
            <div class="information-msg-left-en">
                Length of Service on Current Position (yrs)
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.addressLine1}
            </div>
            <div class="information-msg-right-en">
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                BBAC工作时长(年)
            </div>
            <div class="information-msg-left-en">
                Length of Service in BBAC (yrs)
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.addressLine2}
            </div>
            <div class="information-msg-right-en">
            </div>
        </div>
    </div>
    <div class="information-msg-box">
        <div class="information-msg-left">
            <div class="information-msg-left-zh">
                现级别工作时长(年)
            </div>
            <div class="information-msg-left-en">
                Length of Service on Current Level (yrs)
            </div>
        </div>
        <div class="information-msg-right">
            <div class="information-msg-right-zh">
                ${basicInformation.addressLine3}
            </div>
            <div class="information-msg-right-en">
            </div>
        </div>
    </div>
`
    document.getElementsByClassName('basic-information-msg')[0].insertAdjacentHTML('beforeend', basicInformationHtml)

    // position-information-msg
    let positionInformation = ###positionInformation###
    let positionInformationHtml = `
    <div class="po-information-msg-box">
        <div class="po-information-msg-left">
            <div class="information-msg-left-zh">
                岗位
            </div>
            <div class="information-msg-left-en">
                Position
            </div>
        </div>
        <div class="po-information-msg-right">
            <div class="information-msg-right-zh">
                ${positionInformation.position}
            </div>
            <div class="information-msg-right-en">
                ${positionInformation.positionEn}
            </div>
        </div>
    </div>
    <div class="po-information-msg-box">
        <div class="po-information-msg-left">
            <div class="information-msg-left-zh">
                系统
            </div>
            <div class="information-msg-left-en">
                System
            </div>
        </div>
        <div class="po-information-msg-right">
            <div class="information-msg-right-zh">
                ${positionInformation.system}
            </div>
            <div class="information-msg-right-en">
                ${positionInformation.systemEn}
            </div>
        </div>
    </div>
    <div class="po-information-msg-box">
        <div class="po-information-msg-left">
            <div class="information-msg-left-zh">
                部门
            </div>
            <div class="information-msg-left-en">
                Department
            </div>
        </div>
        <div class="po-information-msg-right">
            <div class="information-msg-right-zh">
                ${positionInformation.department}
            </div>
            <div class="information-msg-right-en">
                ${positionInformation.departmentEn}
            </div>
        </div>
    </div>
    <div class="po-information-msg-box">
        <div class="po-information-msg-left">
            <div class="information-msg-left-zh">
                科室
            </div>
            <div class="information-msg-left-en">
                Section
            </div>
        </div>
        <div class="po-information-msg-right">
            <div class="information-msg-right-zh">
                ${positionInformation.section}
            </div>
            <div class="information-msg-right-en">
                ${positionInformation.sectionEn}
            </div>
        </div>
    </div>
    <div class="po-information-msg-box">
        <div class="po-information-msg-left">
            <div class="information-msg-left-zh">
                工段/组
            </div>
            <div class="information-msg-left-en">
                Group
            </div>
        </div>
        <div class="po-information-msg-right">
            <div class="information-msg-right-zh">
                ${positionInformation.group}
            </div>
            <div class="information-msg-right-en">
                ${positionInformation.groupEn}
            </div>
        </div>
    </div>
`
    document.getElementsByClassName('position-information-msg')[0].insertAdjacentHTML('beforeend', positionInformationHtml)

    // 能力类型数据 中文和英文用 \n 进行分隔
    // 雷达图数据
    let cerDataArr = ###cerDataArr###
    let a = []
    // 类别数据
    let cerData = [];
    // 能力标准   competenciesStandardValue
    let competenciesStandardValue = [];
    // 能力评分   competenciesScoreValue
    let competenciesScoreValue = [];
    // 能力分数平均分计算
    let competenciesScore = '###competenciesScore###';

    cerDataArr && cerDataArr.map((item, index) => {
        // 能力类型数据
        cerData.push({
            name: item.competencyNameCn,
            max: 5
        })
        competenciesStandardValue.push(item.scoreStandard)
        competenciesScoreValue.push(item.score)
    })
    /*a.forEach((item, index) => {
        switch (item) {
            case '0':
                competenciesStandardValue.push('1')
                break;
            case '25':
                competenciesStandardValue.push('2')
                break;
            case '50':
                competenciesStandardValue.push('3')
                break;
            case '75':
                competenciesStandardValue.push('4')
                break;
            default:
                competenciesStandardValue.push('5')
        }
    })*/


    // 能力评价结果 echarts 图表
    window.onload = function () {
        //指定图表的配置项和数据
        option = {
            // backgroundColor: '#fff',//背景颜色
            color: ['rgba(84,150,205,0.3500)', 'rgba(182,217,87,0.3500)'],//数据蛛网线条的颜色，示例中为红黄绿
            title: {
                text: '能力分数  Competencies Score',//统计图标题
                subtext: competenciesScore,
                left: 'center',
                top: '72%',
                textStyle: {
                    color: '#333333',//统计图标题的文字颜色
                    fontSize: 20,
                    fontFamily: 'HYZHongSongJ-regular, HYZHongSongJ',
                    fontWeight: 400,
                    lineHeight: 32,
                },
                subtextStyle: {
                    fontSize: 28,
                    fontFamily: "Microsoft YaHei-Bold, Microsoft YaHei",
                    fontWeight: "bold",
                    color: "#333333",
                }
            },
            legend: { //图例的设置
                show: true, //是否显示图例
                icon: 'rect',//图例形状
                top: '82%',//图例离底部的距离
                itemWidth: 16, // 图例标记的图形宽度。
                itemHeight: 16, // 图例标记的图形高度。
                itemGap: 20, // 图例每项之间的间隔。
                textStyle: {//图例文字的样式设置
                    fontSize: 18,
                    color: '#666666',
                    fontFamily: "CorpoS-Regular, CorpoS"
                },
                data: ['能力标准 Competencies Standard', '能力评分 Competencies Score'],//图例的名称数据
            },
            radar: [{//每个网格的指数名称，类似于X轴或Y轴上的数据的值大小
                indicator: cerData,
                center: ['50%', '40%'],//统计图位置，示例是居中
                radius: '50%',//统计图大小
                startAngle: 90,//统计图起始的角度
                splitNumber: 5,//统计图蛛网的网格分段，示例分为三段
                name: {
                    formatter: '{value}',//蛛网轴尖的数据名称
                    textStyle: {//蛛网轴尖的文字样式
                        fontSize: 12, //外圈标签字体大小
                        color: '#333333', //外圈标签字体颜色
                        padding: [12, 12, 12, 12]
                    }
                },
                splitArea: { // 蛛网在 grid 区域中的分隔区域，默认不显示。
                    show: false
                },
                axisLine: { //蛛网轴线上的颜色，由内向外发散的那条
                    show: false
                },
                splitLine: {//蛛网环形的线条颜色
                    lineStyle: {
                        color: '#DEDEDE', // 分隔线颜色
                        width: 1, // 分隔线线宽
                    }
                }
            },],
            series: [{
                name: '',
                type: 'radar',//统计图专业名称为雷达图，这里叫做蛛网图
                itemStyle: {//数据样式设置
                    emphasis: {//鼠标悬浮效果
                        lineStyle: {
                            width: 1//线条粗细变成4
                        }
                    }
                },
                data: [{
                    name: '能力标准 Competencies Standard',//数据名称
                    value: competenciesStandardValue,
                    areaStyle: {
                        shadowColor: 'rgb(84,150,205)',
                        // opacity: '0.1500'
                    },
                    symbolSize: 2.5, // 单个数据标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [20, 10] 表示标记宽为20，高为10。
                    label: { // 单个拐点文本的样式设置
                        normal: { // 单个拐点文本的样式设置。[ default: false ]
                            show: true,
                            color: '#333', // 文字的颜色。如果设置为 'auto'，则为视觉映射得到的颜色，如系列色。[ default: "#fff" ]
                            position: 'inside', // 标签的位置。[ default: top ]
                            // distance: 20, // 距离图形元素的距离。当 position 为字符描述值（如 'top'、'insideRight'）时候有效。[ default: 5 ]
                            fontSize: 14, // 文字的字体大小
                        },
                        emphasis: {
                            show: true,
                            formatter: '{c}'//显示分析的数字值，a为统计图名称，b为学生姓名,c为学生这项能力的值
                        }
                    },
                    itemStyle: {
                        normal: { //图形悬浮效果
                            borderColor: '#ccc',//单个数据标记描边的颜色
                            borderWidth: 2.5//单个数据标记描边的大小
                        }
                    },
                }, {
                    name: '能力评分 Competencies Score',
                    value: competenciesScoreValue,
                    symbolSize: 2.5,
                    itemStyle: {
                        normal: {
                            borderColor: '#ccc',
                            borderWidth: 2.5,
                        }
                    },
                    areaStyle: {
                        shadowColor: 'rgb(182,217,87)',
                        // opacity: '0.1500'
                    },
                    label: {
                        normal: {
                            show: true,
                            color: '#333',
                            position: 'inside',
                            distance: 0,
                            fontSize: 14,
                        },
                        emphasis: {
                            show: true,
                            formatter: '{c}'
                        }
                    },
                }]
            },]
        };
        //获取dom容器
        var myChart = echarts.init(document.getElementById('cerEchartsBox'));
        // 使用刚指定的配置项和数据显示图表。
        myChart.setOption(option);
    }

    // 4.6.4	能力条目展示 相关数据
    let abilityToEntryDate = ###abilityToEntryDate###;
    let abilityToEntryHtml = ``;
    let leadershipPrinciplesHtml = ``;
    abilityToEntryDate.length > 0 && abilityToEntryDate.map((itemP, indexP) => {
        let competencyCategory = itemP.competencyCategory;
        let competencyCategoryEn = itemP.competencyCategoryEn;
        let children = itemP.children
        abilityToEntryHtml = `
        <div class="leadership-principles">
            <div class="lp-title">
                <div class="lp-title-text">
                    <span class="lp-title-text-zh">${competencyCategory}</span>
                    <span class="lp-title-text-en">${competencyCategoryEn}</span>
                </div>
                <div class="lp-title-line">
                    <div class="lp-title-line-left"></div>
                </div>
            </div>
            <div class="lp-box">
            </div>
        </div>
    `
        document.getElementsByClassName('ability-to-entry')[0].insertAdjacentHTML('beforeend', abilityToEntryHtml)
        children.length > 0 && children.map((item, index) => {
            // 能力评分   这个样式是不变的
            let myAbilityScoreClassName = ''

            // 能力标准  样式会改变
            let myCompetencyStandardsClassName = ''
            switch (item.scoreStandard) {
                case "1":
                    myCompetencyStandardsClassName = "ability-score-box-1";
                    break;
                case "2":
                    myCompetencyStandardsClassName = "ability-score-box-2";
                    break;
                case "3":
                    myCompetencyStandardsClassName = "ability-score-box-3";
                    break;
                case "4":
                    myCompetencyStandardsClassName = "ability-score-box-4";
                    break;
                case "5":
                    myCompetencyStandardsClassName = "ability-score-box-5";
                    break;
            }
            if (Number(item.score) > Number(item.scoreStandard)) {
                switch (item.score) {
                    case "1":
                        myAbilityScoreClassName = "competency-standards-box-1";
                        break;
                    case "2":
                        myAbilityScoreClassName = "competency-standards-box-2";
                        break;
                    case "3":
                        myAbilityScoreClassName = "competency-standards-box-3";
                        break;
                    case "4":
                        myAbilityScoreClassName = "competency-standards-box-4";
                        break;
                    case "5":
                        myAbilityScoreClassName = "competency-standards-box-5";
                        break;
                }
                leadershipPrinciplesHtml += `
             <div class="lp-box-one">
                 <div class="lp-box-title">
                     <div class="lp-box-title-top">
                         ${item.competencyNameCn}
                     </div>
                     <div class="lp-box-title-bottom">
                         ${item.competencyNameEn}
                     </div>
                 </div>
                 <!--                评分条-->
                    <!--                边框-->
                <div class="lp-box-rating-bar">
                    <!--                    能力标准-->
                <div class="competency-standards-box ${myCompetencyStandardsClassName}">
                </div>
                    <!--                    能力评分-->
                <div class="ability-score-box ${myAbilityScoreClassName}"></div>
                </div>
                    <!--                刻度-->
                <div class="my-ruler">
                <div class="ruler-dial1">
                </div>
                <div class="ruler-dial1">
                </div>
                <div class="ruler-dial1">
                </div>
                <div class="ruler-dial4">
                </div>
                </div>
                <div class="my-ruler-number">
                <span ${item.score === "1" ? 'class="my-ruler-number-color"' : ''}>1</span>
                <span ${item.score === "2" ? 'class="my-ruler-number-color"' : ''}>2</span>
                <span ${item.score === "3" ? 'class="my-ruler-number-color"' : ''}>3</span>
                <span ${item.score === "4" ? 'class="my-ruler-number-color"' : ''}>4</span>
                <span ${item.score === "5" ? 'class="my-ruler-number-color"' : ''}>5</span>
                </div>
                </div>
                `
            } else {
                switch (item.score) {
                    case "1":
                        myAbilityScoreClassName = "competency-standards-box-small-1";
                        break;
                    case "2":
                        myAbilityScoreClassName = "competency-standards-box-small-2";
                        break;
                    case "3":
                        myAbilityScoreClassName = "competency-standards-box-small-3";
                        break;
                    case "4":
                        myAbilityScoreClassName = "competency-standards-box-small-4";
                        break;
                    case "5":
                        myAbilityScoreClassName = "competency-standards-box-small-5";
                        break;
                }
                leadershipPrinciplesHtml += `
             <div class="lp-box-one">
                 <div class="lp-box-title">
                     <div class="lp-box-title-top">
                         ${item.competencyNameCn}
                     </div>
                     <div class="lp-box-title-bottom">
                         ${item.competencyNameEn}
                     </div>
                 </div>
                 <!--                评分条-->
                    <!--                边框-->
                <div class="lp-box-rating-bar">
                    <!--                    能力标准-->
                <div class="competency-standards-box-small ${myCompetencyStandardsClassName}">
                </div>
                    <!--                    能力评分-->
                <div class="ability-score-box ${myAbilityScoreClassName}"></div>
                </div>
                    <!--                刻度-->
                <div class="my-ruler">
                <div class="ruler-dial1">
                </div>
                <div class="ruler-dial1">
                </div>
                <div class="ruler-dial1">
                </div>
                <div class="ruler-dial4">
                </div>
                </div>
                <div class="my-ruler-number">
                <span ${item.score === "1" ? 'class="my-ruler-number-color"' : ''}>1</span>
                <span ${item.score === "2" ? 'class="my-ruler-number-color"' : ''}>2</span>
                <span ${item.score === "3" ? 'class="my-ruler-number-color"' : ''}>3</span>
                <span ${item.score === "4" ? 'class="my-ruler-number-color"' : ''}>4</span>
                <span ${item.score === "5" ? 'class="my-ruler-number-color"' : ''}>5</span>
                </div>
                </div>
                `
            }
        })
        document.getElementsByClassName('lp-box')[indexP].insertAdjacentHTML('beforeend', leadershipPrinciplesHtml)
        leadershipPrinciplesHtml = ``
    })
    // 过往人才盘点结果
    // needsCoaching /需要辅导	  1
    // emergingStar/新星	 2
    // star/明星  3
    // marginalPerformer/潜力达标者	 4
    // solidContributor/干将	5
    // risingStar/后起之秀   6
    // underPerformer/绩效较差者	   7
    // acceptablePerformer/绩效较好者   8
    // experiencedProfessional/经验丰富的专业人员  9
    let matrix1Label = '###matrix1Label###'
    let matrix1LabelName='###matrix1LabelName###'
    if(Number(matrix1Label)!==0){
        document.getElementsByClassName('sudoku-box-' + `${matrix1Label}`)[0].classList.add('my-sudoku-box-true')
        document.getElementsByClassName('sudoku-title')[0].innerText=matrix1LabelName
    }





    // 评价中心测评结果
    let assessmentCenterEvaluationResultData = ###assessmentCenterEvaluationResultData###;
    let assessmentCenterEvaluationResultDataHtml = ``
    assessmentCenterEvaluationResultData.length > 0 && assessmentCenterEvaluationResultData.map((item, index) => {
        assessmentCenterEvaluationResultDataHtml += `
        <div class="a-center-ev-r">
            <div class="a-center-ev-r-left">
                <div class="a-center-ev-r-left-l">
                    <div class="a-center-ev-r-left-l-zh" style='width: 48px;'>类型</div>
                    <div class="a-center-ev-r-left-l-en">Type</div>
                </div>
                <div class="a-center-ev-r-left-r">
                    <div class="a-center-ev-r-left-r-zh">${item.evaluationType}</div>
                    <div class="a-center-ev-r-left-r-en">${item.evaluationTypeEN}</div>
                </div>
            </div>
            <div class="a-center-ev-r-center">
                <div class="a-center-ev-r-center-l">
                    <div class="a-center-ev-r-center-l-zh">评估结果</div>
                    <div class="a-center-ev-r-center-l-en" style='width: 180px;'>Evaluation Result</div>
                </div>
                <div class="a-center-ev-r-right-r">
                    <div class="a-center-ev-r-center-r-zh">${item.evaluationResult}</div>
                    <div class="a-center-ev-r-center-r-en">${item.evaluationResultEN}</div>
                </div>
            </div>
            <div class="a-center-ev-r-right">
                <div class="a-center-ev-r-right-l">
                    <div class="a-center-ev-r-right-l-zh">评估日期</div>
                    <div class="a-center-ev-r-right-l-en">Evaluation Date</div>
                </div>
                <div class="a-center-ev-r-right-r">
                    <div class="a-center-ev-r-right-r-en">${item.evaluationDate}</div>
                </div>
            </div>
        </div>
    `
    })
    document.getElementsByClassName('a-center-ev-r-box')[0].insertAdjacentHTML('beforeend', assessmentCenterEvaluationResultDataHtml)

    // 参与的重点项目
    let keyProjectsInvolvedData = ###keyProjectsInvolvedData###;

    let keyProjectsInvolvedHtmlHeader = ``;
    keyProjectsInvolvedData.length > 0 && keyProjectsInvolvedData.map((item, index) => {
        keyProjectsInvolvedHtmlHeader += `
        <div class="key-projects-involved-box">
            <div class="key-projects-involved-box-header">
                <div class="key-projects-involved-box-header-left">
                    <div class="key-projects-involved-box-pro-left">
                        项目<br>
                        Project
                    </div>
                </div>
                <div class="key-projects-involved-box-header-right">
                    <div class="key-projects-involved-box-pro-right">
                        参与项目日期<br>
                        Date of Participation
                    </div>
                </div>
            </div>
            <div class="key-projects-involved-box-main">

            </div>
        </div>
    `
    })
    document.getElementsByClassName('key-projects-involved')[0].insertAdjacentHTML('beforeend', keyProjectsInvolvedHtmlHeader)
    if (document.getElementsByClassName('key-projects-involved-box').length === 2) {
        document.getElementsByClassName('key-projects-involved-box')[0].classList.add('key-projects-involved-box-0')
        document.getElementsByClassName('key-projects-involved-box')[1].classList.add('key-projects-involved-box-1')
    }
    let keyProjectsInvolvedHtml = ``;
    let myHeight = ''
    keyProjectsInvolvedData.length > 0 && keyProjectsInvolvedData.map((item, index) => {
        item.map((itemC, indexC) => {
            keyProjectsInvolvedHtml += `
            <div class="key-projects-involved-box-main-h">
                <div class="key-projects-involved-box-main-left">
                    <div class="key-projects-involved-box-pro-left">
                        ${itemC?.project?itemC?.project:'暂无数据<br>No data'}<br>
                        ${itemC.projectEN}
                    </div>
                </div>
                <div class="key-projects-involved-box-main-right">
                    <div class="key-projects-involved-box-pro-right">
                        ${itemC.participateDate}
                    </div>
                </div>
            </div>
        `
            myHeight = (84 + (indexC + 1) * 94) + 'px'

        })
        document.getElementsByClassName('key-projects-involved-box-' + `${index}`)[0].style.height = myHeight
        document.getElementsByClassName('key-projects-involved-box-main')[index].insertAdjacentHTML('beforeend', keyProjectsInvolvedHtml)
        keyProjectsInvolvedHtml = ``
        myHeight = ''
    })


    // 本岗位本年度培训记录
    let courseRecordPositionData = ###courseRecordPositionData###
    let courseRecordPositionHtml = ``
    courseRecordPositionData.length > 0 && courseRecordPositionData.map((item, index) => {
        courseRecordPositionHtml += `
        <div class="course-record-position-bottom-box">
            <div class="start-date">
                <div class="start-date-zh course-record-position-zh">${item.startDate ? item.startDate : '暂无数据<br>No data'}</div>
            </div>
            <div class="end-date">
                <div class="end-date-zh course-record-position-zh">${item.endDate}</div>
            </div>
            <div class="course">
                <div class="course-zh course-record-position-zh">${item.course}</div>
                <div class="course-en course-record-position-en">${item.courseEN}</div>
            </div>
            <div class="status">
                <div class="status-zh course-record-position-zh">${item.status}</div>
                <div class="status-en course-record-position-en">${item.statusEN}</div>
            </div>
        </div>
    `
    })
    document.getElementsByClassName('course-record-position-bottom')[0].insertAdjacentHTML('beforeend', courseRecordPositionHtml)
</script>
</body>
</html>