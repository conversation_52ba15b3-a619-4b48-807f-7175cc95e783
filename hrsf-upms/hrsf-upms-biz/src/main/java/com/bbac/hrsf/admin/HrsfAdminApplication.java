/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin;

import com.bbac.hrsf.common.feign.annotation.EnableHrsfFeignClients;
import com.bbac.hrsf.common.security.annotation.EnableHrsfResourceServer;
import com.bbac.hrsf.common.swagger.annotation.EnableHrsfSwagger2;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * <AUTHOR>
 * @date 2018年06月21日 用户统一管理系统
 */
//@EnableHrsfSwagger2
@EnableHrsfResourceServer
@EnableHrsfFeignClients
@EnableDiscoveryClient
@SpringBootApplication
public class HrsfAdminApplication {

    public static void main(String[] args) {
        SpringApplication.run(HrsfAdminApplication.class, args);
    }

}
