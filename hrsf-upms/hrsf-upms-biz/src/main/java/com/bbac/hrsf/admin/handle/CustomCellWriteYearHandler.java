package com.bbac.hrsf.admin.handle;

import com.alibaba.excel.context.WriteContext;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.DefaultIndexedColorMap;
import org.apache.poi.xssf.usermodel.XSSFColor;

import java.util.List;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.handle.CustomSheetWriteHandler</li>
 * <li>CreateTime : 2022/04/12 17:17:41</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class CustomCellWriteYearHandler implements CellWriteHandler {


    @Override
    public void beforeCellCreate(CellWriteHandlerContext context) {
        Row row = context.getRow();
        row.setHeight((short) 700);
        Sheet sheet = row.getSheet();
        sheet.createFreezePane(0, 1, 0, 1);
        //锁定影响删除行
//        sheet.protectSheet("模板");
        sheet.setColumnWidth(0, (int) ((10 + 0.728) * 256));
        sheet.setColumnWidth(1, (int) ((20 + 0.728) * 256));
        int i = (int) ((40 + 0.728) * 256);
        sheet.setColumnWidth(2, i);
        sheet.setColumnWidth(3, i);
        sheet.setColumnWidth(4, i);
        int j = (int) ((15 + 0.728) * 256);
        sheet.setColumnWidth(5, j);
        sheet.setColumnWidth(6, j);
        sheet.setColumnWidth(7, j);
        sheet.setColumnWidth(8, j);
        sheet.setColumnWidth(9, j);
        sheet.setColumnWidth(10, j);
        sheet.setColumnWidth(11, j);
        sheet.setColumnWidth(12, j);
        sheet.setColumnWidth(13, j);
        sheet.setColumnWidth(14, j);
        sheet.setColumnWidth(15, j);
        sheet.setColumnWidth(16, j);
        sheet.setColumnWidth(17, j);
        sheet.setColumnWidth(18, j);
        sheet.setColumnWidth(19, j);
        sheet.setColumnWidth(20, (int) ((25 + 0.728) * 256));
        sheet.setColumnWidth(21, j);
        sheet.setColumnWidth(22, j);
        sheet.setColumnWidth(23, j);
        sheet.setColumnWidth(24, j);
        sheet.setColumnWidth(25, j);
        CellWriteHandler.super.beforeCellCreate(context);
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        Cell cell = context.getCell();
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();

        if (rowIndex == 0) {
//            WriteSheetHolder writeSheetHolder = context.getWriteSheetHolder();
//            Sheet sheet = writeSheetHolder.getSheet();
//            sheet.setDefaultColumnStyle(columnIndex, valueCellStyle(context));
            getHeaderCellStyle(context);
        } else {
            getValueCellStyle(context);
        }
        CellWriteHandler.super.afterCellDispose(context);
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {


        /**
         * 设置锁定单元格
         *
         * 取消锁定单元格逻辑
         * 2022-06-07 update
         *
         * if (cell.getColumnIndex() == 0 || cell.getColumnIndex() == 1 ||
         *                 cell.getColumnIndex() == 6 || cell.getColumnIndex() == 8 ||
         *                 cell.getColumnIndex() == 10 || cell.getColumnIndex() == 12 ||
         *                 cell.getColumnIndex() == 14 ||
         *                 cell.getColumnIndex() == 16 || cell.getColumnIndex() == 18 ||
         *                 cell.getColumnIndex() == 20 ||
         *                 cell.getColumnIndex() == 22 || cell.getColumnIndex() == 24 ||
         *                 cell.getColumnIndex() == 26 || cell.getColumnIndex() == 27 ||
         *                 cell.getColumnIndex() == 28 || cell.getColumnIndex() == 29 ||
         *                 cell.getColumnIndex() == 30 || cell.getColumnIndex() == 31 ||
         *                 cell.getColumnIndex() == 32 || cell.getColumnIndex() == 33 ||
         *                 cell.getColumnIndex() == 34 || cell.getColumnIndex() == 35|| cell.getColumnIndex() == 36) {
         *             cellDataList.stream().forEach(o -> {
         *                 WriteCellStyle writeCellStyle = o.getWriteCellStyle();
         *                 writeCellStyle.setLocked(true);
         *                 o.setWriteCellStyle(writeCellStyle);
         *             });
         *         } else {
         *             // 设置锁定单元格
         *             cellDataList.stream().forEach(o -> {
         *                 WriteCellStyle writeCellStyle = o.getWriteCellStyle();
         *                 writeCellStyle.setLocked(false);
         *                 o.setWriteCellStyle(writeCellStyle);
         *             });
         *         }
         */


    }

    private void getHeaderCellStyle(CellWriteHandlerContext context) {

        WriteCellData<?> cellData = context.getFirstCellData();
        WriteCellStyle writeCellStyle = cellData.getWriteCellStyle();
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("Arial");
        writeFont.setFontHeightInPoints((short) 10);
        writeCellStyle.setWriteFont(writeFont);
        writeCellStyle.setLocked(true);

        //设置颜色
//        XSSFColor color = new XSSFColor(new java.awt.Color(221,235,247),new DefaultIndexedColorMap()); //new java.awt.Color(198,217,240) 里的参数为RGB
        writeCellStyle.setFillForegroundColor(IndexedColors.PALE_BLUE.getIndex());

        writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

    }

    private void getValueCellStyle(CellWriteHandlerContext context) {

        WriteCellData<?> cellData = context.getFirstCellData();
        WriteCellStyle writeCellStyle = cellData.getWriteCellStyle();
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("宋体");
        writeFont.setFontHeightInPoints((short) 9);
        writeCellStyle.setWriteFont(writeFont);
        writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        writeCellStyle.setLocked(false);

    }

    private CellStyle valueCellStyle(CellWriteHandlerContext context) {
        WriteWorkbookHolder writeWorkbookHolder = context.getWriteWorkbookHolder();
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();

        cellStyle.setLocked(false);
        return cellStyle;
    }

}