package com.bbac.hrsf.admin.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCard;
import com.bbac.hrsf.admin.mapper.HrsfPmsTalentCardMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 人才卡下载相关服务
 */
@Slf4j
@Service
public class HrsfPmsTalentCardServiceImpl extends ServiceImpl<HrsfPmsTalentCardMapper, HrsfPmsTalentCard>  implements IHrsfPmsTalentCardService {

    @Autowired
    HrsfPmsTalentCardMapper hrsfPmsTalentCardMapper;

    @Override
    public void processJob(List<String> list, HttpServletResponse response) {}

    @Override
    public HrsfPmsTalentCard selectTalentCardByStaffId(String staffId) {
        return hrsfPmsTalentCardMapper.selectTalentCardByStaffId(staffId);
    }

    /**
     * 数据处理
     *
     */
    public void processData(){
        //TODO 下载人才卡
        //1、查找员工基础信息
        //2、组织数据
        //3、调用模板
        //4、调动phantomjs后端渲染Html  这个动作可以考虑在数据生成时就给生成出来
        //5、将文件保存到数据库HrsfPmsTalentCardPDF表
        //6、清理临时文件
    }

    /**
     * 模板参数：
     * 本岗位本年度培训记录 let courseRecordPositionData = [ ]
     */
    public static void main(String[] args) {

    }
}
