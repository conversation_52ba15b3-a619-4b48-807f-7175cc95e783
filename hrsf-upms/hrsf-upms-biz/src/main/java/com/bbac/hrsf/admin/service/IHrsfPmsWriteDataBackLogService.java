package com.bbac.hrsf.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfPmsWriteDataBackLog;

import java.util.List;

/**
 * @author: liu, jie
 * @create: 2022-06-08
 **/
public interface IHrsfPmsWriteDataBackLogService extends IService<HrsfPmsWriteDataBackLog> {
    boolean selectErrorInfoByTaskIds(List<String> taskId);

    boolean selectErrorInfoByEmailStatus(String type);

    List<HrsfPmsWriteDataBackLog> selectByEmailStatus();

    boolean updateEmailStatus(String type, String no_sendType);

    boolean updateEmailStatusById(String type, List<Long> idList);

    void updateEmailStatus2(String type, String type1, String type2);
}
