package com.bbac.hrsf.admin.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.Method;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.metadata.data.FormulaData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.ListUtils;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelation;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelationInfo;
import com.bbac.hrsf.admin.api.entity.HrsfPmsWriteDataBackLog;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserPhotoVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelDownVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelUploadVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelYearDownVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelYearUploadVO;
import com.bbac.hrsf.admin.api.vo.ScoreCollectVO;
import com.bbac.hrsf.admin.api.vo.UploadMessageVo;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.event.PmsUpdateEvent;
import com.bbac.hrsf.admin.event.PmsUserExcelDataListener;
import com.bbac.hrsf.admin.event.PmsUserExcelYearDataListener;
import com.bbac.hrsf.admin.handle.CustomCellWriteHandler;
import com.bbac.hrsf.admin.handle.CustomCellWriteYearHandler;
import com.bbac.hrsf.admin.handle.CustomSheetWriteHandler;
import com.bbac.hrsf.admin.handle.CustomSheetWriteYearHandler;
import com.bbac.hrsf.admin.mapper.HrsfPmsUserRelationInfoMapper;
import com.bbac.hrsf.admin.mapper.HrsfPmsuserBaseMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsUserRelationInfoService;
import com.bbac.hrsf.admin.service.IHrsfPmsUserRelationService;
import com.bbac.hrsf.admin.service.IHrsfPmsWriteDataBackLogService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.ChineseForeignTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.EmailStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.EmployeeTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.FormStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.LevelFlagEnum;
import com.bbac.hrsf.common.core.constant.enums.MappingEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.common.core.constant.enums.PotencyListDataEnum;
import com.bbac.hrsf.common.core.constant.enums.PotentialLevelEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.ScoreSourceEnum;
import com.bbac.hrsf.common.core.constant.enums.ShareJoinBbacEnum;
import com.bbac.hrsf.common.core.constant.enums.SpecialLeaverEnum;
import com.bbac.hrsf.common.core.constant.enums.UserLevelEnum;
import com.bbac.hrsf.common.core.constant.enums.UserStateEnum;
import com.bbac.hrsf.common.core.constant.enums.VariableEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.exception.ServiceException;
import com.bbac.hrsf.common.core.pojo.ErrorMessageSubVo;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import com.bbac.hrsf.performance.api.dto.CalibrationListQueryDTO;
import com.bbac.hrsf.performance.api.dto.HrsfCalibrationBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.api.feign.RemoteEmailService;
import com.bbac.hrsf.performance.api.feign.RemotePmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.sql.Struct;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum.getByCode;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HrsfPmsuserBaseServiceImpl extends ServiceImpl<HrsfPmsuserBaseMapper, HrsfPmsuserBase> implements IHrsfPmsuserBaseService {


    private final IHrsfUserBaseService hrsfUserBaseService;

    private final ApplicationEventPublisher publisher;

    private final IHrsfPmsUserRelationInfoService hrsfPmsUserRelationInfoService;

    private final IHrsfPmsUserRelationService hrsfPmsUserRelationService;

    private final HrsfPmsUserRelationInfoMapper hrsfPmsUserRelationInfoMapper;

    private final IHrsfPmsWriteDataBackLogService writeDataBackLogService;

    private final RemoteEmailService remoteEmailService;

    private final RemotePmsService remotePmsService;

    private final HrsfPmsuserBaseMapper hrsfPmsuserBaseMapper;

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 1000;


    private static final String UPDATE_PMS_FORM_USER_RATING_COMMENT = "FormUserRatingComment(formContentId={}L,formDataId={}L,sectionIndex={},itemId={}L,ratingType='{}')";
    private static final String UPDATE_PMS_FORM_COMPETENCY_SECTION = "FormCompetencySection(formContentId={}L,formDataId={}L,sectionIndex={})";
    private static final String UPDATE_PMS_FORM_COMPETENCY = "FormCompetency(formContentId={}L,formDataId={}L,itemId={}L,sectionIndex={})";
    private static final String UPDATE_PMS_CUST_PERFORMANCE_RATING = "cust_PerformanceRating(effectiveStartDate=datetime'{}T00:00:00',externalCode='{}')";

    private static final String UPDATE_PMS_FORM_USER_RATING_COMMENT_2 = "FormUserRatingComment(formContentId={}L,formDataId={}L,itemId={}L,ratingType='{}',sectionIndex={})";

    private static final String UPDATE_PMS_FORM_CUSTOM_SECTION = "FormCustomSection(formContentId={}L,formDataId={}L,sectionIndex={})";

    private static final String UPDATE_PMS_FORM_CUSTOM_ELEMENT = "FormCustomElement(elementKey='{}',formContentId={}L,formDataId={}L,itemId={}L,sectionIndex={})";

    private static final String UPDATE_PMS_DATE = "/Date({})/";


    private static final String error1 = "员工结果未更新：劳资员未上传结果或HR未退回；Result is not updated: HR coordinator has not yet upload the result or HR has not return the form back.";
    private static final String error2 = "请与管理员联系，确认员工的评分表状态；Please contact with admin to check the status of performance form of the employee.";
    private static final String error3 = "结果尚未提交，请稍后重试；Result has not been submitted yet, please try again later.";
    private static final String error4 = "评分或等级与机构负责人校准结果不一致，请检查。Rating or category is not consistent with the result of calibration execution.";
    private static final String error5 = "员工结果未更新：评分人已评分且未被退回；Result is not updated: Result is submitted by rater and the form has not yet been returned back.";


    @Value("${sf.sync.formHeader}")
    private String sfSyncFormHeader;
    @Value("${sf.sync.pmsRating}")
    private String sfSyncPmsRating;
    @Value("${sf.sync.midYearTransfer}")
    private String sfSyncMidYearTransfer;

    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;
    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;

    @Value("${sf.sync.principal}")
    private String principal;

    @Value("${sf.sync.overallAdjustedRating}")
    private String overallAdjustedRating;

    @Value("${sf.sync.formSummarySection}")
    private String formSummarySection;

    @Value("${sf.sync.formCustomSection}")
    private String formCustomSection;

    @Value("${sf.sync.sendToPreviousStep}")
    private String sendToPreviousStep;

    @Value("${sf.sync.processInactiveEmployees}")
    private String processInactiveEmployees;

    @Value("${sf.sync.username}")
    private String userName;
    @Value("${sf.sync.pwd}")
    private String pwd;

    @Value("${sf.sync.punish}")
    private String sfSyncPunish;

    @Value("${sf.sync.upsertApi}")
    private String syncUpsertApi;

    @Value("${sf.sync.formcontentId}")
    private String sfSyncFormcontentId;

    @Value("${sf.sync.upsert}")
    private String sfSyncUpsert;

    @Value("${sf.sync.sendToNextStep}")
    private String sfSyncSendToNextStep;

    @Value("${sf.sync.sendToNextStepKey}")
    private String sfSyncSendToNextStepKey;


    @Value("${sf.sync.TodoEntryV2}")
    private String sfSyncTodoEntryV2;


    @Value("${sf.sync.itemId}")
    private int itemId;
    @Value("${sf.sync.itemId1}")
    private int itemId1;
    @Value("${sf.sync.itemId2}")
    private int itemId2;
    @Value("${sf.sync.itemId3}")
    private int itemId3;
    @Value("${sf.sync.itemId4}")
    private int itemId4;
    @Value("${sf.sync.itemId5}")
    private int itemId5;
    @Value("${sf.sync.itemId6}")
    private int itemId6;
    @Value("${sf.sync.itemId7}")
    private int itemId7;
    @Value("${sf.sync.itemId8}")
    private int itemId8;
    @Value("${sf.sync.itemId9}")
    private int itemId9;
    @Value("${sf.sync.itemId10}")
    private int itemId10;

    @Value("${sf.sync.itemId11}")
    private int itemId11;
    @Value("${sf.sync.itemId12}")
    private int itemId12;
    @Value("${sf.sync.itemId13}")
    private int itemId13;
    @Value("${sf.sync.itemId14}")
    private int itemId14;
    @Value("${sf.sync.itemId15}")
    private int itemId15;


    @Value("${sf.sync.itemIdKey1}")
    private String itemIdKey1;
    @Value("${sf.sync.itemIdKey2}")
    private String itemIdKey2;
    @Value("${sf.sync.itemIdKey3}")
    private String itemIdKey3;
    @Value("${sf.sync.itemIdKey4}")
    private String itemIdKey4;
    @Value("${sf.sync.itemIdKey5}")
    private String itemIdKey5;
    @Value("${sf.sync.itemIdKey6}")
    private String itemIdKey6;
    @Value("${sf.sync.itemIdKey7}")
    private String itemIdKey7;
    @Value("${sf.sync.itemIdKey8}")
    private String itemIdKey8;
    @Value("${sf.sync.itemIdKey9}")
    private String itemIdKey9;
    @Value("${sf.sync.itemIdKey10}")
    private String itemIdKey10;

    @Value("${sf.sync.itemIdKey11}")
    private String itemIdKey11;
    @Value("${sf.sync.itemIdKey12}")
    private String itemIdKey12;
    @Value("${sf.sync.itemIdKey13}")
    private String itemIdKey13;
    @Value("${sf.sync.itemIdKey14}")
    private String itemIdKey14;
    @Value("${sf.sync.itemIdKey15}")
    private String itemIdKey15;

    @Value("${sf.sync.suffixName}")
    private String suffixName;


    @Value("${sf.sync.itemIdKey}")
    private String itemIdKey;

    private final String UTC_PATTERN_CUSTOM_START = "{}-12-31T00:00:00Z";
    private final String UTC_PATTERN_CUSTOM_END = "{}-12-31T15:59:59Z";
    private final String FORMULA_COMMON = "SUM((F:F*30%+G:G*10%+H:H*30%+I:I*20%+J:J*10%))*100&\"%\"";
    private final String FORMULA_COMMON_T = "(ROUNDDOWN(L:L*10,0)*10+ROUND(RIGHT(L:L*1000,2)/50,0)*5)&\"%\"";
    private final String FORMULA_COMMON_YEAR = "SUM((F:F*20%+G:G*20%+H:H*10%+I:I*10%+J:J*10%+K:K*5%+L:L*5%+M:M*10%+N:N*5%+O:O*5%))*100&\"%\"";
    private final String FORMULA_COMMON_YEAR_T = "(ROUNDDOWN(Q:Q*10,0)*10+ROUND(RIGHT(Q:Q*1000,2)/50,0)*5)&\"%\"";


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startPmsUser(HrsfPmstart hrsfPmstart, List<String> staffIdList) {
        /**
         * "1. 初始化考核员工基础表时，需要设置筛选条件：
         *  仅拉取员工状态=Active且用工形式=正式员工、顶岗实习生、第三方、
         *  退休返聘、内退返聘和其他考核人员； 以及员工状态=inactive且特殊离职人员=退休、因公去世、调至集团以及其他考核人员；
         */
        log.error("开始启动绩效.....");
        List<HrsfUserBase> list = hrsfUserBaseService.list(buildQueryWrapper(staffIdList));

        /**
         * 过滤出levelAssessment为Y的员工
         * 新增判断，若为季度考核，针对L3的filter，无需发起校准会议；白领L3不参与季度考核
         */
        List<HrsfPmsuserBase> baseList = HrsfUpmsConvert.INSTANCE.toHrsfPmsuserBase(list, hrsfPmstart.getAssesType());
        //将其中的考核年份和考核类型匹配进去
        saveBatch(baseList.stream().map(obj -> {
            obj.setAssessType(hrsfPmstart.getAssesType());
            obj.setAssessYear(hrsfPmstart.getAssesYear());
            return obj;
        }).collect(Collectors.toList()));


        Map<String, Long> baseMap = baseList.stream().collect(Collectors.toMap(HrsfPmsuserBase::getStaffId, HrsfPmsuserBase::getId));


        Map<String, Long> empIdMap = baseList.stream().collect(Collectors.toMap(HrsfPmsuserBase::getEmpId, HrsfPmsuserBase::getId,(p1,p2)->p2));

        log.error("\nmacros|startPmsUser,hrsfPmstart={}\n",JSONUtil.toJsonStr(hrsfPmstart));
        /**
         * 2、更新表中部分FROM_ID\CONTEXT_ID数据
         *
         *
         * https://api15preview.sapsf.cn/odata/v2/FormHeader?$filter=formDataStatus eq '1' and
         * (formTemplateId eq '798' or formTemplateId eq '799' or formTemplateId eq '801' or formTemplateId eq '802')
         * and formReviewStartDate eq datetimeoffset'2022-03-31T16:00:00Z' and
         * formReviewEndDate eq datetimeoffset'2022-06-30T15:59:59Z' &$format=json
         *
         * 时间页数处理,选择时间
         *
         * 开始和结束时间都要做减8个小时
         *
         * */
        LocalDate startDate = hrsfPmstart.getStartDate();
        LocalDate endDate = hrsfPmstart.getEndDate();
        String startDateStr = LocalDateTime.of(startDate, LocalTime.MIN).format(DateTimeFormatter.ofPattern(DatePattern.UTC_PATTERN));
        String endDateStr = LocalDateTime.of(endDate, LocalTime.MAX).format(DateTimeFormatter.ofPattern(DatePattern.UTC_PATTERN));
        publisher.publishEvent(PmsUpdateEvent.of(sfSyncFormHeader, startDateStr, null, endDateStr, PmsUpdateEventEnum.A, baseMap,empIdMap,hrsfPmstart));

        /**
         * 仅当启动绩效考核时考核类型=YEAR时
         */
        if (AssessTypeEnum.YEAR.getType().equals(hrsfPmstart.getAssesType())) {

            publisher.publishEvent(PmsUpdateEvent.of(sfSyncPmsRating, hrsfPmstart.getAssesYear(), hrsfPmstart.getAssesType(), PmsUpdateEventEnum.B, baseMap, CollUtil.isNotEmpty(staffIdList),empIdMap));
            /**
             * fix bug - 539
             * 这里特殊处理,开始时间是当前年份减1
             * 结束日期不用处理
             *
             */
            publisher.publishEvent(PmsUpdateEvent.of(sfSyncMidYearTransfer, StrUtil.format(UTC_PATTERN_CUSTOM_START, String.valueOf(Long.valueOf(hrsfPmstart.getAssesYear()) - 1)), hrsfPmstart.getAssesType()
                    , StrUtil.format(UTC_PATTERN_CUSTOM_END, hrsfPmstart.getAssesYear()), PmsUpdateEventEnum.C, baseMap,empIdMap));
        }else if (AssessTypeEnum.Q1.getType().equals(hrsfPmstart.getAssesType()) || AssessTypeEnum.Q2.getType().equals(hrsfPmstart.getAssesType())){
            /**
             * 2023/3/29 新增逻辑，处理年度绩效启动时会更新季度均分值，当再启动Q1绩效时，季度均分还有值，没有置空的问题。
             * 正常来说只需要Q1清空就行，但当前Q1已经启动，无法修复，所以加了Q2也进行处理。
             */
            publisher.publishEvent(PmsUpdateEvent.of(sfSyncPmsRating, hrsfPmstart.getAssesYear(), hrsfPmstart.getAssesType(), PmsUpdateEventEnum.M, baseMap, CollUtil.isNotEmpty(staffIdList),empIdMap));
        }
        /**
         * 获取惩罚信息
         */
        publisher.publishEvent(PmsUpdateEvent.of(sfSyncPunish, hrsfPmstart.getStartDate(), hrsfPmstart.getEndDate(), PmsUpdateEventEnum.D, baseMap,empIdMap));


        return true;
    }

    @Override
    public List<HrsfPmsuserBase> getPmsUserList(String assesYear, String assesType) {
        List<HrsfPmsuserBase> list = list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getAssessYear, assesYear)
                .eq(HrsfPmsuserBase::getAssessType, assesType));
        List<HrsfPmsuserBase> newList = list.stream().collect(Collectors
                .collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(HrsfPmsuserBase::getStaffId))),
                        ArrayList::new));

        convertPmsLevelDesc(newList);
        return newList;
    }

    @Override
    public Boolean updateCalibrationBaseId(Long id, String assesType, String assesYear, Set<String> staffIds) {
        return lambdaUpdate()
                .set(HrsfPmsuserBase::getCalibrationBaseId, id)
                .eq(HrsfPmsuserBase::getAssessType, assesType)
                .eq(HrsfPmsuserBase::getAssessYear, assesYear)
                .in(HrsfPmsuserBase::getStaffId, staffIds).update();
    }

    @Override
    public List<HrsfPmsuserBase> getCalibrationListById(CalibrationListQueryDTO queryDTO, Boolean isVerify) {
        if (!checkAuthority(queryDTO.getTaskId(), queryDTO.getStatus(), isVerify)) {
            throw new CheckedException("该用户没有权限查看数据,请联系管理员");
        }
        List<HrsfPmsuserBase> list = list(buildQueryWrapperCalibration(queryDTO));
        
        convertPmsLevelDesc(list);
        
        return list;
    }

    /**
     * 根据taskID校验权限
     *
     * @param taskId
     * @param isVerify
     * @return
     */
    private Boolean checkAuthority(String taskId, String status, Boolean isVerify) {
        /**
         *  如果有HR管理员的角色则可以查看任何数据
         *  否则需要校验该taskID和当前用户是否匹配
         */
        if (isVerify) {
            if (StrUtil.isNotBlank(taskId)) {
                return true;//remotePmsService.checkTaskById(taskId, status, SecurityUtils.getUser().getUsername(), SecurityConstants.FROM_IN);
            }
        } else {
            Optional<GrantedAuthority> optional = SecurityUtils.getUser().getAuthorities().stream().filter(data ->
                    data.getAuthority().contains(String.valueOf(RoleTypeEnum.HR.getType()))).findFirst();
            if (optional.isPresent()) {
                return true;
            }
        }
        return false;
    }

    @Override
    public LinkedHashMap<String, Object> getCalibrationPmsById(CalibrationListQueryDTO queryDTO, Boolean isVerify) {
        if (!checkAuthority(queryDTO.getTaskId(), queryDTO.getStatus(), isVerify)) {
            throw new CheckedException("该用户没有权限查看数据,请联系管理员");
        }
        LinkedHashMap<String, Object> returnMap = new LinkedHashMap<>(16);

        /**
         * 7.人数指导，针对杰出和优秀的等级区间，需要根据规则计算建议的最大区间人数。计算规则：
         * a.判断若校准会议中的总人数>4个人：
         * 杰出人数上限：该校准会议中的总人数×10%，四舍五入；（比如总共有36人，杰出区间显示为（<=4人）
         * 优秀+杰出人数上限：采取两种计算方式得出两个值，取最大值；
         * 第一种为该校准会议中的总人数×35%，并四舍五入；
         * 第二种为：杰出等级上限人数（该校准会议中的总人数×10%，四舍五入）+优秀等级上限人数（该校准会议中的总人数×25%，四舍五入）
         * （比如总共38人，按照第一种算法，人数上限为13人；按照第二种算法，人数上限为14人，取14；杰出+优秀区间展示位（<=14人）
         *
         *
         * b.判断若校准会议中的总人数<=4个人：
         * 杰出人数上限为1
         * 优秀+杰出人数上限：采取两种计算方式得出两个值，取最大值(同上)
         * 第一种为该校准会议中的总人数×35%，并四舍五入；
         * 第二种为：杰出等级上限人数（该校准会议中的总人数×10%，四舍五入）+优秀等级上限人数（该校准会议中的总人数×25%，四舍五入）
         */
        if (queryDTO.getCalibrationBaseId() != null) {
            List<HrsfPmsuserBase> noFilterList = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId,
                    queryDTO.getCalibrationBaseId()));
            int sizeFixed = noFilterList.size();

            List<HrsfPmsuserBase> baseList = list(buildQueryWrapperCalibration(queryDTO));
            int size = baseList.size();
            /**
             * fix bug 190
             * 2022-07-05
             * 如果总人数<=1的话,则返回null
             */
            if (size < 1) {
                returnMap.put("outStandingTotal", null);
                returnMap.put("exelOutTotal", null);
                returnMap.put("total", sizeFixed);
            } else if (size == 1) {
                returnMap.put("outStandingTotal", 1);
                returnMap.put("exelOutTotal", 1);
                returnMap.put("total", sizeFixed);
            } else {
                /**
                 * 2024.01.04 fix 由四舍五修改为向上取整
                 */
                //Outstanding总人数
                BigDecimal outStanding = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.1)).setScale(0, BigDecimal.ROUND_UP);
                //Excellent+Outstanding总人数
                BigDecimal exelOutTotal = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.35)).setScale(0, BigDecimal.ROUND_UP);
                BigDecimal exelOutPart = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.1)).setScale(0, BigDecimal.ROUND_UP)
                        .add(BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.25)).setScale(0, BigDecimal.ROUND_UP));
                returnMap.put("outStandingTotal", size > 4 ? outStanding : BigDecimal.valueOf(1));
                returnMap.put("exelOutTotal", exelOutTotal.compareTo(exelOutPart) == 1 ? exelOutTotal : exelOutPart);
                returnMap.put("total", sizeFixed);
            }
        }
        /**
         * 查询pmsLevel不为空的数据
         */
        try {
            List<HrsfPmsuserBase> pmsList = list(buildQueryWrapperCalibrationPms(queryDTO));
            if (CollectionUtil.isEmpty(pmsList)) {
                return returnMap;
            }

            convertPmsLevelDesc(pmsList);

            //获取用户头像信息
            List<String> staffIdList = pmsList.stream().map(HrsfPmsuserBase::getStaffId).collect(Collectors.toList());
            List<HrsfUserBase> list = hrsfUserBaseService.list(Wrappers.<HrsfUserBase>lambdaQuery().
                    in(HrsfUserBase::getStaffId, staffIdList).select(HrsfUserBase::getPhoto, HrsfUserBase::getStaffId));
            Map<String, String> photoMap = list.stream().filter(p -> StrUtil.isNotBlank(p.getPhoto()))
                    .collect(Collectors.toMap(HrsfUserBase::getStaffId, HrsfUserBase::getPhoto));

            /**
             * 设置用户头像
             */
            LinkedHashMap<String, List<HrsfPmsUserPhotoVO>> pmsMap = pmsList.stream().map(p -> {
                HrsfPmsUserPhotoVO photoVO = HrsfUpmsConvert.INSTANCE.toHrsfPmsUserPhotoVO(p);
                photoVO.setPhoto(photoMap.get(photoVO.getStaffId()));
                return photoVO;
            }).collect(Collectors.toList())
                    //对里面的先分组然后在根据最终得分进行排序
                    .stream().filter(data -> data.getPmsLevel() != null).sorted(Comparator.comparing(HrsfPmsUserPhotoVO::getTotalScore, Comparator.reverseOrder())).collect(Collectors.groupingBy(
                            p -> PmsLevelEnum.getByCode(p.getPmsLevel()), LinkedHashMap::new, Collectors.toList()));
            pmsMap.entrySet().stream().forEach(p -> returnMap.put(p.getKey(), p.getValue()));


        } catch (Exception e) {
            log.error("查询业绩视图失败:{}", e);
            throw new ServiceException(null, "查询业绩视图失败,请稍后再试");
        }
        return returnMap;


    }

    /**
     * 获取筛选器接口
     *
     * @param id
     * @return
     */
    @Override
    public HrsfOrganizationVO getCalibrationPmsSelect(Long id) {

        //分别去员工基础表中查询相应的系统、部门、科室、群组
        return new HrsfOrganizationVO(list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT SYSTEM").lambda()
                .eq(HrsfPmsuserBase::getCalibrationBaseId, id).isNotNull(HrsfPmsuserBase::getSystem).orderByDesc(HrsfPmsuserBase::getSystem)).stream().map(HrsfPmsuserBase::getSystem).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT DEPARTMENT").lambda()
                        .eq(HrsfPmsuserBase::getCalibrationBaseId, id).isNotNull(HrsfPmsuserBase::getDepartment).orderByDesc(HrsfPmsuserBase::getDepartment)).stream().map(HrsfPmsuserBase::getDepartment).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT SECTION").lambda()
                        .eq(HrsfPmsuserBase::getCalibrationBaseId, id).isNotNull(HrsfPmsuserBase::getSection).orderByDesc(HrsfPmsuserBase::getSection)).stream().map(HrsfPmsuserBase::getSection).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT USER_GROUP").lambda()
                        .eq(HrsfPmsuserBase::getCalibrationBaseId, id).isNotNull(HrsfPmsuserBase::getUserGroup).orderByDesc(HrsfPmsuserBase::getUserGroup)).stream().map(HrsfPmsuserBase::getUserGroup).collect(Collectors.toList()));
    }

    /**
     * 获取筛选器接口-员工信息
     *
     * @param id
     * @param fullName
     * @return
     */
    @Override
    public List<HrsfUserVO> getCalibrationPmsSelectName(Long id, String fullName) {
        LambdaQueryWrapper<HrsfPmsuserBase> wrapper = Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getCalibrationBaseId, id);
        if (StrUtil.isNotBlank(fullName)) {
            List<HrsfPmsuserBase> hrsfPmsuserBases = hrsfPmsuserBaseMapper.selectByBaseIdOrStaffId(id, fullName);
            return hrsfPmsuserBases.stream()
                    .map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
        } else {
            return list(wrapper).stream()
                    .map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
        }
    }

    @Override
    public ScoreCollectVO getCalibrationScoreCollect(CalibrationListQueryDTO queryDTO, Boolean isVerify) {
        if (!checkAuthority(queryDTO.getTaskId(), queryDTO.getStatus(), isVerify)) {
            throw new CheckedException("该用户没有权限查看数据,请联系管理员");
        }
        List<HrsfPmsuserBase> baseList = list(buildQueryWrapperCalibration(queryDTO));
        List<HrsfPmsuserBase> noFilterList = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId,
                queryDTO.getCalibrationBaseId()));
        /**
         * 获取业绩等级不为空的数据
         *
         */
        List<HrsfPmsuserBase> alReadyList = baseList.stream().filter(p -> p.getPmsLevel() != null).collect(Collectors.toList());
        BigDecimal average = new BigDecimal(alReadyList.stream().mapToDouble(p -> p.getTotalScore() == null ? 0 : p.getTotalScore().doubleValue()).average().orElse(0d))
                .setScale(2, BigDecimal.ROUND_HALF_UP);

        BigDecimal averageFixed = new BigDecimal(noFilterList.stream().mapToDouble(p -> p.getTotalScore() == null ? 0 : p.getTotalScore().doubleValue()).average().orElse(0d))
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        String rationComplete = alReadyList.size() + "/" + baseList.size();

        return new ScoreCollectVO(rationComplete, average, baseList.size(), averageFixed, noFilterList.size());
    }

    @Override
    public ErrorMessageVo syncScoreBySF(List<Long> userBaseIdList, HrsfCalibrationBase calibrationBase) {

        ErrorMessageVo errorMessageVo = new ErrorMessageVo();
        List<ErrorMessageSubVo> errorMessageSubVoList = new ArrayList<>();
        List<Long> cancelList = new ArrayList<>();
        List<HrsfPmsuserBase> successList = new ArrayList<>();
        String processStep = calibrationBase.getProcessStep();
        String template = calibrationBase.getTemplate();
        /**
         * 这里按校准会议模板区分
         */

        if (CalibrationTemplateEnum.ONE.getType().equals(template)) {
            handleTemplateOne(userBaseIdList, processStep, cancelList, successList, errorMessageSubVoList);
        } else if (CalibrationTemplateEnum.TWO.getType().equals(template)) {
            handleTemplateTwo(userBaseIdList, processStep, cancelList, successList, errorMessageSubVoList);
        }
        /**
         * successList表示更新成功的数量
         * userBaseIdList.size() - successList.size()表示失败的数量
         */
        errorMessageVo.setSuccessNum(successList.size());
        errorMessageVo.setErrorNum(userBaseIdList.size() - successList.size());
        errorMessageVo.setErrorMessage(errorMessageSubVoList);
        return errorMessageVo;
    }

    /**
     * 处理白领年度的逻辑
     *
     * @param userBaseIdList
     * @param processStep
     * @param cancelList
     * @param errorMessageSubVoList
     */
    private void handleTemplateTwo(List<Long> userBaseIdList, String processStep, List<Long> cancelList, List<HrsfPmsuserBase> successList, List<ErrorMessageSubVo> errorMessageSubVoList) {

        list(Wrappers.<HrsfPmsuserBase>lambdaQuery().select(HrsfPmsuserBase::getFormId, HrsfPmsuserBase::getContentId
                , HrsfPmsuserBase::getId, HrsfPmsuserBase::getScoreSource
                , HrsfPmsuserBase::getFormStatus, HrsfPmsuserBase::getChangeFlag
                , HrsfPmsuserBase::getTotalScore, HrsfPmsuserBase::getPmsLevel, HrsfPmsuserBase::getStaffId)
                .in(HrsfPmsuserBase::getId, userBaseIdList)).stream().forEach(obj -> {

                    log.warn("handleTemplateTwo|two={}",JSONUtil.toJsonStr(obj));
            String formStatus = obj.getFormStatus(), formId = obj.getFormId();
            Long id = obj.getId();
            try {
                /**
                 * 判断若表单状态字段不等于评分中,不做任何处理,结束不再往下查询,直接终止
                 */
                if (!FormStatusEnum.In_Rating.getType().equals(formStatus)) {
                    log.error("该员工身上的表单状态字段不等于评分中,员工ID:{},主键ID:{}", obj.getStaffId(), obj.getId());
                    ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                    errorMessageSubVo.setStaffId(obj.getStaffId());
                    errorMessageSubVo.setErrorMessage(error5);
                    errorMessageSubVoList.add(errorMessageSubVo);
                    return;
                }
                String format = StrUtil.format(principal, formId);
                log.info("获取获取当前步骤负责人的url:{}", format);
                String body;
                try {
                    body = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                } catch (Exception e) {
                    log.error("获取获取当前步骤负责人失败自动重试一次");
                    body = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                }
                JSONObject jsonObject = JSONUtil.parseObj(body);
                log.error("macros|获取获取当前步骤负责人的url:{}", body);
                /**
                 * update 2022/05/30
                 * 此处替换成从返回接口取contentId
                 * 不从基础表中获取
                 */

                String contentId = jsonObject.getJSONObject("d").getJSONObject("formLastContent").getStr("formContentId");
                Optional<String> folderOption = jsonObject.getJSONObject("d").getJSONObject("formLastContent")
                        .getJSONObject("folders").getJSONArray("results").stream().filter(p ->
                                CommonConstants.TODO.equals(((JSONObject) p).getStr("folderName")))
                        .map(p -> ((JSONObject) p).getStr("userId")).findFirst();

                if (folderOption.isPresent()) {
                    String userId = folderOption.get();
                    if (CommonConstants.PMAPI.equals(userId)) {
                        String aRating = StrUtil.format(overallAdjustedRating, contentId, formId);
                        log.info("获取获取当前步骤负责人的url:{}", aRating);
                        String aRatingBody = new HttpRequestCustom(aRating).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        JSONObject aRatingJson = JSONUtil.parseObj(aRatingBody);
                        log.error("macros|获取获取当前步骤负责人的url:{}", aRatingJson);
                        /**
                         * 处理list返回多个的场景
                         */
                        String rating = aRatingJson.getJSONObject("d").getStr("rating");

                        String fRating = StrUtil.format(formSummarySection, contentId, formId);
                        log.info("获取获取当前步骤负责人的url:{}", fRating);
                        String fRatingBody = new HttpRequestCustom(fRating).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        JSONObject fRatingJson = JSONUtil.parseObj(fRatingBody);
                        log.error("macros|获取获取当前步骤负责人的url:{}", fRatingJson);
                        /**
                         * 处理list返回多个的场景
                         */
                        String ratingLevel = fRatingJson.getJSONObject("d").getStr("rating");
                        String textRating = fRatingJson.getJSONObject("d").getStr("textRating");


                        String potential = StrUtil.format(formCustomSection, contentId, formId);
                        log.info("获取潜力等级和可变动性的url:{}", potential);
                        String potentialBody = new HttpRequestCustom(potential).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        JSONObject potentialJson = JSONUtil.parseObj(potentialBody);
                        log.error("macros|获取潜力等级和可变动性的url:{}", potentialBody);
                        /**
                         * 处理list返回多个的场景
                         */
                        Map<String, String> potentialMap = potentialJson.getJSONObject("d").getJSONArray("results").stream()
                                .collect(Collectors.toMap(data -> ((JSONObject) data).getStr("elementKey"), data -> ((JSONObject) data).getStr("value")));
                        //可变动性和潜力等级
                        String variable = VariableEnum.getTypeByDesc(potentialMap.get(CommonConstants.ELE_2));
                        String potentialLevel = PotentialLevelEnum.getTypeByDesc(potentialMap.get(CommonConstants.ELE_1));
                        /**
                         * 根据流程状态获取表单状态
                         * 然后更新表单状态
                         */
                        /**
                         * 更新分数来源
                         */
                        String newStatus = handleFormStatusByProcessStep(processStep);
                        successList.add(new HrsfPmsuserBase(new BigDecimal(rating), new BigDecimal(ratingLevel),
                                textRating, newStatus, ScoreSourceEnum.SF.getType(), id, potentialLevel, PotentialLevelEnum.getDescByType(potentialLevel)
                                , variable, VariableEnum.getDescByType(variable)));
                    } else {
                        log.error("当前SF系统节点不是PMAPI而是:{}", userId);
                        ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                        errorMessageSubVo.setStaffId(obj.getStaffId());
                        errorMessageSubVo.setErrorMessage(error3);
                        errorMessageSubVoList.add(errorMessageSubVo);
                    }
                } else {
                    ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                    errorMessageSubVo.setStaffId(obj.getStaffId());
                    errorMessageSubVo.setErrorMessage(error2);
                    errorMessageSubVoList.add(errorMessageSubVo);
                    log.error("当前SF系统节点没有获取到人员ID:{}", JSONUtil.toJsonStr(folderOption));
                }
            } catch (Exception e) {
                ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                errorMessageSubVo.setStaffId(obj.getStaffId());
                errorMessageSubVo.setErrorMessage("网络异常，请稍后重试；Network anomaly, please try again later.");
                errorMessageSubVoList.add(errorMessageSubVo);
                log.error("syncScoreBySF更新员工数据失败:{}", e);
                cancelList.add(id);
            }
        });
        /**
         * 批量更新业绩等级和最终分、表单状态、分数来源
         */
        if (CollectionUtil.isNotEmpty(successList)) {
            updateBatchById(successList);
        }
    }

    /**
     * 处理蓝白领季度+蓝领年度的逻辑
     *
     * @param userBaseIdList
     * @param processStep
     * @param cancelList
     * @param errorMessageSubVoList
     */
    private void handleTemplateOne(List<Long> userBaseIdList, String processStep, List<Long> cancelList, List<HrsfPmsuserBase> successList, List<ErrorMessageSubVo> errorMessageSubVoList) {
        /**
         * 查询员工基础表数据
         */

        list(Wrappers.<HrsfPmsuserBase>lambdaQuery().select(HrsfPmsuserBase::getFormId, HrsfPmsuserBase::getContentId
                , HrsfPmsuserBase::getId, HrsfPmsuserBase::getScoreSource
                , HrsfPmsuserBase::getFormStatus, HrsfPmsuserBase::getChangeFlag
                , HrsfPmsuserBase::getTotalScore, HrsfPmsuserBase::getPmsLevel, HrsfPmsuserBase::getStaffId)
                .in(HrsfPmsuserBase::getId, userBaseIdList)).stream().forEach(obj -> {
            String formId = obj.getFormId(), scoreSource = obj.getScoreSource(),
                    formStatus = obj.getFormStatus(), changeFlag = obj.getChangeFlag();
            Long id = obj.getId();
            BigDecimal totalScore = obj.getTotalScore(), pmsLevel = obj.getPmsLevel();

            try {
                /**
                 * 判断若被选中员工的分数来源字段为空或者从SF
                 */
                if (StrUtil.isBlank(scoreSource) || ScoreSourceEnum.SF.getType().equals(scoreSource)) {

                    /**
                     * 判断若表单状态字段不等于评分中,不做任何处理,结束不再往下查询,直接终止
                     */
                    if (!FormStatusEnum.In_Rating.getType().equals(formStatus)) {
                        log.error("该员工身上的表单状态字段不等于评分中,员工ID:{},主键ID:{}", obj.getStaffId(), obj.getId());
                        ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                        errorMessageSubVo.setStaffId(obj.getStaffId());
                        errorMessageSubVo.setErrorMessage(error5);
                        errorMessageSubVoList.add(errorMessageSubVo);
                        return;
                    }
                    if (StrUtil.isBlank(formId)) {
                        log.error("该员工身上的formId为空:{},请检查！", obj.getFormId());
                        return;
                    }
                    String format = StrUtil.format(principal, formId);
                    log.info("获取获取当前步骤负责人的url:{}", format);
                    String body;
                    try {
                        body = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();

                    } catch (Exception e) {
                        log.error("获取获取当前步骤负责人失败自动重试一次");
                        body = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();

                    }
                    JSONObject jsonObject = JSONUtil.parseObj(body);
                    /**
                     * update 2022/05/30
                     * 此处替换成从返回接口取contentId
                     * 不从基础表中获取
                     */

                    String contentId = jsonObject.getJSONObject("d").getJSONObject("formLastContent").getStr("formContentId");
                    Optional<String> folderOption = jsonObject.getJSONObject("d").getJSONObject("formLastContent")
                            .getJSONObject("folders").getJSONArray("results").stream().filter(p ->
                                    CommonConstants.TODO.equals(((JSONObject) p).getStr("folderName"))).map(p -> ((JSONObject) p).getStr("userId")).findFirst();
                    if (folderOption.isPresent()) {
                        String userId = folderOption.get();
                        if (CommonConstants.PMAPI.equals(userId)) {
                            String aRating = StrUtil.format(overallAdjustedRating, contentId, formId);
                            log.info("获取获取当前步骤负责人的url:{}", aRating);
                            String aRatingBody = new HttpRequestCustom(aRating).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                            JSONObject aRatingJson = JSONUtil.parseObj(aRatingBody);
                            /**
                             * 处理list返回多个的场景
                             */
                            String rating = aRatingJson.getJSONObject("d").getStr("rating");

                            String fRating = StrUtil.format(formSummarySection, contentId, formId);
                            log.info("获取获取当前步骤负责人的url:{}", fRating);
                            String fRatingBody = new HttpRequestCustom(fRating).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                            JSONObject fRatingJson = JSONUtil.parseObj(fRatingBody);
                            /**
                             * 处理list返回多个的场景
                             */
                            String ratingLevel = fRatingJson.getJSONObject("d").getStr("rating");
                            String textRating = fRatingJson.getJSONObject("d").getStr("textRating");

                            /**
                             * 根据流程状态获取表单状态
                             * 然后更新表单状态
                             */
                            /**
                             * 更新分数来源
                             */
                            String newStatus = handleFormStatusByProcessStep(processStep);
                            /**
                             * 判断若员工是否存在绿色标记，若员工无绿色标记
                             */
                            if (CommonConstants.FLAG_NO.equals(changeFlag)) {
                                /**
                                 * 判断若接口获取的userId不等于PMAPI，不做任何处理；结束；
                                 * 判断若接口获取的userId=PMAPI，根据考核员工基础表中存储的DataId和ContentID，调取接口获取评分表中的最终总分和业绩等级
                                 * 将满足存放在列表中
                                 */
                                successList.add(new HrsfPmsuserBase(new BigDecimal(rating), new BigDecimal(ratingLevel), textRating, newStatus, ScoreSourceEnum.SF.getType(), id));
                            } else {
                                /**
                                 * 标记为绿色的员工
                                 */
                                if (totalScore != null && pmsLevel != null
                                        && new BigDecimal(rating).compareTo(totalScore) == 0 && new BigDecimal(ratingLevel).compareTo(pmsLevel) == 0) {
                                    /**
                                     * 如果都相等的情况下,清空绿色标记
                                     */
                                    cancelList.add(id);
                                } else {
                                    ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                                    errorMessageSubVo.setStaffId(obj.getStaffId());
                                    errorMessageSubVo.setErrorMessage(error4);
                                    errorMessageSubVoList.add(errorMessageSubVo);
                                }
                                successList.add(new HrsfPmsuserBase(null, null, null, newStatus, ScoreSourceEnum.SF.getType(), id));
                            }
                        } else {
                            log.error("当前SF系统节点不是PMAPI而是:{}", userId);
                            ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                            errorMessageSubVo.setStaffId(obj.getStaffId());
                            errorMessageSubVo.setErrorMessage(error3);
                            errorMessageSubVoList.add(errorMessageSubVo);
                        }
                    } else {
                        ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                        errorMessageSubVo.setStaffId(obj.getStaffId());
                        errorMessageSubVo.setErrorMessage(error2);
                        errorMessageSubVoList.add(errorMessageSubVo);
                        log.error("当前SF系统节点没有获取到人员ID:{}", JSONUtil.toJsonStr(folderOption));
                    }
                } else {
                    /**
                     * 系统判断若被选中员工的分数来源字段为批量导入
                     * 判断若表单状态字段不等于评分中,不做任何处理,结束不再往下查询,直接终止
                     */
                    if (!FormStatusEnum.Rated.getType().equals(formStatus)) {
                        ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                        errorMessageSubVo.setStaffId(obj.getStaffId());
                        errorMessageSubVo.setErrorMessage(error1);
                        errorMessageSubVoList.add(errorMessageSubVo);
                        return;
                    } else {
                        String newStatus = handleFormStatusByProcessStep(processStep);
                        successList.add(new HrsfPmsuserBase(newStatus, id));
                    }
                }
            } catch (Exception e) {
                ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                errorMessageSubVo.setStaffId(obj.getStaffId());
                errorMessageSubVo.setErrorMessage("网络异常，请稍后重试；Network anomaly, please try again later.");
                errorMessageSubVoList.add(errorMessageSubVo);
                log.error("syncScoreBySF更新员工数据失败:{}", e);
            }
        });
        /**
         * 批量更新业绩等级和最终分、表单状态、分数来源
         */
        if (CollectionUtil.isNotEmpty(successList)) {
            updateBatchById(successList);
        }
        /**
         * 处理那些标绿的字段
         *
         */
        if (CollectionUtil.isNotEmpty(cancelList)) {
            update(Wrappers.<HrsfPmsuserBase>lambdaUpdate().set(HrsfPmsuserBase::getChangeFlag, CommonConstants.FLAG_NO)
                    .in(HrsfPmsuserBase::getId, cancelList));
        }
    }

    /**
     * 退回到表单
     *
     * @param userBaseIdList
     * @return
     */
    @Override
    public R<ErrorMessageVo> returnCalibration(List<Long> userBaseIdList) {
        ErrorMessageVo errorMessageVo = new ErrorMessageVo();
        List<ErrorMessageSubVo> errorMessageSubVoList = new ArrayList<>();
        Set<Long> successList = new HashSet<>();
        list(Wrappers.<HrsfPmsuserBase>lambdaQuery().select(HrsfPmsuserBase::getFormId, HrsfPmsuserBase::getContentId
                , HrsfPmsuserBase::getId, HrsfPmsuserBase::getScoreSource, HrsfPmsuserBase::getStaffId
                , HrsfPmsuserBase::getFormStatus, HrsfPmsuserBase::getChangeFlag
                , HrsfPmsuserBase::getTotalScore, HrsfPmsuserBase::getPmsLevel)
                .in(HrsfPmsuserBase::getId, userBaseIdList)).stream().forEach(obj -> {

            if (FormStatusEnum.Completed.getType().equals(obj.getFormStatus())) {
                ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                errorMessageSubVo.setStaffId(obj.getStaffId());
                errorMessageSubVo.setErrorMessage("员工的绩效考核已完成，无法退回修改，请联系HR:Performance evaluation of the employee was completed,the result can't be changed.Please contactwith HR.");
                errorMessageSubVoList.add(errorMessageSubVo);
                return;
            }

            if (FormStatusEnum.In_Rating.getType().equals(obj.getFormStatus())) {
                ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                errorMessageSubVo.setStaffId(obj.getStaffId());
                errorMessageSubVo.setErrorMessage("员工正处于评分状态，无需再次退回;Rating in progress, no need to return.");
                errorMessageSubVoList.add(errorMessageSubVo);
                return;
            }
            /**
             * 新增逻辑,如果是批量导入则不调取SF的接口
             */
            if (ScoreSourceEnum.IMPORT.getType().equals(obj.getScoreSource())) {
                successList.add(obj.getId());
            } else {

                try {
                    int count = 1;
                    while (count <= 3) {
                        String formId = obj.getFormId();
                        String format = StrUtil.format(sendToPreviousStep, formId);
                        String body;
                        try {
                            body = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();

                        } catch (Exception e) {
                            log.error("退回到表单失败自动重试一次");
                            body = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        }
                        JSONObject jsonObject = JSONUtil.parseObj(body);
                        log.info("第{}次，退回表单步骤返回数据:{},请求URL:{}", count, body, format);
                        /**
                         * 成功后更新状态
                         */
                        JSONObject successJson = jsonObject.getJSONObject("d");
                        /**
                         * 如果返回为null,则返回结果表示退回失败,终止退回
                         * {
                         *     "error": {
                         *         "code": "TWF_INCORRECT_PARAMETER_ERROR",
                         *         "message": {
                         *             "lang": "en-US",
                         *             "value": "Form: 29257 cannot be sent."
                         *         }
                         *     }
                         * }
                         *
                         */
                        if (successJson == null) {
                            //这里count直接等于3,则表示需要直接跳过
                            count ++;
                        } else {
                            String status
                                    = successJson.getJSONObject("CORouteFormStatusBean").getStr("status");
                            if (CommonConstants.SUCCESS_FLAG.equals(status)) {
                                successList.add(obj.getId());
                            }
                            break;
                        }
                    }
                } catch (Exception e) {
                    ErrorMessageSubVo errorMessageSubVo = new ErrorMessageSubVo();
                    errorMessageSubVo.setStaffId(obj.getStaffId());
                    errorMessageSubVo.setErrorMessage("网络异常，请稍后重试；Network anomaly, please try again later.");
                    errorMessageSubVoList.add(errorMessageSubVo);
                    log.error("退回表单失败:{}", e);
                }
            }

        });
        if (CollectionUtil.isNotEmpty(successList)) {
            update(Wrappers.<HrsfPmsuserBase>lambdaUpdate().set(HrsfPmsuserBase::getFormStatus, FormStatusEnum.In_Rating.getType())
                    .in(HrsfPmsuserBase::getId, successList));
        }
        errorMessageVo.setSuccessNum(successList.size());
        errorMessageVo.setErrorNum(userBaseIdList.size() - successList.size());
        errorMessageVo.setErrorMessage(errorMessageSubVoList);
        return R.ok(errorMessageVo);
    }


    //发送响应流方法
    private void setResponseHeader(HttpServletResponse response, String fileName) throws Exception {
        String fileNameUrl = URLEncoder.encode(fileName, "UTF-8");
        //解决下载后文件名称空格变成+的问题
        fileNameUrl = fileNameUrl.replaceAll("\\+", "%20");
        response.setContentType("application/octet-stream;");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameUrl + ";" + "filename*=utf-8''" + fileNameUrl);
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
    }

    @Override
    public void export(Long calibrationId, String assessType, String calibrationName, HttpServletResponse response) {

        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        List<HrsfPmsuserBase> baseList = list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getCalibrationBaseId, calibrationId));
        /**
         * 这里只取分数来源为空的或者分数来源为批量导入的数据
         */
        List<HrsfPmsuserBase> filterBaseList = baseList.stream().filter(data -> StrUtil.isBlank(data.getScoreSource()) ||
                ScoreSourceEnum.IMPORT.getType().equals(data.getScoreSource())).collect(Collectors.toList());
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        WriteFont headWriteFont = new WriteFont();
//        headWriteFont.setFontHeightInPoints((short) 10);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        //如果查不到数据则直接返回为空
        if (CollectionUtil.isEmpty(filterBaseList)) {
            log.error("下载的导入模板数据为空!");
        }
        filterBaseList.forEach(o -> {
            if (Objects.equals(o.getNewHire(), "N")) {
                o.setNewHire("");
            }
            if (Objects.equals(o.getNewPromotion(), "N")) {
                o.setNewPromotion("");
            }
        });
        String fileName;
        try {
            /**
             * 蓝领年度模板和季度模板
             * 采用两套不同模板
             */
            if (AssessTypeEnum.YEAR.getType().equals(assessType)) {
                List<PmsUserExcelYearDownVO> pmsUserExcelVOList = filterBaseList.stream()
                        .map(p -> {
                            PmsUserExcelYearDownVO excelYearVO = HrsfUpmsConvert.INSTANCE.toUserExcelYearVO(p);
                            excelYearVO.setAverageScore(p.getAverageScore() == null ? null : p.getAverageScore().toString() + CommonConstants.PERCENT);

                            return excelYearVO;
                        }).collect(Collectors.toList());
                // 设置公式
                WriteCellData<Double> formula = new WriteCellData<>();
                WriteCellData<Double> formulaFinal = new WriteCellData<>();
                pmsUserExcelVOList.stream().forEach(item -> {
                    item.setWeightRating(formula);
                    item.setFinalRating(formulaFinal);
                    //解决 股东方加入BBAC人员取label，不要取value
                    item.setShareholdersJoinBbac(ShareJoinBbacEnum.getDescByType(item.getShareholdersJoinBbac()));
                    FormulaData formulaData = new FormulaData();
                    formula.setFormulaData(formulaData);
                    // 这里只是例子 如果真的涉及到公式 能内存算好尽量内存算好 公式能不用尽量不用
                    formulaData.setFormulaValue(StrUtil.format(FORMULA_COMMON_YEAR));
                    FormulaData formulaDataFinal = new FormulaData();
                    formulaFinal.setFormulaData(formulaDataFinal);
                    // 这里只是例子 如果真的涉及到公式 能内存算好尽量内存算好 公式能不用尽量不用
                    formulaDataFinal.setFormulaValue(StrUtil.format(FORMULA_COMMON_YEAR_T));
                });
                fileName = "分数导入模板-" + calibrationName;
                this.setResponseHeader(response, fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), PmsUserExcelYearDownVO.class)
                        .registerWriteHandler(new CustomSheetWriteYearHandler(pmsUserExcelVOList.size()))
                        .registerWriteHandler(horizontalCellStyleStrategy)
                        .registerWriteHandler(new CustomCellWriteYearHandler())
                        .sheet("模板")
                        .doWrite(pmsUserExcelVOList);
            } else {
                List<PmsUserExcelDownVO> pmsUserExcelVOList = filterBaseList.stream()
                        .map(p -> HrsfUpmsConvert.INSTANCE.toUserExcelVO(p)).collect(Collectors.toList());
                // 设置公式
                WriteCellData<String> formula = new WriteCellData<>();
                WriteCellData<String> formulaFinal = new WriteCellData<>();
                pmsUserExcelVOList.stream().forEach(item -> {
                    item.setWeightRating(formula);
                    item.setFinalRating(formulaFinal);
                    //解决 股东方加入BBAC人员取label，不要取value
                    item.setShareholdersJoinBbac(ShareJoinBbacEnum.getDescByType(item.getShareholdersJoinBbac()));
                    FormulaData formulaData = new FormulaData();
                    formula.setFormulaData(formulaData);
                    // 这里只是例子 如果真的涉及到公式 能内存算好尽量内存算好 公式能不用尽量不用
                    formulaData.setFormulaValue(StrUtil.format(FORMULA_COMMON));
                    FormulaData formulaDataFinal = new FormulaData();
                    formulaFinal.setFormulaData(formulaDataFinal);
                    // 这里只是例子 如果真的涉及到公式 能内存算好尽量内存算好 公式能不用尽量不用
                    formulaDataFinal.setFormulaValue(StrUtil.format(FORMULA_COMMON_T));
                });
                fileName = "分数导入模板-" + calibrationName;
                this.setResponseHeader(response, fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), PmsUserExcelDownVO.class)
                        .registerWriteHandler(new CustomSheetWriteHandler(pmsUserExcelVOList.size()))
                        .registerWriteHandler(horizontalCellStyleStrategy)
                        .registerWriteHandler(new CustomCellWriteHandler())
                        .sheet("模板").doWrite(pmsUserExcelVOList);
            }
        } catch (Exception e) {
            log.error("download template calibrationId:{},error:{}", calibrationId, e);
        }
    }

    /**
     * 文件上传
     *
     * @param calibrationId
     * @param assessType
     * @param file
     */
    @Override
    public UploadMessageVo upload(Long calibrationId, String assessType, MultipartFile file) {
        UploadMessageVo uploadMessageVo = new UploadMessageVo();
        /**
         * 缓存的数据
         */
        List<HrsfPmsuserBase> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        List messageVoList = new ArrayList<>();
        List existStaffIdList = new ArrayList<>();
        Map<String, HrsfPmsuserBase> pmsUserBaseMap = list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getCalibrationBaseId, calibrationId)).stream().collect(Collectors.toMap(
                p -> p.getStaffId(), p -> p));
        try {
            if (AssessTypeEnum.YEAR.getType().equals(assessType)) {

                EasyExcel.read(file.getInputStream(), PmsUserExcelYearUploadVO.class, new PmsUserExcelYearDataListener(this, cachedDataList, messageVoList, pmsUserBaseMap, existStaffIdList)).sheet().doRead();
            } else {
                EasyExcel.read(file.getInputStream(), PmsUserExcelUploadVO.class, new PmsUserExcelDataListener(this, cachedDataList, messageVoList, pmsUserBaseMap, existStaffIdList)).sheet().doRead();
            }
        } catch (Exception e) {
            log.info("upload error:{}", e);
            throw new CheckedException("上传的文件中存在分数或等级信息填写格式有误!");
        }
        /**
         * 返回对象
         */
        uploadMessageVo.setSuccessNum(cachedDataList.size());
        uploadMessageVo.setErrorNum(messageVoList.size());
        uploadMessageVo.setErrorMessage(messageVoList);
        return uploadMessageVo;
    }

    /**
     * 每天定时同步绩效协调员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncPmsJobHandler() {
        /**
         * 先将筛选器关系表中的数据进行分组
         * 然后将RelationInfo的数据删除
         * 重新生成最新的RelationInfo
         * 在同步到SF系统
         */
        Map<Long, List<HrsfPmsUserRelation>> relationMap = hrsfPmsUserRelationService.list().stream()
                .collect(Collectors.groupingBy(HrsfPmsUserRelation::getPmsUserId));

        /**
         * 2022.07.08
         * 这里将全表清空,防止历史数据导致回写到SF系统
         * 出现账号不匹配的情况
         */
        hrsfPmsUserRelationInfoMapper.deleteTabelData();
        relationMap.entrySet().stream().forEach(data -> {
            Set<HrsfPmsUserRelationInfo> relationInfoList = new HashSet<>();
            data.getValue().forEach(userDTO ->
                    hrsfUserBaseService.getUserInfoList(HrsfUpmsConvert.INSTANCE.toUserDTO(userDTO)).stream().forEach(info ->
                            relationInfoList.add(new HrsfPmsUserRelationInfo(userDTO.getPmsUserId(), userDTO.getPmsStaffId(), info.getStaffId()))));
            if (CollectionUtil.isNotEmpty(relationInfoList)) {
                hrsfPmsUserRelationInfoService.saveBatch(relationInfoList);
            }
        });
        /**
         * 调用SF接口同步绩效协调员
         */
        publisher.publishEvent(PmsUpdateEvent.of(processInactiveEmployees, PmsUpdateEventEnum.I));
    }

    /**
     * 每天定时同步绩效协调员信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncPmsJobHandlerNoPushToSF() {
        /**
         * 先将筛选器关系表中的数据进行分组
         * 然后将RelationInfo的数据删除
         * 重新生成最新的RelationInfo
         * 在同步到SF系统
         */
        Map<Long, List<HrsfPmsUserRelation>> relationMap = hrsfPmsUserRelationService.list().stream()
                .collect(Collectors.groupingBy(HrsfPmsUserRelation::getPmsUserId));

        /**
         * 2022.07.08
         * 这里将全表清空,防止历史数据导致回写到SF系统
         * 出现账号不匹配的情况
         */
        hrsfPmsUserRelationInfoMapper.deleteTabelData();
        relationMap.entrySet().stream().forEach(data -> {
            Set<HrsfPmsUserRelationInfo> relationInfoList = new HashSet<>();
            data.getValue().forEach(userDTO ->
                    hrsfUserBaseService.getUserInfoList(HrsfUpmsConvert.INSTANCE.toUserDTO(userDTO)).stream().forEach(info ->
                            relationInfoList.add(new HrsfPmsUserRelationInfo(userDTO.getPmsUserId(), userDTO.getPmsStaffId(), info.getStaffId()))));
            if (CollectionUtil.isNotEmpty(relationInfoList)) {
                hrsfPmsUserRelationInfoService.saveBatch(relationInfoList);
            }
        });
    }

    @Override
    public boolean writeDataBack(List<WriteDataBackDTO> writeDataBackDTOS) {
        Integer flag = 1;
        if (!CollectionUtils.isEmpty(writeDataBackDTOS)) {
            String formcontentID;
            for (WriteDataBackDTO writeDataBackDTO : writeDataBackDTOS) {
                log.info("======非白领年度开始推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
                String formDataId = writeDataBackDTO.getFormId();
                try {
                    String format = StrUtil.format(sfSyncFormcontentId, formDataId);
                    log.info("非白领年度获取formDataId 的url:{}", format);
                    String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                    writeDataBackDTO.setResponseBody(body);

                    JSONObject jsonObject = JSONUtil.parseObj(body);
                    log.error("macros|非白领年度获取formDataId:{},body:{}", formDataId, body);
                    formcontentID = jsonObject.getJSONObject("d").getStr("formContentId");
                } catch (Exception e) {
                    log.info("非白领年度获取formDataId,抛出异常:{}", e);
                    saveErrorLog2(writeDataBackDTO, flag);
                    continue;
                }
                HashMap<String, Object> paramMap = new HashMap<>();
                HashMap<String, Object> subParamMap = new HashMap<>();
                paramMap.put("__metadata", subParamMap);
                subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT, formcontentID, formDataId, 4, 0, "overall"));
                subParamMap.put("type", "SFOData.FormUserRatingComment");
                paramMap.put("ratingKey", "wf_sect_4_rating");
                paramMap.put("rating", writeDataBackDTO.getPmsLevel().toString());

                try {
                    log.info("paramMap:{}，url:{}", JSONUtil.toJsonStr(paramMap), sfSyncUpsert);
                    String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                    writeDataBackDTO.setResponseBody(body);
                    log.error("macros|非白领年度回写数据到SF系统成功,入参和出参:{}", JSONUtil.toJsonStr(writeDataBackDTO));
                    JSONObject jsonObject2 = JSONUtil.parseObj(body);
                    JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                    String status = returnJson.getStr("status");
                    if (!"OK".equals(status)) {
                        saveErrorLog2(writeDataBackDTO, flag);
                    }
                } catch (Exception e) {
                    log.error("非白领年度回写数据到SF系统,抛出异常:{}", e);
                    saveErrorLog2(writeDataBackDTO, flag);
                }

                if (writeDataBackDTO.getId() != null) {
                    writeDataBackDTO.setEmailStatus(EmailStatusEnum.SUCCESS.getType());
                    saveErrorLog2(writeDataBackDTO, flag);
                }
                log.info("======非白领年度结束推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean whiteYearWriteDataBack(List<WriteDataBackDTO> writeDataBackDTOS) {
        if (CollUtil.isEmpty(writeDataBackDTOS)) {
            return true;
        }
        Integer flag = 2;
        for (WriteDataBackDTO writeDataBackDTO : writeDataBackDTOS) {
            log.info("======白领年度开始推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
            String formDataId = writeDataBackDTO.getFormId();
            String formcontentID;
            try {
                String format = StrUtil.format(sfSyncFormcontentId, formDataId);
                log.info("1.白领年度获取formcontentID url:{}", format);
                String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                writeDataBackDTO.setResponseBody(body);
                JSONObject jsonObject = JSONUtil.parseObj(body);
                log.error("macros|1.白领年度获取formDataId:{},入参和出参:{}", formDataId, JSONUtil.toJsonStr(writeDataBackDTO));
                formcontentID = jsonObject.getJSONObject("d").getStr("formContentId");
            } catch (Exception e) {
                log.info("1.白领年度获取formDataId:{},抛出异常", e);
                saveErrorLog2(writeDataBackDTO, flag);
                continue;
            }

            //步骤二：调接口写入competency rating（最终总分）
            try {
                HashMap<String, Object> paramMap = new HashMap<>();
                HashMap<String, Object> subParamMap = new HashMap<>();
                paramMap.put("__metadata", subParamMap);
                /**
                 * 写入到配置文件
                 *
                 * 1438 -- 723
                 * wf_sect_3__c1438_r  -- wf_sect_3__c723_r
                 */
                subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT_2, formcontentID, formDataId, itemId, "official", 3));
                subParamMap.put("type", "SFOData.FormUserRatingComment");
                paramMap.put("ratingKey", itemIdKey);
                paramMap.put("rating", writeDataBackDTO.getTotalScore().toString());
                String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                writeDataBackDTO.setResponseBody(body);
                log.error("macros|2.白领年度写入最终总分,出参和入参:{}", JSONUtil.toJsonStr(writeDataBackDTO));
                JSONObject jsonObject2 = JSONUtil.parseObj(body);
                JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                String status = returnJson.getStr("status");
                if (!"OK".equals(status)) {
                    saveErrorLog2(writeDataBackDTO, flag);
                    continue;
                }
            } catch (Exception e) {
                log.info("2.白领年度写入最终总分,抛出异常:{}", e);
                saveErrorLog2(writeDataBackDTO, flag);
                continue;
            }
            //步骤三，写入业绩等级
            try {
                HashMap<String, Object> paramMap2 = new HashMap<>();
                HashMap<String, Object> subParamMap = new HashMap<>();
                paramMap2.put("__metadata", subParamMap);
                subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT, formcontentID, formDataId, 4, 0, "overall"));
                subParamMap.put("type", "SFOData.FormUserRatingComment");
                paramMap2.put("ratingKey", "wf_sect_4_rating");
                paramMap2.put("rating", writeDataBackDTO.getPmsLevel().toString());
                String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap2)).basicAuth(userName, pwd).execute().body();
                writeDataBackDTO.setResponseBody(body);
                log.error("macros|3.白领年度写入业绩等级:{}", JSONUtil.toJsonStr(writeDataBackDTO));
                JSONObject jsonObject2 = JSONUtil.parseObj(body);
                JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                String status = returnJson.getStr("status");
                if (!"OK".equals(status)) {
                    saveErrorLog2(writeDataBackDTO, flag);
                    continue;
                }
            } catch (Exception e) {
                log.info("3.白领年度写入业绩等级,抛出异常:{}", e);
                saveErrorLog2(writeDataBackDTO, flag);
                continue;
            }
            //步骤四：写入潜力等级和可变动性
            try {
                HashMap<String, Object> paramMap = new HashMap<>();
                HashMap<String, Object> subMap = new HashMap<>();
                List<HashMap<String, Object>> subListMap = new ArrayList<>();
                subMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_CUSTOM_SECTION, formcontentID, formDataId, 5));
                subMap.put("type", "SFOData.FormCustomSection");
                paramMap.put("__metadata", subMap);
                paramMap.put("customElement", subListMap);

                HashMap<String, Object> paramMap2 = new HashMap<>();
                HashMap<String, Object> subMap2 = new HashMap<>();
                paramMap2.put("__metadata", subMap2);
                paramMap2.put("valueKey", "wf_sect_5_e_ele_10");
                paramMap2.put("value", PotentialLevelEnum.getDescByType(writeDataBackDTO.getPotentialLevel()));
                subMap2.put("uri", StrUtil.format(UPDATE_PMS_FORM_CUSTOM_ELEMENT, "ele_1", formcontentID, formDataId, -1, 5));
                subMap2.put("tupe", "SFOData.FormCustomElement");
                subListMap.add(paramMap2);

                HashMap<String, Object> paramMap3 = new HashMap<>();
                HashMap<String, Object> subMap3 = new HashMap<>();
                paramMap3.put("__metadata", subMap3);
                paramMap3.put("valueKey", "wf_sect_5_e_ele_21");
                paramMap3.put("value", VariableEnum.getDescByType(writeDataBackDTO.getVariable()));
                subMap3.put("uri", StrUtil.format(UPDATE_PMS_FORM_CUSTOM_ELEMENT, "ele_2", formcontentID, formDataId, -1, 5));
                subMap3.put("tupe", "SFOData.FormCustomElement");
                subListMap.add(paramMap3);

                String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                writeDataBackDTO.setResponseBody(body);
                log.error("macros|4.白领年度写入潜力等级和可变动性,入参和出参:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                JSONObject jsonObject2 = JSONUtil.parseObj(body);
                JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                String status = returnJson.getStr("status");
                if (!"OK".equals(status)) {
                    saveErrorLog2(writeDataBackDTO, flag);
                }
            } catch (Exception e) {
                log.info("4.白领年度写入潜力等级和可变动性:{},抛出异常", e);
                saveErrorLog2(writeDataBackDTO, flag);
            }
            if (writeDataBackDTO.getId() != null) {
                writeDataBackDTO.setEmailStatus(EmailStatusEnum.SUCCESS.getType());
                saveErrorLog2(writeDataBackDTO, flag);
            }
            log.info("======白领年度结束推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
        }
        return true;
    }

    @Override
    public boolean whiteYearFinalWriteDataBack(List<WriteDataBackDTO> writeDataBackDTOS) {
        if (CollUtil.isEmpty(writeDataBackDTOS)) {
            return true;
        }
        Integer flag = 3;
        for (WriteDataBackDTO writeDataBackDTO : writeDataBackDTOS) {
            log.info("======白领年度/HRL3开始推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
            int step = writeDataBackDTO.getStep();
            if (step < 1) {
                //将绩效考核结果回写到SF的员工档案中
                try {
                    HashMap<String, Object> paramMap = new HashMap<>();
                    HashMap<String, Object> subParamMap = new HashMap<>();
                    paramMap.put("__metadata", subParamMap);
                    subParamMap.put("uri", StrUtil.format(UPDATE_PMS_CUST_PERFORMANCE_RATING, writeDataBackDTO.getEndDate(), writeDataBackDTO.getStaffId()));
                    paramMap.put("cust_year", writeDataBackDTO.getAssessYear());
                    paramMap.put("cust_type", writeDataBackDTO.getAssessType());
                    paramMap.put("cust_rating", writeDataBackDTO.getTotalScore().toString());
                    //取绩效员工基础表HRSF_PMSUSER_BASE中的业绩等级字段值；
                    paramMap.put("cust_perank", MappingEnum.getTypeByDesc(writeDataBackDTO.getPmsLevel().toString()));
                    //取绩效员工基础表HRSF_PMSUSER_BASE中的潜力等级字段值；
                    paramMap.put("cust_porank", writeDataBackDTO.getPotentialLevel());
                    //取绩效员工基础表HRSF_PMSUSER_BASE中的可变动性字段值；
                    paramMap.put("cust_trend", writeDataBackDTO.getVariable());
                    paramMap.put("cust_status", "A");
                    String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                    writeDataBackDTO.setResponseBody(body);
                    log.error("macros|6.白领年度/HRL3将绩效考核结果回写到SF的员工档案中:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                    JSONObject jsonObject2 = JSONUtil.parseObj(body);
                    JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                    String status = returnJson.getStr("status");
                    if (!"OK".equals(status)) {
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                } catch (Exception e) {
                    log.info("6.白领年度/HRL3将绩效考核结果回写到SF的员工档案中,抛出异常:{}", e);
                    saveErrorLog2(writeDataBackDTO, flag);
                    continue;
                }
            }
            if (step < 2) {
                //将绩效考核结果回写到人才信息中
                try {
                    HashMap<String, Object> paramMap = new HashMap<>();
                    HashMap<String, Object> subParamMap = new HashMap<>();
                    paramMap.put("__metadata", subParamMap);
                    subParamMap.put("uri", "TrendData_SysOverallPotential");
                    subParamMap.put("type", "SFOData.TrendData_SysOverallPotential");
                    paramMap.put("userId", writeDataBackDTO.getStaffId());
                    paramMap.put("endDate", StrUtil.format(UPDATE_PMS_DATE, getLong(writeDataBackDTO.getEndDate())));
                    paramMap.put("rating", PotentialLevelEnum.getMappingByType(writeDataBackDTO.getPotentialLevel()));
                    paramMap.put("startDate", StrUtil.format(UPDATE_PMS_DATE, getLong(writeDataBackDTO.getStartDate())));
                    String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                    writeDataBackDTO.setResponseBody(body);
                    log.warn("macros|7.白领年度/HRL3绩效考核结果回写到人才信息中:{},请求的入参:{}", JSONUtil.toJsonStr(writeDataBackDTO), JSONUtil.toJsonStr(paramMap));

                    JSONObject jsonObject2 = JSONUtil.parseObj(body);
                    JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                    String status = returnJson.getStr("status");
                    if (!"OK".equals(status)) {
                        writeDataBackDTO.setStep(1);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                } catch (HttpException err) {
                    //这里发生超时，先不做处理继续执行
                    log.error("7.白领年度/HRL3绩效考核结果回写到人才信息中,超时异常:{}", err);
                } catch (Exception e) {
                    writeDataBackDTO.setStep(1);
                    log.error("7.白领年度/HRL3绩效考核结果回写到人才信息中,抛出异常:{}", e);
                    saveErrorLog2(writeDataBackDTO, flag);
                    continue;
                }
            }


            if (Objects.equals(writeDataBackDTO.getUserLevel(), UserLevelEnum.L5.name()) ||
                    Objects.equals(writeDataBackDTO.getUserLevel(), UserLevelEnum.L6.name())) {
                //判断若员工的职级字段=L5或L6，调取一次接口，将表单发送至下一步骤
                if (step < 3) {
                    try {
                        String format = StrUtil.format(sfSyncSendToNextStep, writeDataBackDTO.getFormId());
                        log.info("1.白领年度/HRL3发送到下一步 url:{}", format);
                        String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.info("1.白领年度/HRL3发送到下一步,入参和出参:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                        JSONObject jsonObject = JSONUtil.parseObj(body);
                        JSONObject returnJson = jsonObject.getJSONObject("d").getJSONObject("CORouteFormStatusBean");
                        String status = returnJson.getStr("status");
                        if (!"Success".equals(status)) {
                            saveErrorLog2(writeDataBackDTO, flag);
                        }
                    } catch (HttpException err) {
                        //这里发生超时，先不做处理继续执行
                        log.error("1.白领年度/HRL3发送到下一步骤,超时抛出异常:{}", err);

                    } catch (Exception e) {
                        log.error("1.白领年度/HRL3发送到下一步骤,抛出异常:{}", e);
                        writeDataBackDTO.setStep(2);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                }
            } else if (Objects.equals(writeDataBackDTO.getUserLevel(), UserLevelEnum.L3.name()) ||
                    Objects.equals(writeDataBackDTO.getUserLevel(), UserLevelEnum.L4.name())) {
                //步骤一：调接口获取formcontentID：
                String formDataId = writeDataBackDTO.getFormId();
                String formcontentID;
                if (step < 5) {
                    try {
                        String format = StrUtil.format(sfSyncFormcontentId, formDataId);
                        log.info("1.白领年度/HRL3获取formcontentID的url:{}", format);
                        String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        JSONObject jsonObject = JSONUtil.parseObj(body);
                        log.warn("macros1.白领年度/HRL3formDataId:{},body:{}", formDataId, body);
                        formcontentID = jsonObject.getJSONObject("d").getStr("formContentId");
                    } catch (Exception e) {
                        log.error("1.白领年度/HRL3获取formcontentID的url", e);
                        writeDataBackDTO.setStep(2);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                    //步骤二：调接口写入competency rating（最终总分）
                    try {
                        HashMap<String, Object> paramMap = new HashMap<>();
                        HashMap<String, Object> subParamMap = new HashMap<>();
                        paramMap.put("__metadata", subParamMap);
                        /**
                         * 配置文件
                         * 1438 -- 723
                         * wf_sect_3__c1438_r -- wf_sect_3__c723_r
                         */
                        subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT_2, formcontentID, formDataId, itemId, "official", 3));
                        subParamMap.put("type", "SFOData.FormUserRatingComment");
                        paramMap.put("ratingKey", itemIdKey);
                        paramMap.put("rating", writeDataBackDTO.getTotalScore().toString());
                        log.info("paramMap:{}", JSONUtil.toJsonStr(paramMap));
                        String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.warn("macros|2.白领年度/HRL3回写最终总分,入参出参值:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                        JSONObject jsonObject2 = JSONUtil.parseObj(body);
                        JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                        String status = returnJson.getStr("status");
                        if (!"OK".equals(status)) {
                            writeDataBackDTO.setStep(2);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    } catch (Exception e) {
                        log.info("2.白领年度/HRL3回写最终总分,抛出异常:{}", e);
                        writeDataBackDTO.setStep(2);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                    //步骤三，写入业绩等级
                    try {
                        HashMap<String, Object> paramMap2 = new HashMap<>();
                        HashMap<String, Object> subParamMap = new HashMap<>();
                        paramMap2.put("__metadata", subParamMap);
                        subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT, formcontentID, formDataId, 4, 0, "overall"));
                        subParamMap.put("type", "SFOData.FormUserRatingComment");
                        paramMap2.put("ratingKey", "wf_sect_4_rating");
                        paramMap2.put("rating", writeDataBackDTO.getPmsLevel().toString());
                        String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap2)).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.warn("macros|3.白领年度/HRL3写入业绩等级:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                        JSONObject jsonObject2 = JSONUtil.parseObj(body);
                        JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                        String status = returnJson.getStr("status");
                        if (!"OK".equals(status)) {
                            writeDataBackDTO.setStep(3);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    } catch (Exception e) {
                        log.info("3.白领年度/HRL3写入业绩等级,抛出异常:{}", e);
                        writeDataBackDTO.setStep(3);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                    //步骤四：写入潜力等级和可变动性
                    try {
                        HashMap<String, Object> paramMap = new HashMap<>();
                        HashMap<String, Object> subMap = new HashMap<>();
                        List<HashMap<String, Object>> subListMap = new ArrayList<>();
                        subMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_CUSTOM_SECTION, formcontentID, formDataId, 5));
                        subMap.put("type", "SFOData.FormCustomSection");
                        paramMap.put("__metadata", subMap);
                        paramMap.put("customElement", subListMap);

                        HashMap<String, Object> paramMap2 = new HashMap<>();
                        HashMap<String, Object> subMap2 = new HashMap<>();
                        paramMap2.put("__metadata", subMap2);
                        paramMap2.put("valueKey", "wf_sect_5_e_ele_10");
                        paramMap2.put("value", PotentialLevelEnum.getDescByType(writeDataBackDTO.getPotentialLevel()));
                        subMap2.put("uri", StrUtil.format(UPDATE_PMS_FORM_CUSTOM_ELEMENT, "ele_1", formcontentID, formDataId, -1, 5));
                        subMap2.put("type", "SFOData.FormCustomElement");
                        subListMap.add(paramMap2);

                        HashMap<String, Object> paramMap3 = new HashMap<>();
                        HashMap<String, Object> subMap3 = new HashMap<>();
                        paramMap3.put("__metadata", subMap3);
                        paramMap3.put("valueKey", "wf_sect_5_e_ele_21");
                        paramMap3.put("value", VariableEnum.getDescByType(writeDataBackDTO.getVariable()));
                        subMap3.put("uri", StrUtil.format(UPDATE_PMS_FORM_CUSTOM_ELEMENT, "ele_2", formcontentID, formDataId, -1, 5));
                        subMap3.put("type", "SFOData.FormCustomElement");
                        subListMap.add(paramMap3);


                        String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.warn("macros|4.白领年度/HRL3写入潜力等级和可变动性:入参和出参{}", JSONUtil.toJsonStr(writeDataBackDTO));

                        JSONObject jsonObject2 = JSONUtil.parseObj(body);
                        JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                        String status = returnJson.getStr("status");
                        if (!"OK".equals(status)) {
                            writeDataBackDTO.setStep(4);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }

                    } catch (Exception e) {
                        log.info("4.白领年度/HRL3写入潜力等级和可变动性,抛出异常:{}", e);
                        writeDataBackDTO.setStep(4);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                }
                if (step < 6) {
                    try {
                        String format = StrUtil.format(sfSyncSendToNextStep, writeDataBackDTO.getFormId());
                        log.info("5.白领年度/HRL3发送到下一步骤 url:{}", format);
                        String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.warn("macros|5.白领年度/HRL3发送到下一步骤,入参和出参:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                        JSONObject jsonObject = JSONUtil.parseObj(body);
                        JSONObject returnJson = jsonObject.getJSONObject("d").getJSONObject("CORouteFormStatusBean");
                        String status = returnJson.getStr("status");
                        if (!"Success".equals(status)) {
                            writeDataBackDTO.setStep(5);
                            saveErrorLog2(writeDataBackDTO, flag);
                        }
                    } catch (HttpException err) {
                        //这里发生超时，先不做处理继续执行
                        log.error("5.白领年度/HRL3发送到下一步骤,超时抛出异常{}", err);
                    } catch (Exception e) {
                        log.info("5.白领年度/HRL3发送到下一步骤,抛出异常{}", e);
                        writeDataBackDTO.setStep(5);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                }
            }

            if (writeDataBackDTO.getId() != null) {
                /**
                 * 这里是处理重试后成功
                 * 将状态置为SUCCESS
                 * 下次扫描不会出现该条数据
                 */
                writeDataBackDTO.setEmailStatus(EmailStatusEnum.SUCCESS.getType());
                saveErrorLog2(writeDataBackDTO, flag);
            }
            log.info("======白领年度/HRL3结束推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
        }

        return false;
    }

    @Override
    public List<HrsfPmsuserBase> selectByStaffIdAndASSESS(List<String> staffIds, String assesYear, String assesType) {
        List<HrsfPmsuserBase> list = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().in(HrsfPmsuserBase::getStaffId, staffIds)
                .eq(HrsfPmsuserBase::getAssessYear, assesYear)
                .eq(HrsfPmsuserBase::getAssessType, assesType));

        convertPmsLevelDesc(list);
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveCompanyLevelCalibration(List<HrsfPmsuserBase> pmsUserList, ArrayList<String> failNameList) {
        pmsUserList.stream().forEach(data -> {
            try {
                save(data);
            } catch (Exception e) {
                log.error("添加公司级校准会议人员员工号:{},失败:{}", data.getStaffId(), e);
                failNameList.add(data.getFullName());
            }
        });
        if (failNameList.size() > 0) {
            throw new CheckedException("添加公司级校准会议人员失败");
        }
        return true;
    }

    @Override
    public Object removeCalibrationBaseId(Long id) {

        update(Wrappers.<HrsfPmsuserBase>lambdaUpdate().set(HrsfPmsuserBase::getCalibrationBaseId, "")
                .eq(HrsfPmsuserBase::getCalibrationBaseId, id));
        return null;
    }

    @Override
    public Object batchUpdateByCalibrationBaseId(Long baseId) {
        List<HrsfPmsuserBase> baseList = selectByBaseId(baseId);
        if (CollUtil.isNotEmpty(baseList)) {
            HrsfPmsuserBase hrsfPmsuserBase = baseList.get(0);
            List<HrsfPmsuserBase> list = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getAssessYear, hrsfPmsuserBase.getAssessYear())
                    .eq(HrsfPmsuserBase::getAssessType, hrsfPmsuserBase.getAssessType())
                    .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType()));
            if (CollUtil.isNotEmpty(list)) {
                Map<String, HrsfPmsuserBase> collect = baseList.stream().collect(Collectors.toMap(HrsfPmsuserBase::getStaffId, e -> e, (oldVal, newVal) -> newVal));
                list.forEach(o -> {
                    HrsfPmsuserBase company = collect.get(o.getStaffId());
                    if (Objects.nonNull(company)) {
                        o.setTotalScore(company.getTotalScore());
                        o.setPmsLevel(company.getPmsLevel());
                        o.setPmsLevelDesc(company.getPmsLevelDesc());
                        o.setPotentialLevel(company.getPotentialLevel());
                        o.setPotentialLevelDesc(company.getPotentialLevelDesc());
                        o.setVariable(company.getVariable());
                        o.setVariableDesc(company.getVariableDesc());
                        updateById(o);
                    }
                });
            }

        }
        return null;
    }

    @Override
    public Object removeCalibrationUser(AddCalibrationUserDTO addCalibrationUserDTO) {
        List<HrsfPmsuserBase> list = list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getCalibrationBaseId, addCalibrationUserDTO.getCalibrationId())
                .in(HrsfPmsuserBase::getStaffId, addCalibrationUserDTO.getStaffIds()));


        if (CollUtil.isEmpty(list)) {
            return null;
        }
        list.forEach(o -> {
            if (Objects.nonNull(o.getId())) {
                if (Objects.equals(o.getLevelFlag(), LevelFlagEnum.IS_CHECK.getType())) {
                    hrsfPmsuserBaseMapper.updateCalibrationById(o.getId());
                } else {
                    hrsfPmsuserBaseMapper.deleteUserById(o.getId());
                }
            }
        });

        return null;
    }

    @Override
    public List<HrsfPmsuserBase> selectPmsUserBaseByStaffIdList(List<String> staffIds, String assesYear, String assesType) {
        List<HrsfPmsuserBase> list = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().in(HrsfPmsuserBase::getStaffId, staffIds)
                .eq(HrsfPmsuserBase::getAssessYear, assesYear)
                .eq(HrsfPmsuserBase::getAssessType, assesType)
                .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType()));

        convertPmsLevelDesc(list);
        return list;
    }

    @Override
    public Map<String, Object> getCalibrationPotencyById(CalibrationListQueryDTO queryDTO, Boolean isVerify) {
        if (!checkAuthority(queryDTO.getTaskId(), queryDTO.getStatus(), isVerify)) {
            throw new CheckedException("该用户没有权限查看数据,请联系管理员");
        }
        LinkedHashMap<String, Object> returnMap = new LinkedHashMap<>(32);
        /**
         * 7.人数指导，针对杰出和优秀的等级区间，需要根据规则计算建议的最大区间人数。计算规则：
         * a.判断若校准会议中的总人数>4个人：
         * 杰出人数上限：该校准会议中的总人数×10%，四舍五入；（比如总共有36人，杰出区间显示为（<=4人）
         * 优秀+杰出人数上限：采取两种计算方式得出两个值，取最大值；
         * 第一种为该校准会议中的总人数×35%，并四舍五入；
         * 第二种为：杰出等级上限人数（该校准会议中的总人数×10%，四舍五入）+优秀等级上限人数（该校准会议中的总人数×25%，四舍五入）
         * （比如总共38人，按照第一种算法，人数上限为13人；按照第二种算法，人数上限为14人，取14；杰出+优秀区间展示位（<=14人）
         *
         *
         * b.判断若校准会议中的总人数<=4个人：
         * 杰出人数上限为1
         * 优秀+杰出人数上限：采取两种计算方式得出两个值，取最大值(同上)
         * 第一种为该校准会议中的总人数×35%，并四舍五入；
         * 第二种为：杰出等级上限人数（该校准会议中的总人数×10%，四舍五入）+优秀等级上限人数（该校准会议中的总人数×25%，四舍五入）
         */
        if (queryDTO.getCalibrationBaseId() != null) {
            List<HrsfPmsuserBase> noFilterList = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId,
                    queryDTO.getCalibrationBaseId()));
            int sizeFixed = noFilterList.size();

            List<HrsfPmsuserBase> baseList = list(buildQueryWrapperCalibration(queryDTO));
            int size = baseList.size();
            /**
             * fix bug 190
             * 2022-07-05
             * 如果总人数<=1的话,则返回null
             */
            if (size < 1) {
                returnMap.put("outStandingTotal", null);
                returnMap.put("exelOutTotal", null);
                returnMap.put("total", sizeFixed);
            } else if (size == 1) {
                returnMap.put("outStandingTotal", 1);
                returnMap.put("exelOutTotal", 1);
                returnMap.put("total", sizeFixed);
            } else {
                BigDecimal exelOutTotal = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.15)).setScale(0, BigDecimal.ROUND_UP);
                BigDecimal outStandingTotal = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.25)).setScale(0, BigDecimal.ROUND_UP);
                returnMap.put("exelOutTotal", exelOutTotal);
                returnMap.put("outStandingTotal", outStandingTotal);
                returnMap.put("total", sizeFixed);
            }
        }
        try {
            List<HrsfPmsuserBase> pmsList = list(buildQueryWrapperCalibrationPotency(queryDTO));

            if (CollectionUtil.isEmpty(pmsList)) {
                Arrays.stream(PotencyListDataEnum.values()).forEach(data ->
                        returnMap.put(data.getResult(), new ArrayList<>()));
                return returnMap;
            }

            convertPmsLevelDesc(pmsList);

            //获取用户头像信息
            List<String> staffIdList = pmsList.stream().map(HrsfPmsuserBase::getStaffId).collect(Collectors.toList());
            List<HrsfUserBase> list = hrsfUserBaseService.list(Wrappers.<HrsfUserBase>lambdaQuery().
                    in(HrsfUserBase::getStaffId, staffIdList).select(HrsfUserBase::getPhoto, HrsfUserBase::getStaffId));
            Map<String, String> photoMap = list.stream().filter(p -> StrUtil.isNotBlank(p.getPhoto()))
                    .collect(Collectors.toMap(HrsfUserBase::getStaffId, HrsfUserBase::getPhoto));
            /**
             * 设置用户头像
             */
            LinkedHashMap<String, List<HrsfPmsUserPhotoVO>> pmsMap = pmsList.stream().map(p -> {
                HrsfPmsUserPhotoVO photoVO = HrsfUpmsConvert.INSTANCE.toHrsfPmsUserPhotoVO(p);
                photoVO.setPhoto(photoMap.get(photoVO.getStaffId()));
                return photoVO;
            }).collect(Collectors.toList())
                    //对里面的先分组然后在根据最终得分进行排序
                    .stream().sorted(Comparator.comparing(HrsfPmsUserPhotoVO::getTotalScore, Comparator.reverseOrder())).collect(Collectors.groupingBy(
                            p -> StrUtil.join("_", Arrays.asList(p.getPotentialLevel()
                                    , p.getVariable())), LinkedHashMap::new, Collectors.toList()));
            Arrays.stream(PotencyListDataEnum.values()).forEach(data ->
                    returnMap.put(data.getResult(), pmsMap.get(data.getType()) == null ?
                            new ArrayList<>() : pmsMap.get(data.getType())));


        } catch (Exception e) {
            log.error("查询潜力等级视图失败:{}", e);
            throw new ServiceException(null, "查询潜力等级视图失败,请稍后再试");
        }
        return returnMap;

    }

    @Override
    public List<HrsfPmsuserBase> selectByCalibrationUserDTO(AddCalibrationUserDTO addCalibrationUserDTO) {
        Wrapper<HrsfPmsuserBase> hrsfPmsuserBaseWrapper = buildQueryWrapper(addCalibrationUserDTO);
        return list(hrsfPmsuserBaseWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean compensator(String assessYear, String assessType, Long calibrationBaseId) {
        R<HrsfPmstart> pmsStartInfo = remotePmsService.getPmsStartInfoInner(assessYear, assessType, SecurityConstants.FROM_IN);
        List<HrsfPmsuserBase> list = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getAssessType, assessType)
                .eq(HrsfPmsuserBase::getAssessYear, assessYear));
        Map<String, Long> baseMap = list.stream().collect(Collectors.toMap(HrsfPmsuserBase::getStaffId, HrsfPmsuserBase::getId));
        Map<String, Long> empIdMap = list.stream().collect(Collectors.toMap(HrsfPmsuserBase::getStaffId, HrsfPmsuserBase::getId,(p1,p2)->p2));
        if (pmsStartInfo.getCode() == 0) {
            HrsfPmstart hrsfPmstart = pmsStartInfo.getData();
            if (AssessTypeEnum.YEAR.getType().equals(hrsfPmstart.getAssesType())) {
                publisher.publishEvent(PmsUpdateEvent.of(sfSyncPmsRating, hrsfPmstart.getAssesYear(), hrsfPmstart.getAssesType(), PmsUpdateEventEnum.B, baseMap, false,empIdMap));
            }
//            publisher.publishEvent(PmsUpdateEvent.of(sfSyncPunish, hrsfPmstart.getStartDate(), hrsfPmstart.getEndDate(), PmsUpdateEventEnum.D, baseMap));
//            LocalDate startDate = hrsfPmstart.getStartDate();
//            LocalDate endDate = hrsfPmstart.getEndDate();
//            String startDateStr = LocalDateTime.of(startDate, LocalTime.MIN).minusHours(8).format(DateTimeFormatter.ofPattern(DatePattern.UTC_PATTERN));
//            String endDateStr = LocalDateTime.of(endDate, LocalTime.MAX).minusHours(8).format(DateTimeFormatter.ofPattern(DatePattern.UTC_PATTERN));
//            publisher.publishEvent(PmsUpdateEvent.of(sfSyncFormHeader, startDateStr, null, endDateStr, PmsUpdateEventEnum.A, baseMap));
        }
        return true;
    }

    @Override
    public Object removeFormId(HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO) {
        /**
         * 修改问题清单-358 update 2022-07-27
         List<HrsfPmsuserBase> hrsfPmsuserBases = selectPmsUserBaseByStaffIdList(hrsfPmsUserBaseDTO.getStaffIds(), hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType());
         if (CollUtil.isNotEmpty(hrsfPmsuserBases)) {
         List<String> collect = hrsfPmsuserBases.stream().filter(e -> Objects.equals(e.getFormStatus(), FormStatusEnum.Completed.getType())).map(HrsfPmsuserBase::getFullName).collect(Collectors.toList());
         if (CollUtil.isNotEmpty(collect)) {
         String join = StringUtils.join(collect, ",");
         String str = StrUtil.format("【{}】员工已存在于已完成校准会议中，请先将其从校准会议中移除，再进行添加操作；", join);
         throw new CheckedException(str);
         }
         }*/

        if (CollUtil.isNotEmpty(hrsfPmsUserBaseDTO.getStaffIds())) {
            for (String staffId : hrsfPmsUserBaseDTO.getStaffIds()) {
                hrsfPmsuserBaseMapper.deleteByYearAndTypeAndStaffId(hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType(), staffId);
            }
        }
        remotePmsService.deleteFilter(hrsfPmsUserBaseDTO.getStaffIds(), hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType(), SecurityConstants.FROM_IN);
        return null;
    }

    /**
     * 将TotalScoreOriginal字段更新成TotalScore的值
     *
     * @param baseId
     */
    @Override
    public void updateTotalScoreOriginal(Long baseId) {
        hrsfPmsuserBaseMapper.updateTotalScoreOriginal(baseId);
    }

    /**
     * @param
     * @return
     */
    @Override
    public Boolean syncUserBaseInfo(long current, long size) {
        Page page = new Page(current, size);
        Page<HrsfPmsuserBase> pageObj = baseMapper.selectPage(page, Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .select(HrsfPmsuserBase::getStaffId, HrsfPmsuserBase::getId)
                .ne(HrsfPmsuserBase::getFormStatus, FormStatusEnum.Completed.getType()));
        try {
            List<HrsfPmsuserBase> pmsUserBaseList = pageObj.getRecords();
            //是否mp在oracle组装sql的问题需要进一步排查
            log.info("查询到的pmsUserBaseList={}", pmsUserBaseList);
            //首先查询绩效基础表的数据(表单状态不是已完成)
            List<String> staffIdList = pmsUserBaseList.stream().map(HrsfPmsuserBase::getStaffId)
                    .distinct().filter(obj -> StrUtil.isNotBlank(obj)).collect(Collectors.toList());
            log.info("查询到的staffIdList={}", staffIdList);
            List<HrsfUserBase> hrsfUserBaseList = hrsfUserBaseService.lambdaQuery().select(
                    HrsfUserBase::getStaffId
                    , HrsfUserBase::getNewHire
                    , HrsfUserBase::getNewPromotion
                    , HrsfUserBase::getShareholdersJoinBbac
                    , HrsfUserBase::getAbsence
                    , HrsfUserBase::getMaternityLeave
                    , HrsfUserBase::getAttendanceRate)
                    .in(HrsfUserBase::getStaffId, staffIdList).list();
            Map<String, HrsfUserBase> hrsfUserBaseMap =
                    hrsfUserBaseList.stream().collect(Collectors.toMap(HrsfUserBase::getStaffId, data -> data, (p1, p2) -> p2));
            /**
             * 将主数据的最新信息,保存到绩效员工基础表中
             */
            List<HrsfPmsuserBase> baseList = pmsUserBaseList.stream().map(data -> {
                HrsfPmsuserBase hrsfPmsuserBase = new HrsfPmsuserBase();
                HrsfUserBase hrsfUserBase = hrsfUserBaseMap.get(data.getStaffId());
                if(hrsfUserBase!=null){
                    hrsfPmsuserBase.setNewHire(hrsfUserBase.getNewHire());
                    hrsfPmsuserBase.setNewPromotion(hrsfUserBase.getNewPromotion());
                    hrsfPmsuserBase.setShareholdersJoinBbac(hrsfUserBase.getShareholdersJoinBbac());
                    hrsfPmsuserBase.setAbsence(hrsfUserBase.getAbsence());
                    hrsfPmsuserBase.setAttendanceRate(hrsfUserBase.getAttendanceRate());
                    hrsfPmsuserBase.setMaternityLeave(hrsfUserBase.getMaternityLeave());
                }
                hrsfPmsuserBase.setId(data.getId());
                return hrsfPmsuserBase;
            }).collect(Collectors.toList());
            if(CollectionUtil.isNotEmpty(baseList)){
                updateBatchById(baseList);
            }
        } catch (Exception e) {
            log.error("同步绩效员工基础表的基本信息失败:{}", e);
        }
        log.info("同步绩效员工基础表的基本信息成功{}次,{}", current, pageObj.hasNext());
        return pageObj.hasNext();
    }

    @Override
    public void updatePmsLevelById(List<Long> cachedDataListTwo) {
        hrsfPmsuserBaseMapper.updatePmsLevelById(cachedDataListTwo);
    }

    /**
     * 查询等级视图
     *
     * @param queryDTO
     * @return
     */
    private Wrapper<HrsfPmsuserBase> buildQueryWrapperCalibrationPotency(CalibrationListQueryDTO queryDTO) {
        LambdaQueryWrapper<HrsfPmsuserBase> wrapper = Wrappers.lambdaQuery();
        if (CollectionUtil.isNotEmpty(queryDTO.getSystem())) {
            wrapper.in(HrsfPmsuserBase::getSystem, queryDTO.getSystem());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getDepartment())) {
            wrapper.in(HrsfPmsuserBase::getDepartment, queryDTO.getDepartment());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getSection())) {
            wrapper.in(HrsfPmsuserBase::getSection, queryDTO.getSection());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getUserGroup())) {
            wrapper.in(HrsfPmsuserBase::getUserGroup, queryDTO.getUserGroup());
        }
        if (queryDTO.getCalibrationBaseId() != null) {
            wrapper.eq(HrsfPmsuserBase::getCalibrationBaseId, queryDTO.getCalibrationBaseId());
        }
        if (StrUtil.isNotBlank(queryDTO.getFormStatus())) {
            wrapper.eq(HrsfPmsuserBase::getFormStatus, queryDTO.getFormStatus());
        }

        if (StrUtil.isNotBlank(queryDTO.getEmploymentType())) {
            /**
             * 如果传过来是外方,则直接将EmploymentType等于戴姆勒集团调入
             * 否则 不等于戴姆勒集团调入
             */
            if (ChineseForeignTypeEnum.Foreign.getType().equals(queryDTO.getEmploymentType())) {
                wrapper.eq(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            } else {
                wrapper.ne(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getStaffIdList())) {
            wrapper.in(HrsfPmsuserBase::getStaffId, queryDTO.getStaffIdList());
        }

        wrapper.isNotNull(HrsfPmsuserBase::getPotentialLevel).isNotNull(HrsfPmsuserBase::getVariable);
        return wrapper;
    }

    @Override
    public HrsfOrganizationVO getCalibrationPmsSelectByYearAndType(String assesYear, String assesType) {
        //分别去员工基础表中查询相应的系统、部门、科室、群组
        return new HrsfOrganizationVO(
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT SYSTEM").lambda()
                        .eq(HrsfPmsuserBase::getAssessYear, assesYear).eq(HrsfPmsuserBase::getAssessType, assesType).isNotNull(HrsfPmsuserBase::getSystem).orderByDesc(HrsfPmsuserBase::getSystem)).stream().map(HrsfPmsuserBase::getSystem).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT DEPARTMENT").lambda()
                        .eq(HrsfPmsuserBase::getAssessYear, assesYear).eq(HrsfPmsuserBase::getAssessType, assesType).isNotNull(HrsfPmsuserBase::getDepartment).orderByDesc(HrsfPmsuserBase::getDepartment)).stream().map(HrsfPmsuserBase::getDepartment).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT SECTION").lambda()
                        .eq(HrsfPmsuserBase::getAssessYear, assesYear).eq(HrsfPmsuserBase::getAssessType, assesType).isNotNull(HrsfPmsuserBase::getSection).orderByDesc(HrsfPmsuserBase::getSection)).stream().map(HrsfPmsuserBase::getSection).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT USER_GROUP").lambda()
                        .eq(HrsfPmsuserBase::getAssessYear, assesYear).eq(HrsfPmsuserBase::getAssessType, assesType).isNotNull(HrsfPmsuserBase::getUserGroup).orderByDesc(HrsfPmsuserBase::getUserGroup)).stream().map(HrsfPmsuserBase::getUserGroup).collect(Collectors.toList()),
                list(Wrappers.<HrsfPmsuserBase>query().select("DISTINCT USER_LEVEL").lambda()
                        .eq(HrsfPmsuserBase::getAssessYear, assesYear).eq(HrsfPmsuserBase::getAssessType, assesType).isNotNull(HrsfPmsuserBase::getUserLevel).orderByDesc(HrsfPmsuserBase::getUserLevel)).stream().map(HrsfPmsuserBase::getUserLevel).collect(Collectors.toList()));

    }

    @Override
    public List<HrsfUserVO> getCalibrationPmsSelectNameByYearAndType(String assesYear, String assesType, String fullName) {
        LambdaQueryWrapper<HrsfPmsuserBase> wrapper = Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getAssessYear, assesYear)
                .eq(HrsfPmsuserBase::getAssessType, assesType);
        if (StrUtil.isNotBlank(fullName)) {
            List<HrsfPmsuserBase> hrsfPmsuserBases = hrsfPmsuserBaseMapper.selectByFullNameOrStaffId(assesYear, assesType, fullName);
            return hrsfPmsuserBases.stream()
                    .map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
        } else {
            return list(wrapper).stream()
                    .map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
        }
    }


    @Override
    public IPage<HrsfPmsuserBase> getPmsUserListByOverView(AddCalibrationUserDTO addCalibrationUserDTO) {
        Wrapper<HrsfPmsuserBase> wrapper = buildQueryWrapper(addCalibrationUserDTO);
        Page pageObj = baseMapper.selectPage(addCalibrationUserDTO, wrapper);

        return pageObj;
    }

    @Override
    public IPage<HrsfPmsuserBase> getOverviewPage(AddCalibrationUserDTO addCalibrationUserDTO) {
        return getPmsUserListByOverView(addCalibrationUserDTO);
    }

    private long getLong(LocalDate localDate) {
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.systemDefault());
        Date date = Date.from(zonedDateTime.toInstant());
        log.info("localDate:{}", date.getTime());
        //时间+1天
        return date.getTime() + 24 * 60 * 60 * 1000;
    }

    @Override
    public boolean finalWriteDataBack(List<WriteDataBackDTO> writeDataBackDTOS) {
        Integer flag = 4;
        if (!CollectionUtils.isEmpty(writeDataBackDTOS)) {
            for (WriteDataBackDTO writeDataBackDTO : writeDataBackDTOS) {
                int step = writeDataBackDTO.getStep();
                log.info("======非白领年度/HRL开始推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
                //所有
                //将绩效考核结果回写到SF的员工档案中
                if (step < 1) {
                    try {
                        HashMap<String, Object> paramMap = new HashMap<>();
                        HashMap<String, Object> subParamMap = new HashMap<>();
                        paramMap.put("__metadata", subParamMap);
                        subParamMap.put("uri", StrUtil.format(UPDATE_PMS_CUST_PERFORMANCE_RATING, writeDataBackDTO.getEndDate(), writeDataBackDTO.getStaffId()));
                        paramMap.put("cust_year", writeDataBackDTO.getAssessYear());
                        paramMap.put("cust_type", writeDataBackDTO.getAssessType());
                        paramMap.put("cust_rating", writeDataBackDTO.getTotalScore().toString());
                        paramMap.put("cust_perank", MappingEnum.getTypeByDesc(writeDataBackDTO.getPmsLevel().toString()));
                        paramMap.put("cust_porank", null);
                        paramMap.put("cust_trend", null);
                        paramMap.put("cust_status", "A");
                        String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap, JSONConfig.create().setIgnoreNullValue(false))).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.error("macros|7.非白领年度/HRL3将绩效考核结果回写到SF的员工档案中,出参和入参:{}", JSONUtil.toJsonStr(writeDataBackDTO));
                        JSONObject jsonObject2 = JSONUtil.parseObj(body);
                        JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                        String status = returnJson.getStr("status");
                        if (!"OK".equals(status)) {
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    } catch (Exception e) {
                        log.error("7.非白领年度/HRL3将绩效考核结果回写到SF的员工档案中,抛出异常:{}", e);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }
                }

                //判断若员工的分数来源字段=评分表，调取一次接口，将表单发送至下一步骤
                String formDataId = writeDataBackDTO.getFormId();
                if (Objects.equals(writeDataBackDTO.getScoreSource(), ScoreSourceEnum.SF.getType())) {
                    try {
                        String format = StrUtil.format(sfSyncSendToNextStep, writeDataBackDTO.getFormId());
                        log.error("1.非白领年度/HRL3将表单发送至下一步骤的url:{}", format);
                        String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                        writeDataBackDTO.setResponseBody(body);
                        log.error("1.非白领年度/HRL3发送到下一步骤,入参和出参值:{}", JSONUtil.toJsonStr(writeDataBackDTO));
                        JSONObject jsonObject = JSONUtil.parseObj(body);
                        JSONObject returnJson = jsonObject.getJSONObject("d").getJSONObject("CORouteFormStatusBean");
                        String status = returnJson.getStr("status");
                        if (!"Success".equals(status)) {
                            writeDataBackDTO.setStep(1);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    } catch (HttpException err) {
                        //这里发生超时，先不做处理继续执行
                        log.error("1.非白领年度/HRL3发送到下一步骤,超时抛出异常:{}", err);

                    } catch (Exception e) {
                        writeDataBackDTO.setStep(1);
                        log.error("1.非白领年度/HRL3发送到下一步骤,抛出异常:{}", e);
                        saveErrorLog2(writeDataBackDTO, flag);
                        continue;
                    }

                } else if (Objects.equals(writeDataBackDTO.getScoreSource(), ScoreSourceEnum.IMPORT.getType())) {
                    //判断若分数来源=批量导入，调取接口将小指标分、最终总分、业绩等级回写进评分表中；
                    // 同时调两次接口将表单发送到下一步骤；
                    String userNameNew;
                    if (step < 3) {
                        try {
                            String format = StrUtil.format(sfSyncTodoEntryV2, formDataId);
                            log.error("1.非白领年度/HRL3未获取表单当前负责人的url:{}", format);
                            String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                            writeDataBackDTO.setResponseBody(body);
                            log.error("1.非白领年度/HRL3未获取表单当前负责人,出参和入参:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                            JSONObject jsonObject = JSONUtil.parseObj(body);
                            Optional<Object> optional = jsonObject.getJSONObject("d").getJSONArray("results").stream().
                                    filter(obj -> (StrUtil.isNotBlank(((JSONObject) obj).getStr("userId")))).findFirst();
                            if (optional.isPresent()) {
                                JSONObject userJson = (JSONObject) optional.get();
                                userNameNew = userJson.getStr("userId");
                                userNameNew = userNameNew + suffixName;
                            } else {
                                writeDataBackDTO.setStep(1);
                                saveErrorLog2(writeDataBackDTO, flag);
                                log.error("1.非白领年度/HRL3未获取表单当前负责人,员工ID为:{}", writeDataBackDTO.getStaffId());
                                continue;
                            }
                        } catch (Exception e) {
                            log.error("1.非白领年度/HRL3未获取表单当前负责人,员工ID为:{},抛异常原因:{}", writeDataBackDTO.getStaffId(), e);
                            writeDataBackDTO.setStep(1);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                        try {
                            /**
                             * 此处调整为token的方式
                             * 不是用用户名密码的方式
                             *
                             */
                            /**
                             * String assertion = new HttpRequestCustom(idpUrl).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(StrUtil.format(idpBody, userNameNew)).execute().body();
                             String accessTokenStr = new HttpRequestCustom(tokenUrl).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(StrUtil.format(tokenBody, userNameNew, assertion)).execute().body();
                             JSONObject accessTokenJson = JSONUtil.parseObj(accessTokenStr);
                             String access_token = accessTokenJson.getStr("access_token");
                             if (StrUtil.isBlank(access_token)) {
                             log.info("获取返回的accessTokenStr为空:{}", accessTokenStr);
                             saveErrorLog2(writeDataBackDTO, flag);
                             continue;
                             }*/
                            String format = StrUtil.format(sfSyncSendToNextStep, writeDataBackDTO.getFormId());
                            log.error("2.非白领年度/HRL3将表单发送至下一步骤的url:{}", format);
                            String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userNameNew, sfSyncSendToNextStepKey).execute().body();
                            writeDataBackDTO.setResponseBody(body);
                            log.error("2.非白领年度/HRL3将表单发送至下一步骤:出参和入参{}", JSONUtil.toJsonStr(writeDataBackDTO));
                            JSONObject jsonObject = JSONUtil.parseObj(body);
                            JSONObject returnJson = jsonObject.getJSONObject("d").getJSONObject("CORouteFormStatusBean");
                            String status = returnJson.getStr("status");
                            if (!"Success".equals(status)) {
                                writeDataBackDTO.setStep(2);
                                saveErrorLog2(writeDataBackDTO, flag);
                                continue;
                            }
                        } catch (HttpException err) {
                            //这里发生超时，先不做处理继续执行
                            log.error("2.非白领年度/HRL3将表单发送至下一步骤的url,超时抛异常原因:{}", err);

                        } catch (Exception e) {
                            log.error("2.非白领年度/HRL3将表单发送至下一步骤的url,抛异常原因:{}", e);
                            writeDataBackDTO.setStep(2);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    }
                    if (step < 6) {
                        //步骤三：调接口获取formcontentID
                        String formcontentID;
                        try {
                            String format = StrUtil.format(sfSyncFormcontentId, formDataId);
                            String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                            writeDataBackDTO.setResponseBody(body);
                            log.error("3.非白领年度/HRL3调接口获取formcontentID:出参和入参{}", JSONUtil.toJsonStr(writeDataBackDTO));

                            JSONObject jsonObject = JSONUtil.parseObj(body);
                            log.error("formDataId:{},body:{}", formDataId, body);
                            formcontentID = jsonObject.getJSONObject("d").getStr("formContentId");
                        } catch (Exception e) {
                            log.error("3.非白领年度/HRL3调接口获取formcontentID的url,抛异常原因:{}", e);
                            writeDataBackDTO.setStep(3);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }

                        //步骤四
                        HashMap<String, Object> paramMap = new HashMap<>();

                        if (Objects.equals(AssessTypeEnum.YEAR.getType(), writeDataBackDTO.getAssessType())) {
                            HashMap<String, Object> subParamMap = new HashMap<>();
                            List<HashMap<String, Object>> competenciesMapList = new ArrayList<>();
                            subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_COMPETENCY_SECTION, formcontentID, formDataId, 2));
                            subParamMap.put("type", "SFOData.FormCompetencySection");
                            paramMap.put("__metadata", subParamMap);
                            paramMap.put("competencies", competenciesMapList);

                            /**
                             * 改到配置文件中
                             * 1487 -- 735   wf_sect_2__c735_r
                             * 1493 -- 739   wf_sect_2__c739_r
                             * 1484 -- 733   wf_sect_2__c733_r
                             * 1481 -- 731   wf_sect_2__c731_r
                             * 1490 -- 737   wf_sect_2__c737_r
                             * 1496 -- 741   wf_sect_2__c741_r
                             * 1497 -- 743   wf_sect_2__c743_r
                             * 1498 -- 745   wf_sect_2__c745_r
                             * 1458 -- 727   wf_sect_2__c727_r
                             * 1457 -- 725   wf_sect_2__c725_r
                             */
                            getCompetenciesHashMap(formcontentID, formDataId, itemId1, 2, "na", itemIdKey1, writeDataBackDTO.getTarget1Score() == null ? null : writeDataBackDTO.getTarget1Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId2, 2, "na", itemIdKey2, writeDataBackDTO.getTarget2Score() == null ? null : writeDataBackDTO.getTarget2Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId3, 2, "na", itemIdKey3, writeDataBackDTO.getTarget3Score() == null ? null : writeDataBackDTO.getTarget3Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId4, 2, "na", itemIdKey4, writeDataBackDTO.getTarget4Score() == null ? null : writeDataBackDTO.getTarget4Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId5, 2, "na", itemIdKey5, writeDataBackDTO.getTarget5Score() == null ? null : writeDataBackDTO.getTarget5Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId6, 2, "na", itemIdKey6, writeDataBackDTO.getTarget6Score() == null ? null : writeDataBackDTO.getTarget6Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId7, 2, "na", itemIdKey7, writeDataBackDTO.getTarget7Score() == null ? null : writeDataBackDTO.getTarget7Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId8, 2, "na", itemIdKey8, writeDataBackDTO.getTarget8Score() == null ? null : writeDataBackDTO.getTarget8Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId9, 2, "na", itemIdKey9, writeDataBackDTO.getTarget9Score() == null ? null : writeDataBackDTO.getTarget9Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId10, 2, "na", itemIdKey10, writeDataBackDTO.getTarget10Score() == null ? null : writeDataBackDTO.getTarget10Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);

                        } else {
                            //若考核类型=Q1/Q2/Q3
                            HashMap<String, Object> subParamMap = new HashMap<>();
                            List<HashMap<String, Object>> competenciesMapList = new ArrayList<>();
                            subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_COMPETENCY_SECTION, formcontentID, formDataId, 2));
                            subParamMap.put("type", "SFOData.FormCompetencySection");
                            paramMap.put("__metadata", subParamMap);
                            paramMap.put("competencies", competenciesMapList);

                            /**
                             * 改到配置文件中
                             * 1411 -- 705   wf_sect_2__c705_r
                             * 1414 -- 707   wf_sect_2__c707_r
                             * 1417 -- 709   wf_sect_2__c709_r
                             * 1420 -- 711   wf_sect_2__c711_r
                             * 1423 -- 713   wf_sect_2__c713_r
                             */
                            getCompetenciesHashMap(formcontentID, formDataId, itemId11, 2, "na", itemIdKey11, writeDataBackDTO.getTarget1Score() == null ? null : writeDataBackDTO.getTarget1Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId12, 2, "na", itemIdKey12, writeDataBackDTO.getTarget2Score() == null ? null : writeDataBackDTO.getTarget2Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId13, 2, "na", itemIdKey13, writeDataBackDTO.getTarget3Score() == null ? null : writeDataBackDTO.getTarget3Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId14, 2, "na", itemIdKey14, writeDataBackDTO.getTarget4Score() == null ? null : writeDataBackDTO.getTarget4Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                            getCompetenciesHashMap(formcontentID, formDataId, itemId15, 2, "na", itemIdKey15, writeDataBackDTO.getTarget5Score() == null ? null : writeDataBackDTO.getTarget5Score().multiply(BigDecimal.valueOf(100)), competenciesMapList);
                        }
                        try {
                            String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
                            writeDataBackDTO.setResponseBody(body);
                            log.error("4.非白领年度/HRL3调接口写入competency rating（小指标分）:出参和入参{}", JSONUtil.toJsonStr(writeDataBackDTO));
                            JSONObject jsonObject2 = JSONUtil.parseObj(body);
                            JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                            String status = returnJson.getStr("status");
                            if (!"OK".equals(status)) {
                                writeDataBackDTO.setStep(4);
                                saveErrorLog2(writeDataBackDTO, flag);
                                continue;
                            }
                        } catch (Exception e) {
                            log.error("4.非白领年度/HRL3回写数据到SF系统,抛异常原因:{}", e);
                            writeDataBackDTO.setStep(4);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                        //步骤4结束
                        //步骤五：写入业绩等级
                        try {
                            HashMap<String, Object> paramMap2 = new HashMap<>();
                            HashMap<String, Object> subParamMap = new HashMap<>();
                            paramMap2.put("__metadata", subParamMap);
                            subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT, formcontentID, formDataId, 4, 0, "overall"));
                            subParamMap.put("type", "SFOData.FormUserRatingComment");
                            paramMap2.put("ratingKey", "wf_sect_4_rating");
                            paramMap2.put("rating", writeDataBackDTO.getPmsLevel().toString());
                            String body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap2)).basicAuth(userName, pwd).execute().body();
                            writeDataBackDTO.setResponseBody(body);
                            log.error("5.非白领年度/HRL3写入业绩等级,出参和入参:{}", JSONUtil.toJsonStr(writeDataBackDTO));

                            JSONObject jsonObject2 = JSONUtil.parseObj(body);
                            JSONObject returnJson = (JSONObject) jsonObject2.getJSONArray("d").get(0);
                            String status = returnJson.getStr("status");
                            if (!"OK".equals(status)) {
                                writeDataBackDTO.setStep(5);
                                saveErrorLog2(writeDataBackDTO, flag);
                                continue;
                            }
                        } catch (Exception e) {
                            log.error("5.非白领年度/HRL3回写数据到SF,抛出异常:{}", e);
                            writeDataBackDTO.setStep(5);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    }
                    if (step < 7) {
                        //步骤六：将表单发送至已完成（同步骤二）
                        try {
                            String format = StrUtil.format(sfSyncSendToNextStep, writeDataBackDTO.getFormId());
                            log.info("6.非白领年度/HRL3将表单发送至已完成的url:{}", format);
                            String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                            writeDataBackDTO.setResponseBody(body);
                            log.error("6.非白领年度/HRL3将表单发送至已完成,出参和入参:{}", JSONUtil.toJsonStr(writeDataBackDTO));
                            JSONObject jsonObject = JSONUtil.parseObj(body);
                            JSONObject returnJson = jsonObject.getJSONObject("d").getJSONObject("CORouteFormStatusBean");
                            String status = returnJson.getStr("status");
                            if (!"Success".equals(status)) {
                                writeDataBackDTO.setStep(6);
                                saveErrorLog2(writeDataBackDTO, flag);
                                continue;
                            }
                        } catch (HttpException err) {
                            //这里发生超时，先不做处理继续执行
                            log.error("6.非白领年度/HRL3将表单发送至已完成,超时抛出异常:{}", err);

                        } catch (Exception e) {
                            log.error("6.非白领年度/HRL3将表单发送至已完成,抛出异常:{}", e);
                            writeDataBackDTO.setStep(6);
                            saveErrorLog2(writeDataBackDTO, flag);
                            continue;
                        }
                    }
                }


                if (writeDataBackDTO.getId() != null) {
                    writeDataBackDTO.setEmailStatus(EmailStatusEnum.SUCCESS.getType());
                    saveErrorLog2(writeDataBackDTO, flag);
                }
                log.info("======非白领年度/HRL结束推送数据,员工号:{}===========", writeDataBackDTO.getStaffId());
            }

        }
        return false;
    }

    private void saveErrorLog2(WriteDataBackDTO writeDataBackDTO, Integer flag) {
        HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
        BeanUtils.copyProperties(writeDataBackDTO, hrsfPmsWriteDataBackLog);
        if (StringUtils.isBlank(hrsfPmsWriteDataBackLog.getEmailStatus())) {
            hrsfPmsWriteDataBackLog.setEmailStatus(EmailStatusEnum.ZERO.getType());
        }
        hrsfPmsWriteDataBackLog.setFlag(flag.toString());
        /**
         * 这里做一个优化
         * 将writeDataBackDTO对象中的ResponseBody置空
         * 无需将该值存入到表中
         *
         */
        writeDataBackDTO.setResponseBody(null);
        hrsfPmsWriteDataBackLog.setErrorInfo(JSONUtil.toJsonStr(writeDataBackDTO));
        writeDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
    }

    @Override
    public List<HrsfPmsuserBase> selectByBaseId(Long baseId) {
        if (null == baseId) {
            return new ArrayList<>();
        }
        return list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId, baseId)
                .orderByDesc(HrsfPmsuserBase::getPmsLevel, HrsfPmsuserBase::getTotalScore));
    }

    /**
     * 更新绩效员工考核表信息
     *
     * @param dataMap
     * @param calibrationBaseId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateHrsfPmsUserInfo(Map<String, List<HrsfPmsuserBase>> dataMap, Long calibrationBaseId) {
        /**
         * 循环更新每个组别的考核员工基础表
         */
        List<HrsfPmsuserBase> dataList = new ArrayList<>();

        /**
         * 首先校验一下考核员工基础表状态
         */
        if (calibrationBaseId == null) {
            throw new CheckedException("calibrationBaseId为空,请稍后再试!");
        }
        R<HrsfCalibrationBaseDTO> calibrationBaseR = remotePmsService.getCalibrationInner(calibrationBaseId, SecurityConstants.FROM_IN);
        if (calibrationBaseR.getCode() == 0) {
            HrsfCalibrationBaseDTO calibrationBase = calibrationBaseR.getData();
            /**
             * 非公司级校准会议，节点必须是负责人
             */
            if (calibrationBase != null && ProcessStepEnum.OWNER.getType().equals(calibrationBase.getProcessStep())) {

                /**
                 * 提交之前的数据
                 * 先查询一下所有的员工组装成一个Map
                 * ID和总分
                 */
                Map<Long, BigDecimal> idTotalScoreMap = list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId, calibrationBaseId))
                        .stream().collect(Collectors.toMap(HrsfPmsuserBase::getId, HrsfPmsuserBase::getTotalScoreOriginal));

                dataMap.values().stream().filter(data -> CollectionUtil.isNotEmpty(data)).forEach(data ->
                        dataList.addAll(data.stream().map(pmsUserBase -> {
                                    /**
                                     * 判断传入的分数和历史分数
                                     * 如果不一致则设置changeFlag为true
                                     */
                                    pmsUserBase.setChangeFlag(pmsUserBase.getTotalScore()
                                            .compareTo(idTotalScoreMap.get(pmsUserBase.getId())) != 0 ?
                                            CommonConstants.FLAG_Y : CommonConstants.FLAG_NO);
                                    return pmsUserBase;
                                }
                        ).collect(Collectors.toList())));
            } else if (calibrationBase != null && CalibrationTemplateEnum.THREE.getType().equals(calibrationBase.getTemplate())
                    && ProcessStepEnum.HR_MANAGER.getType().equals(calibrationBase.getProcessStep())) {
                /**
                 * * 公司级校准会议，节点必须是HR管理员
                 */
                dataMap.values().stream().filter(data -> CollectionUtil.isNotEmpty(data)).forEach(data -> dataList.addAll(data));
            } else {
                throw new CheckedException("未找到该校准会议或者校准会议的流程未在机构负责人节点,请检查重试!");
            }
            /**
             * 更新员工数据
             */
            if (CollectionUtil.isNotEmpty(dataList)) {
                updateBatchById(dataList);
            }
        }
        return true;
    }


    private HashMap<String, Object> getCompetenciesHashMap(String formcontentID,
                                                           String formDataId,
                                                           int itemId,
                                                           int sectionIndex,
                                                           String ratingType,
                                                           String ratingKey,
                                                           BigDecimal rating, List<HashMap<String, Object>> competenciesMapList) {
        HashMap<String, Object> paramMap = new HashMap<>();
        HashMap<String, Object> paramMap2 = new HashMap<>();
        HashMap<String, Object> subParamMap = new HashMap<>();
        HashMap<String, Object> subParamMap2 = new HashMap<>();
        subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORM_COMPETENCY, formcontentID, formDataId, itemId, sectionIndex));
        subParamMap.put("type", "SFOData.FormCompetency");

        subParamMap2.put("uri", StrUtil.format(UPDATE_PMS_FORM_USER_RATING_COMMENT_2, formcontentID, formDataId, itemId, ratingType, sectionIndex));
        subParamMap2.put("type", "SFOData.FormUserRatingComment");
        paramMap2.put("__metadata", subParamMap2);
        paramMap2.put("ratingKey", ratingKey);
        paramMap2.put("rating", rating.toString());

        paramMap.put("__metadata", subParamMap);
        paramMap.put("officialRating", paramMap2);
        competenciesMapList.add(paramMap);
        return paramMap;

    }

    private void sendToNextStep(WriteDataBackDTO writeDataBackDTO, String userName, String pwd) {
        String format = StrUtil.format(sfSyncSendToNextStep, writeDataBackDTO.getFormId());
        log.info("获取获取当前步骤负责人的url:{}", format);
        String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(body);
        JSONObject returnJson = (JSONObject) jsonObject.getJSONArray("d").get(0);
        String status = returnJson.getStr("status");
        if (!"OK".equals(status)) {
            log.error("发送到下一步骤失败:{},入参值:{}", body, JSONUtil.toJsonStr(writeDataBackDTO));
        } else {
            log.info("发送到下一步骤成功,入参值:{}", JSONUtil.toJsonStr(writeDataBackDTO));
        }
    }


    /**
     * 根据当前流程状态返回对应的表单类型
     *
     * @param processStep
     */
    private String handleFormStatusByProcessStep(String processStep) {
        switch (getByCode(processStep)) {
            case HRBP:
                return FormStatusEnum.Rated.getType();
            case OWNER:
                return FormStatusEnum.In_Calibration.getType();
            case HR_MANAGER:
            case HRL4:
            case HRL3:
                return FormStatusEnum.In_HR_Approval.getType();
            case END:
                return FormStatusEnum.Completed.getType();
            default:
                throw new IllegalStateException("Unexpected value: " + processStep);
        }
    }

    /**
     * 查询条件(排除业绩等级为空的数据)
     *
     * @param queryDTO
     * @return
     */
    private Wrapper<HrsfPmsuserBase> buildQueryWrapperCalibrationPms(CalibrationListQueryDTO queryDTO) {
        LambdaQueryWrapper<HrsfPmsuserBase> wrapper = Wrappers.lambdaQuery();
        if (CollectionUtil.isNotEmpty(queryDTO.getSystem())) {
            wrapper.in(HrsfPmsuserBase::getSystem, queryDTO.getSystem());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getDepartment())) {
            wrapper.in(HrsfPmsuserBase::getDepartment, queryDTO.getDepartment());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getSection())) {
            wrapper.in(HrsfPmsuserBase::getSection, queryDTO.getSection());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getUserGroup())) {
            wrapper.in(HrsfPmsuserBase::getUserGroup, queryDTO.getUserGroup());
        }
        if (queryDTO.getCalibrationBaseId() != null) {
            wrapper.eq(HrsfPmsuserBase::getCalibrationBaseId, queryDTO.getCalibrationBaseId());
        }
        if (StrUtil.isNotBlank(queryDTO.getFormStatus())) {
            wrapper.eq(HrsfPmsuserBase::getFormStatus, queryDTO.getFormStatus());
        }

        if (StrUtil.isNotBlank(queryDTO.getEmploymentType())) {
            /**
             * 如果传过来是外方,则直接将EmploymentType等于戴姆勒集团调入
             * 否则 不等于戴姆勒集团调入
             */
            if (ChineseForeignTypeEnum.Foreign.getType().equals(queryDTO.getEmploymentType())) {
                wrapper.eq(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            } else {
                wrapper.ne(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getStaffIdList())) {
            wrapper.in(HrsfPmsuserBase::getStaffId, queryDTO.getStaffIdList());
        }

        wrapper.isNotNull(HrsfPmsuserBase::getPmsLevel);
        return wrapper;
    }

    /**
     * 构建查询收件箱列表视图Wrapper
     *
     * @param queryDTO
     * @return
     */
    private LambdaQueryWrapper buildQueryWrapperCalibration(CalibrationListQueryDTO queryDTO) {
        LambdaQueryWrapper<HrsfPmsuserBase> wrapper = Wrappers.lambdaQuery();
        if (CollectionUtil.isNotEmpty(queryDTO.getSystem())) {
            wrapper.in(HrsfPmsuserBase::getSystem, queryDTO.getSystem());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getDepartment())) {
            wrapper.in(HrsfPmsuserBase::getDepartment, queryDTO.getDepartment());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getSection())) {
            wrapper.in(HrsfPmsuserBase::getSection, queryDTO.getSection());
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getUserGroup())) {
            wrapper.in(HrsfPmsuserBase::getUserGroup, queryDTO.getUserGroup());
        }
        if (queryDTO.getCalibrationBaseId() != null) {
            wrapper.eq(HrsfPmsuserBase::getCalibrationBaseId, queryDTO.getCalibrationBaseId());
        }
        if (StrUtil.isNotBlank(queryDTO.getFormStatus())) {
            wrapper.eq(HrsfPmsuserBase::getFormStatus, queryDTO.getFormStatus());
        }
        if (queryDTO.getAdjustedFlag() != null && queryDTO.getAdjustedFlag()) {
            wrapper.and(wp -> wp.eq(HrsfPmsuserBase::getAdjusted, "1").or().eq(HrsfPmsuserBase::getPAdjusted, "1"));
        }

        if (StrUtil.isNotBlank(queryDTO.getEmploymentType())) {
            /**
             * 如果传过来是外方,则直接将EmploymentType等于戴姆勒集团调入
             * 否则 不等于戴姆勒集团调入
             */
            if (ChineseForeignTypeEnum.Foreign.getType().equals(queryDTO.getEmploymentType())) {
                wrapper.eq(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            } else {
                wrapper.ne(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            }
        }
        if (CollectionUtil.isNotEmpty(queryDTO.getStaffIdList())) {
            wrapper.in(HrsfPmsuserBase::getStaffId, queryDTO.getStaffIdList());
        }

        wrapper.orderByDesc(HrsfPmsuserBase::getPmsLevel).orderByAsc(HrsfPmsuserBase::getPotentialLevel)
                .orderByDesc(HrsfPmsuserBase::getTotalScore).orderByDesc(HrsfPmsuserBase::getDepartment);
        return wrapper;
    }


    private LambdaQueryWrapper buildQueryWrapper(List<String> staffIdList) {
        LambdaQueryWrapper<HrsfUserBase> wrapper = Wrappers.lambdaQuery();
        if (CollUtil.isNotEmpty(staffIdList)) {
            wrapper.in(HrsfUserBase::getStaffId, staffIdList);
        } else {
            wrapper.and(wp -> wp.eq(HrsfUserBase::getStatus, UserStateEnum.T.getType()).in(HrsfUserBase::getEmploymentType,
                    Arrays.asList(EmployeeTypeEnum.A.getType(), EmployeeTypeEnum.C.getType()
                            , EmployeeTypeEnum.B.getType(), EmployeeTypeEnum.E.getType(), EmployeeTypeEnum.F.getType(), EmployeeTypeEnum.K.getType()
                    )))
                    .or(wp -> wp.eq(HrsfUserBase::getStatus, UserStateEnum.F.getType()).in(HrsfUserBase::getSpecialResignedEmployee,
                            Arrays.asList(SpecialLeaverEnum.B.getType(), SpecialLeaverEnum.C.getType(), SpecialLeaverEnum.A.getType(), SpecialLeaverEnum.D.getType())))
                    .or(wp -> wp.eq(HrsfUserBase::getStatus, UserStateEnum.T.getType()).eq(HrsfUserBase::getEmploymentType, EmployeeTypeEnum.J.getType())
                            .in(HrsfUserBase::getUserLevel, Arrays.asList(UserLevelEnum.L3.name(), UserLevelEnum.L4.name())));

        }

        return wrapper;
    }

    //行专列
    public static <T> List<List<T>> rowToCol(List<List<T>> list) {
        List<List<T>> toList = new ArrayList<List<T>>();
        Map<Integer, List<T>> map = new HashMap<Integer, List<T>>();
        for (int i = 0; i < list.size(); i++) {
            List<T> row = list.get(i);
            for (int j = 0; j < row.size(); j++) {
                T cell = row.get(j);
                List<T> col = map.get(j);
                if (col == null) {
                    col = new ArrayList<T>();
                }
                col.add(cell);
                map.put(j, col);
            }
        }
        List<Integer> sort = new ArrayList<Integer>(map.keySet());
        Collections.sort(sort);
        for (Integer key : sort) {
            toList.add(map.get(key));
        }
        return toList;
    }

    private Wrapper<HrsfPmsuserBase> buildQueryWrapper(AddCalibrationUserDTO addCalibrationUserDTO) {
        LambdaQueryWrapper<HrsfPmsuserBase> wrapper = Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getAssessYear, addCalibrationUserDTO.getAssesYear())
                .eq(HrsfPmsuserBase::getAssessType, addCalibrationUserDTO.getAssesType())
                .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType());

        if (StrUtil.isNotBlank(addCalibrationUserDTO.getSystem())) {
            if (CommonConstants.DEFAULT_NA.equals(addCalibrationUserDTO.getSystem())) {
                wrapper.isNull(HrsfPmsuserBase::getSystem);
            } else {
                wrapper.eq(HrsfPmsuserBase::getSystem, addCalibrationUserDTO.getSystem());
            }
        }
        if (StrUtil.isNotBlank(addCalibrationUserDTO.getDepartment())) {
            if (CommonConstants.DEFAULT_NA.equals(addCalibrationUserDTO.getDepartment())) {
                wrapper.isNull(HrsfPmsuserBase::getDepartment);
            } else {
                wrapper.eq(HrsfPmsuserBase::getDepartment, addCalibrationUserDTO.getDepartment());
            }
        }
        if (StrUtil.isNotBlank(addCalibrationUserDTO.getSection())) {
            if (CommonConstants.DEFAULT_NA.equals(addCalibrationUserDTO.getSection())) {
                wrapper.isNull(HrsfPmsuserBase::getSection);
            } else {
                wrapper.eq(HrsfPmsuserBase::getSection, addCalibrationUserDTO.getSection());
            }
        }
        if (StrUtil.isNotBlank(addCalibrationUserDTO.getUserGroup())) {
            if (CommonConstants.DEFAULT_NA.equals(addCalibrationUserDTO.getUserGroup())) {
                wrapper.isNull(HrsfPmsuserBase::getUserGroup);
            } else {
                wrapper.eq(HrsfPmsuserBase::getUserGroup, addCalibrationUserDTO.getUserGroup());
            }
        }
        if (StrUtil.isNotBlank(addCalibrationUserDTO.getUserLevel())) {
            wrapper.eq(HrsfPmsuserBase::getUserLevel, addCalibrationUserDTO.getUserLevel());
        }
        if (StrUtil.isNotBlank(addCalibrationUserDTO.getFormStatus())) {
            wrapper.eq(HrsfPmsuserBase::getFormStatus, addCalibrationUserDTO.getFormStatus());
        }
        if (StringUtils.isNotBlank(addCalibrationUserDTO.getEmploymentType())) {
            /**
             * 如果传过来是外方,则直接将EmploymentType等于戴姆勒集团调入
             * 否则 不等于戴姆勒集团调入
             */
            if (ChineseForeignTypeEnum.Foreign.getType().equals(addCalibrationUserDTO.getEmploymentType())) {
                wrapper.eq(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            } else {
                wrapper.ne(HrsfPmsuserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            }
        }
        if (CollUtil.isNotEmpty(addCalibrationUserDTO.getStaffIds())) {
            wrapper.in(HrsfPmsuserBase::getStaffId, addCalibrationUserDTO.getStaffIds());
        }


        wrapper.orderByDesc(HrsfPmsuserBase::getPmsLevel).orderByAsc(HrsfPmsuserBase::getPotentialLevel)
                .orderByDesc(HrsfPmsuserBase::getTotalScore).orderByDesc(HrsfPmsuserBase::getDepartment).orderByAsc(HrsfPmsuserBase::getId);
        /**
         * orderByDesc(HrsfPmsuserBase::getDepartment)
         * 此处注释,到时分页有问题
         */;
        return wrapper;
    }

    /**
     * 转换pmsLevelDesc显示文本
     * @param list 需要转换的列表
     */
    private void convertPmsLevelDesc(List<HrsfPmsuserBase> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(pms -> {
            String pmsLevelDesc = pms.getPmsLevelDesc();
            if (pmsLevelDesc != null && Integer.parseInt(pms.getAssessYear()) > 2024) {
                if ("完全达标 Successful".equals(pmsLevelDesc)) {
                    pms.setPmsLevelDesc("良好 Successful");
                } else if ("不完全达标 Inconsistent".equals(pmsLevelDesc)) {
                    pms.setPmsLevelDesc("及格 Sufficient");
                } else if ("完全不达标 Insufficient".equals(pmsLevelDesc)) {
                    pms.setPmsLevelDesc("需改进 Improvement Required");
                }
            }
        });
    }
}
