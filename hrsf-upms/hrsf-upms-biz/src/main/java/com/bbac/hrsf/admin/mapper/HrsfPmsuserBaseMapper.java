package com.bbac.hrsf.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Mapper
public interface HrsfPmsuserBaseMapper extends BaseMapper<HrsfPmsuserBase> {

    @Update("UPDATE HRSF_PMSUSER_BASE SET CALIBRATION_BASE_ID = NULL where id=#{id}")
    void updateCalibrationById(@Param("id") Long id);

    @Delete("delete from HRSF_PMSUSER_BASE where id=#{id}")
    void deleteUserById(@Param("id") Long id);

    @Delete("delete from HRSF_PMSUSER_BASE where ASSESS_YEAR=#{assessYear} and ASSESS_TYPE=#{assessType} and STAFF_ID=#{staffId} ")
    void deleteByYearAndTypeAndStaffId(@Param("assessYear") String assessYear, @Param("assessType") String assessType, @Param("staffId") String staffId);

    @Update("UPDATE HRSF_PMSUSER_BASE SET TOTAL_SCORE_ORIGINAL=TOTAL_SCORE WHERE CALIBRATION_BASE_ID =#{baseId}")
    void updateTotalScoreOriginal(@Param("baseId") Long baseId);

    @Select("select STAFF_ID,STATUS,FULL_NAME,SYSTEM,DEPARTMENT,SECTION,USER_GROUP,POSITION,JOB_TITLE,USER_LEVEL from HRSF_PMSUSER_BASE  where ASSESS_YEAR = #{assesYear} AND ASSESS_TYPE = #{assesType} and LEVEL_FLAG='0' and (regexp_like(FULL_NAME ,#{fullName},'i') or regexp_like(STAFF_ID ,#{fullName},'i')) AND ROWNUM<=100")
    List<HrsfPmsuserBase> selectByFullNameOrStaffId(@Param("assesYear") String assesYear, @Param("assesType") String assesType, @Param("fullName") String fullName);

    @Select("select STAFF_ID,STATUS,FULL_NAME,SYSTEM,DEPARTMENT,SECTION,USER_GROUP,POSITION,JOB_TITLE,USER_LEVEL from HRSF_PMSUSER_BASE  where CALIBRATION_BASE_ID = #{id}  and (regexp_like(FULL_NAME ,#{fullName},'i') or regexp_like(STAFF_ID ,#{fullName},'i')) AND ROWNUM<=100")
    List<HrsfPmsuserBase> selectByBaseIdOrStaffId(@Param("id")Long id, @Param("fullName")String fullName);

    void updatePmsLevelById(@Param("cachedDataListTwo") List<Long> cachedDataListTwo);

    void updateFormIdBatchById(@Param("pmsUserBaseList")List<HrsfPmsuserBase> pmsUserBaseList);
}
