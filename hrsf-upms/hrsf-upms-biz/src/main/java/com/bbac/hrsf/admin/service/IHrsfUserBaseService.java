package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseFilterDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserBaseFilterDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserFilterDTO;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserSimpleVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
public interface IHrsfUserBaseService extends IService<HrsfUserBase> {

    Boolean sync(long current, long size, String i, Long id);

    Boolean syncPhoto(long current, long size);

    IPage<HrsfUserVO> getUserPageInfo(Page page, HrsfUserBaseFilterDTO hrsfUserDTO);

    List<HrsfUserVO> getUserInfoList(HrsfUserFilterDTO hrsfUserDTO);

    List<HrsfUserVO> getUserBaseSelectName(String fullName);

    List<HrsfUserSimpleVO> getAllSimpleInfo();

    List<HrsfUserVO> getUserInfoByStaffId(List<String> staffIdList);

    Set<String> selectByRoleCode(Long roleCode);

    Set<String> selectByStaffIdList(Set<String> staffIdList);

    List<HrsfUserBase> selectEmailAddressByStaffIdList(Set<String> staffIdList);

    void syncAgain();

    IPage<HrsfUserVO> getUserPage(HrsfPmsUserBaseFilterDTO hrsfPmsUserBaseDTO);

    HrsfOrganizationVO getCalibrationPmsSelect();

    void syncFormIdAgain();

    List<HrsfUserVO> getUserBaseSelectByStaffId(String staffId);
}
