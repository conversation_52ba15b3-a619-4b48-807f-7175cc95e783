/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * <p>
 * 用户角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Mapper
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {

    /**
     * 根据用户Id删除该用户的角色关系
     *
     * @param userId 用户ID
     * @return boolean
     * <AUTHOR>
     * @date 2017年12月7日 16:31:38
     */
    Boolean deleteByUserId(@Param("userId") String userId);

    /**
     * 根据用户Id删除该用户的角色关系
     *
     * @param roleId 用户ID
     * @return boolean
     * <AUTHOR>
     * @date 2017年12月7日 16:31:38
     */
    Boolean deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色CODE获取用户
     *
     * @return boolean
     * <AUTHOR>
     * @date 2017年12月7日 16:31:38
     */
    Set<String> selectByRoleCode(@Param("roleCode") Long roleCode);
}
