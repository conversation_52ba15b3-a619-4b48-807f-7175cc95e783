package com.bbac.hrsf.admin.service;

import com.bbac.hrsf.admin.api.vo.PerformanceRatingVO;

import java.util.List;
import java.util.Map;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.service.EnterpriseWeChatService</li>
 * <li>CreateTime : 2022/08/10 11:11:09</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface EnterpriseWeChatService {
    /**
     *
     * @param userId
     * @return
     */
    List<PerformanceRatingVO> performanceRating(String userId);

    Map getUserIdByCode(String code, String state);
}