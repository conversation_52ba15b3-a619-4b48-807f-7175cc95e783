package com.bbac.hrsf.admin.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.*;
import com.bbac.hrsf.admin.api.entity.*;
import com.bbac.hrsf.admin.api.vo.HrsfCalibrationBaseTaskVO;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.mapper.*;
import com.bbac.hrsf.admin.service.*;
import com.bbac.hrsf.common.core.exception.CheckedException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Service
@AllArgsConstructor
public class HrsfPmsUserServiceImpl extends ServiceImpl<HrsfPmsUserMapper, HrsfPmsUser> implements IHrsfPmsUserService {

    private final IHrsfPmsUserRelationService hrsfPmsUserRelationService;

    private final IHrsfPmsUserRelationInfoService hrsfPmsUserRelationInfoService;

    private final IHrsfUserBaseService hrsfUserBaseService;

    private final HrsfPmsUserRelationInfoMapper hrsfPmsUserRelationInfoMapper;

    private final HrsfPmsUserRelationMapper hrsfPmsUserRelationMapper;


    @Override
    public IPage<HrsfPmsUser> getUserPageInfo(Page page, HrsfPmsUserDTO hrsfPmsUserDTO) {
        Page page2 = baseMapper.selectPage(page, buildQueryWrapper(hrsfPmsUserDTO));

        List<HrsfPmsUser> records = page2.getRecords();
        if(CollUtil.isNotEmpty(records)){
            records.forEach(o->{
                String pmsStaffName = o.getPmsStaffName();
                String pmsStaff = o.getPmsStaff();
                String str=pmsStaffName+"("+pmsStaff+")";
                o.setPmsStaffName(str);
            });
            page2.setRecords(records);
        }
        return page2;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePmsUser(HrsfPmsUserUpdateDTO updateDTO) {
        /**
         * 1,更新绩效协调员的基本信息 2,删除绩效协调员工关系表 3新增绩效协调员工关系表
         * 4 删除员工绩效员的对应表
         * 5 新增员工绩效员的对应表
         */
        try {
            updateById(HrsfUpmsConvert.INSTANCE.toHrsfPmsUser(updateDTO));
            hrsfPmsUserRelationMapper.deleteByPmsUserId(updateDTO.getPmsUserId());
            List<HrsfPmsUserRelation> relationList = new ArrayList<>();
            Set<HrsfPmsUserRelationInfo> relationInfoList = new HashSet<>();
            updateDTO.getSelectList().stream().forEach(o -> relationList.add(new HrsfPmsUserRelation(
                    updateDTO.getPmsUserId(), updateDTO.getPmsStaff(), updateDTO.getDescription(),
                    o.getSystem(), o.getDepartment(), "", ""
                    , o.getUserLevel(), o.getDirectlyUnder(), o.getAssistant())));
            hrsfPmsUserRelationService.saveBatch(relationList);
            hrsfPmsUserRelationInfoMapper.deleteByPmsUserId(updateDTO.getPmsUserId());
            /**
             * 根据多个筛选器分别获取员工ID
             * ,然后改造成HrsfPmsUserRelationInfo对象
             * pmsUserId/pmsStaffId/staffId
             */
            updateDTO.getSelectList().stream().forEach(userDTO ->
                    hrsfUserBaseService.getUserInfoList(userDTO).stream().forEach(data ->
                            relationInfoList.add(new HrsfPmsUserRelationInfo(updateDTO.getPmsUserId(), updateDTO.getPmsStaff(), data.getStaffId()))));
            if (CollectionUtil.isNotEmpty(relationInfoList)) {
                hrsfPmsUserRelationInfoService.saveBatch(relationInfoList);
            }
        } catch (Exception e) {
            log.error("保存失败:{}", e);
            throw new CheckedException("保存失败,请稍后再试!");
        }
        return true;
    }

    @Override
    public HrsfPmsUserVO getPmsUserInfo(Long pmsUserId) {
        List<HrsfPmsUserRelation> list = hrsfPmsUserRelationService.list(Wrappers.<HrsfPmsUserRelation>lambdaQuery()
                .eq(HrsfPmsUserRelation::getPmsUserId, pmsUserId));
        HrsfPmsUser pmsUser = getById(pmsUserId);
        //直接从关系表中去数据,如果未取到从绩效协调员表中获取数据
        if (CollectionUtil.isEmpty(list)) {
            return HrsfUpmsConvert.INSTANCE.HrsfPmsUserVO(pmsUser);
        }
        List<HrsfUserFilterDTO> dtoList = HrsfUpmsConvert.INSTANCE.toHrsfUserDTO(list);
        List<HrsfUserVO> userVOList = new ArrayList<>();
        dtoList.stream().forEach(p -> userVOList.addAll(hrsfUserBaseService.getUserInfoList(p)));
        return HrsfUpmsConvert.INSTANCE.toHrsfPmsUserVO(
                list, userVOList, pmsUser);
    }

    private LambdaQueryWrapper buildQueryWrapper(HrsfPmsUserDTO hrsfPmsUserDTO) {
        LambdaQueryWrapper<HrsfPmsUser> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(hrsfPmsUserDTO.getPmsStaff())) {
            wrapper.like(HrsfPmsUser::getPmsStaff, hrsfPmsUserDTO.getPmsStaff()).or().like(
                    HrsfPmsUser::getPmsStaffName, hrsfPmsUserDTO.getPmsStaff());
        }
        return wrapper;
    }
}
