package com.bbac.hrsf.admin.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserUpdateDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserFilterDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUser;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserCompetency;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelation;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.entity.SysUser;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserPhotoVO;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.api.vo.PerformanceRatingVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelDownVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelUploadVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelYearDownVO;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelYearUploadVO;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnumNew;
import com.bbac.hrsf.common.core.constant.enums.UserLevelEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.convert.HrsfOrganizaConvert</li>
 * <li>CreateTime : 2022/05/07 17:17:50</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface HrsfUpmsConvert {
    HrsfUpmsConvert INSTANCE = Mappers.getMapper(HrsfUpmsConvert.class);


    default HrsfOrganizationVO toHrsfOrganizationVO(Set<String> systemList, Set<String> departmentList, Set<String> sectionList, Set<String> userGroupList, Set<String> userLevelList) {
        HrsfOrganizationVO hrsfOrganizationVO = new HrsfOrganizationVO();
        /**
         *  hrsfOrganizationVO.setSystem(systemList);
         hrsfOrganizationVO.setDepartment(departmentList);
         hrsfOrganizationVO.setSection(sectionList);
         hrsfOrganizationVO.setUserGroup(userGroupList);
         hrsfOrganizationVO.setUserLevel(userLevelList);*/
        return hrsfOrganizationVO;
    }

    @Mapping(source = "userId", target = "id")
    HrsfUserVO toHrsfUserVO(HrsfUserBase hrsfUserBase);


    HrsfUserVO toHrsfUserVO(HrsfPmsuserBase hrsfPmsuserBase);


    @Mapping(source = "pmsUserId", target = "id")
    HrsfPmsUser toHrsfPmsUser(HrsfPmsUserUpdateDTO updateDTO);

    default HrsfPmsUserVO toHrsfPmsUserVO(List<HrsfPmsUserRelation> list, List<HrsfUserVO> userVOList, HrsfPmsUser pmsUser) {
        HrsfPmsUserVO hrsfPmsUserVO = new HrsfPmsUserVO();
        hrsfPmsUserVO.setSelectList(toHrsfUserDTO(list));
        hrsfPmsUserVO.setPmsUserId(pmsUser.getId());
        hrsfPmsUserVO.setPmsStaff(pmsUser.getPmsStaff());
        hrsfPmsUserVO.setPmsStaffName(pmsUser.getPmsStaffName());
        hrsfPmsUserVO.setDescription(pmsUser.getDescription());
        hrsfPmsUserVO.setHrsfUserList(userVOList);
        return hrsfPmsUserVO;
    }

    List<HrsfUserFilterDTO> toHrsfUserDTO(List<HrsfPmsUserRelation> list);

    @Mapping(source = "id", target = "pmsUserId")
    HrsfPmsUserVO HrsfPmsUserVO(HrsfPmsUser pmsUser);

    default HrsfUserBase toHrsfUserBase(JSONObject obj) {
        HrsfUserBase userBase = new HrsfUserBase();
        userBase.setStatus(obj.getStr("status"));
        userBase.setUserName(obj.getStr("username"));
        userBase.setStaffId(obj.getStr("userId"));
        /**
         * 2022-07-19
         * userBase.setUserId(obj.getStr("empId"));
         * 这里的empID没有作用
         * 替换成userId
         */
        userBase.setEmpId(obj.getStr("empId"));
        userBase.setUserId(obj.getStr("userId"));
        String firstName = obj.getStr("firstName");
        String lastName = obj.getStr("lastName");
        //设置中英文名称，排除为null的情况
        userBase.setFullName(StrUtil.join(" ", Arrays.asList(lastName, firstName).stream().filter(data ->
                StrUtil.isNotBlank(data)).collect(Collectors.toList())));

        userBase.setGender(obj.getStr("gender"));
        userBase.setAge(obj.getStr("businessPhone"));
        userBase.setEmail(obj.getStr("email"));
        userBase.setCountry(obj.getStr("country"));
        userBase.setMajor(obj.getStr("jobLevel"));

        userBase.setInitialWorkingDate(obj.getStr("finalJobCode"));
        userBase.setLenofsInBbac(obj.getStr("addressLine1"));
        userBase.setLenofsInCurrentPosition(obj.getStr("addressLine2"));
        userBase.setLenofsInCurrnetLevel(obj.getStr("addressLine3"));
        userBase.setSystem(obj.getStr("custom01") == null ? null : CommonConstants.NA.equals(obj.getStr("custom01")) ? null : obj.getStr("custom01"));
        userBase.setDepartment(obj.getStr("division") == null ? null : CommonConstants.NA.equals(obj.getStr("division")) ? null : obj.getStr("division"));
        userBase.setSection(obj.getStr("department") == null ? null : CommonConstants.NA.equals(obj.getStr("department")) ? null : obj.getStr("department"));
        userBase.setUserGroup(obj.getStr("location") == null ? null : CommonConstants.NA.equals(obj.getStr("location")) ? null : obj.getStr("location"));
        userBase.setPosition(obj.getStr("title") == null ? null : CommonConstants.NA.equals(obj.getStr("title")) ? null : obj.getStr("title"));


        userBase.setJobCode(obj.getStr("jobCode"));

        userBase.setSyl4(obj.getJSONObject("homePhoneNav") == null ? null : obj.getJSONObject("homePhoneNav").getStr("externalCode"));
        userBase.setAssistant(obj.getJSONObject("nicknameNav") == null ? null : obj.getJSONObject("nicknameNav").getStr("externalCode"));
        userBase.setLevelAssessment(obj.getJSONObject("custom05Nav") == null ? null : obj.getJSONObject("custom05Nav").getStr("externalCode"));
        userBase.setNewHire(obj.getJSONObject("cityNav") == null ? null : obj.getJSONObject("cityNav").getStr("externalCode"));
        userBase.setNewPromotion(obj.getJSONObject("custom11Nav") == null ? null : obj.getJSONObject("custom11Nav").getStr("externalCode"));

        userBase.setAttendanceRate(obj.getStr("custom06"));
        userBase.setMaternityLeave(obj.getStr("custom07"));
        userBase.setAbsence(obj.getStr("custom08"));
        userBase.setPxpPlace(obj.getStr("matrix1Label"));
        userBase.setPxcYPlace(obj.getStr("matrix2Label"));
        userBase.setJobTitle(obj.getStr("jobTitle"));

        //需要做特殊处理的字段
        if (obj.getJSONObject("reviewFreqNav") != null) {
            JSONObject reviewFreqNav = obj.getJSONObject("reviewFreqNav");
            Optional<Object> optional = reviewFreqNav.getJSONObject("picklistLabels").getJSONArray("results").stream()
                    .filter(label -> "zh_CN".equals(((JSONObject) label).getStr("locale"))).findFirst();
            userBase.setHightestEducation(((JSONObject) optional.get()).getStr("label"));
        }
        if (obj.getJSONObject("levelNav") != null) {
            JSONObject levelNav = obj.getJSONObject("levelNav");
            Optional<Object> optional = levelNav.getJSONObject("picklistLabels").getJSONArray("results").stream()
                    .filter(label -> "zh_CN".equals(((JSONObject) label).getStr("locale"))).findFirst();
            Optional<Object> optionalEn = levelNav.getJSONObject("picklistLabels").getJSONArray("results").stream()
                    .filter(label -> "en_US".equals(((JSONObject) label).getStr("locale"))).findFirst();
            userBase.setHightestDegree(((JSONObject) optional.get()).getStr("label") + " " + ((JSONObject) optionalEn.get()).getStr("label"));
        }
        userBase.setUserLevel(obj.getJSONObject("custom09Nav") == null ? null : obj.getJSONObject("custom09Nav").getStr("externalCode"));
        userBase.setPositionLevel(obj.getJSONObject("custom10Nav") == null ? null : obj.getJSONObject("custom10Nav").getStr("externalCode"));
        userBase.setEmploymentType(obj.getJSONObject("stateNav") == null ? null : obj.getJSONObject("stateNav").getStr("externalCode"));
        userBase.setBcWc(obj.getJSONObject("custom03Nav") == null ? null : obj.getJSONObject("custom03Nav").getStr("externalCode"));
        userBase.setShareholdersJoinBbac(obj.getJSONObject("custom02Nav") == null ? null : obj.getJSONObject("custom02Nav").getStr("externalCode"));
        userBase.setSpecialResignedEmployee(obj.getJSONObject("custom04Nav") == null ? null : obj.getJSONObject("custom04Nav").getStr("externalCode"));
        JSONObject manager = obj.getJSONObject("manager");
        userBase.setEvaluator1(manager == null ? null :
                StrUtil.join(" ", Arrays.asList(manager.getStr("lastName"), manager.getStr("firstName")).stream().filter(data ->
                        StrUtil.isNotBlank(data)).collect(Collectors.toList())));
        if (manager != null) {
            JSONObject levelManager = manager.getJSONObject("manager");
            userBase.setLevelEvaluator(levelManager == null ? null : StrUtil.join(" ", Arrays.asList(levelManager.getStr("lastName"), levelManager.getStr("firstName")).stream().filter(data ->
                    StrUtil.isNotBlank(data)).collect(Collectors.toList())));
        }
        JSONObject matrixManagerJson = obj.getJSONObject("matrixManager");
        if (matrixManagerJson != null) {
            Set<String> collect = matrixManagerJson.getJSONArray("results").stream()
                    .map(matrix -> StrUtil.join(" ", Arrays.asList(((JSONObject) matrix).getStr("lastName"), ((JSONObject) matrix).getStr("firstName")).stream().filter(data ->
                            StrUtil.isNotBlank(data)).collect(Collectors.toList()))).collect(Collectors.toSet());
            String join = StrUtil.join("|", collect);
            userBase.setEvaluator2(join);
        }
        //特殊处理时间
        String hireDate = obj.getStr("hireDate");
        String lastReviewDate = obj.getStr("dateOfCurrentPosition");
        if (StrUtil.isNotBlank(hireDate)) {
            String hireDateStr = hireDate.substring(hireDate.indexOf("(") + 1, hireDate.indexOf(")"));
            userBase.setHireDate(Instant.ofEpochMilli(Long.valueOf(hireDateStr)).atZone(ZoneOffset.ofHours(8)).toLocalDateTime());
        }
        if (StrUtil.isNotBlank(lastReviewDate)) {
            String lastReviewDateStr = lastReviewDate.substring(lastReviewDate.indexOf("(") + 1, lastReviewDate.indexOf(")"));
            userBase.setShareholdersJoinDate(Instant.ofEpochMilli(Long.valueOf(lastReviewDateStr)).atZone(ZoneOffset.ofHours(8)).toLocalDateTime());

        }
        return userBase;
    }

    /**
     * 需要特殊处理某些字段
     *
     * @param list
     * @return
     */
    default List<HrsfPmsuserBase> toHrsfPmsuserBase(List<HrsfUserBase> list, String assesType) {
        List<HrsfPmsuserBase> pmsUserBaseList = list.stream().map(data -> {
                    HrsfPmsuserBase hrsfPmsuserBase = toHrsfPmsuserBasePo(data);
                    /**
                     * 若隔级上级评分=Y，评分人=隔级上级|评分人1|评分人2；若隔级上级评分为空或N，评分人=评分人1|评分人2
                     * --todo
                     * 还得获取隔级上级评分人
                     */
                    if (CommonConstants.FLAG_Y.equals(data.getLevelAssessment())) {
                        List<String> evaluatorList = Arrays.asList(data.getLevelEvaluator(), data.getEvaluator1(), data.getEvaluator2());
                        /**
                         * 移除为null的数据
                         */
                        List<String> filterList = evaluatorList.stream().filter(p -> StrUtil.isNotBlank(p)).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(filterList)) {
                            hrsfPmsuserBase.setEvaluators(StrUtil.join("|", filterList));
                        }
                    } else {
                        List<String> evaluatorList = Arrays.asList(data.getEvaluator1(), data.getEvaluator2());
                        /**
                         * 移除为null的数据
                         */
                        List<String> filterList = evaluatorList.stream().filter(p -> StrUtil.isNotBlank(p)).collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(filterList)) {
                            hrsfPmsuserBase.setEvaluators(StrUtil.join("|", filterList));
                        }
                    }
                    return hrsfPmsuserBase;
                }

        ).collect(Collectors.toList());


        /**
         * 新增判断，若为季度考核，针对L3的filter，无需发起校准会议；白领L3不参与季度考核
         *
         * 2022-07-20
         * 新增逻辑 季度考核没有L1\L2\L3
         * 年度考核没有L1\L2
         */
        if (!AssessTypeEnum.YEAR.getType().equals(assesType)) {
            return pmsUserBaseList.stream().filter(data -> !(UserLevelEnum.L1.name().equals(data.getUserLevel())
                    || UserLevelEnum.L2.name().equals(data.getUserLevel())
                    || UserLevelEnum.L3.name().equals(data.getUserLevel()))).collect(Collectors.toList());
        } else {
            return pmsUserBaseList.stream().filter(data -> !(UserLevelEnum.L1.name().equals(data.getUserLevel())
                    || UserLevelEnum.L2.name().equals(data.getUserLevel()))).collect(Collectors.toList());
        }
    }

    /**
     * 需要特殊处理某些字段
     *
     * @param hrsfUserBase
     * @return
     */
    @Mapping(target = "createBy", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateBy", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    HrsfPmsuserBase toHrsfPmsuserBasePo(HrsfUserBase hrsfUserBase);

    /**
     * --todo
     * 需要特殊处理某些字段
     *
     * @param
     * @return
     */

    default HrsfPmsuserBase toHrsfPmsuserBase(PmsUserExcelYearUploadVO excelYearVO, HrsfPmsuserBase hrsfPmsuserBase) {
        HrsfPmsuserBase pmsuserBase = new HrsfPmsuserBase();
        pmsuserBase.setId(hrsfPmsuserBase.getId());
        String assessYear = hrsfPmsuserBase.getAssessYear();
        int year = 2025; // 默认值
        if (StringUtils.isNotBlank(assessYear)) {
            year = Integer.parseInt(assessYear);
        }
        if (year < 2025) {
            pmsuserBase.setPmsLevel(PmsLevelEnum.getByDescription(excelYearVO.getPmsLevelDesc()));
        } else {
            pmsuserBase.setPmsLevel(PmsLevelEnumNew.getByDescription(excelYearVO.getPmsLevelDesc()));
        }
        pmsuserBase.setPmsLevelDesc(excelYearVO.getPmsLevelDesc());
        pmsuserBase.setTarget1Score(BigDecimal.valueOf(excelYearVO.getTarget1Score()));
        pmsuserBase.setTarget2Score(BigDecimal.valueOf(excelYearVO.getTarget2Score()));
        pmsuserBase.setTarget3Score(BigDecimal.valueOf(excelYearVO.getTarget3Score()));
        pmsuserBase.setTarget4Score(BigDecimal.valueOf(excelYearVO.getTarget4Score()));
        pmsuserBase.setTarget5Score(BigDecimal.valueOf(excelYearVO.getTarget5Score()));
        pmsuserBase.setTarget6Score(BigDecimal.valueOf(excelYearVO.getTarget6Score()));
        pmsuserBase.setTarget7Score(BigDecimal.valueOf(excelYearVO.getTarget7Score()));
        pmsuserBase.setTarget8Score(BigDecimal.valueOf(excelYearVO.getTarget8Score()));
        pmsuserBase.setTarget9Score(BigDecimal.valueOf(excelYearVO.getTarget9Score()));
        pmsuserBase.setTarget10Score(BigDecimal.valueOf(excelYearVO.getTarget10Score()));


        return pmsuserBase;
    }

    /**
     * --todo
     * 需要特殊处理某些字段
     *
     * @param
     * @return
     */
    default HrsfPmsuserBase toHrsfPmsuserBase(PmsUserExcelUploadVO excelVO, HrsfPmsuserBase hrsfPmsuserBase) {
        HrsfPmsuserBase pmsuserBase = new HrsfPmsuserBase();
        pmsuserBase.setId(hrsfPmsuserBase.getId());
        String assessYear = hrsfPmsuserBase.getAssessYear();
        int year = 2025; // 默认值
        if (StringUtils.isNotBlank(assessYear)) {
            year = Integer.parseInt(assessYear);
        }
        if (year < 2025) {
            pmsuserBase.setPmsLevel(PmsLevelEnum.getByDescription(excelVO.getPmsLevelDesc()));
        } else {
            pmsuserBase.setPmsLevel(PmsLevelEnumNew.getByDescription(excelVO.getPmsLevelDesc()));
        }

        pmsuserBase.setPmsLevelDesc(excelVO.getPmsLevelDesc());
        pmsuserBase.setTarget1Score(BigDecimal.valueOf(excelVO.getTarget1Score()));
        pmsuserBase.setTarget2Score(BigDecimal.valueOf(excelVO.getTarget2Score()));
        pmsuserBase.setTarget3Score(BigDecimal.valueOf(excelVO.getTarget3Score()));
        pmsuserBase.setTarget4Score(BigDecimal.valueOf(excelVO.getTarget4Score()));
        pmsuserBase.setTarget5Score(BigDecimal.valueOf(excelVO.getTarget5Score()));
        return pmsuserBase;
    }

    /**
     * 修改问题清单413
     * 解决下载模板中带上小分
     *
     * @param hrsfPmsuserBase
     * @return
     */
//    @Mapping(target = "target1Score", ignore = true)
//    @Mapping(target = "target2Score", ignore = true)
//    @Mapping(target = "target3Score", ignore = true)
//    @Mapping(target = "target4Score", ignore = true)
//    @Mapping(target = "target5Score", ignore = true)
//    @Mapping(target = "pmsLevelDesc", ignore = true)
    PmsUserExcelDownVO toUserExcelVO(HrsfPmsuserBase hrsfPmsuserBase);

    /**
     * 修改问题清单413
     * 解决下载模板中带上小分
     *
     * @param p
     * @return
     */
//    @Mapping(target = "target1Score", ignore = true)
//    @Mapping(target = "target2Score", ignore = true)
//    @Mapping(target = "target3Score", ignore = true)
//    @Mapping(target = "target4Score", ignore = true)
//    @Mapping(target = "target5Score", ignore = true)
//    @Mapping(target = "target6Score", ignore = true)
//    @Mapping(target = "target7Score", ignore = true)
//    @Mapping(target = "target8Score", ignore = true)
//    @Mapping(target = "target9Score", ignore = true)
//    @Mapping(target = "target10Score", ignore = true)
//    @Mapping(target = "pmsLevelDesc", ignore = true)
    PmsUserExcelYearDownVO toUserExcelYearVO(HrsfPmsuserBase p);

    default SysUser toUserInfo(HrsfUserBase hrsfUserBase) {
        SysUser sysUser = new SysUser();

        sysUser.setUsername(hrsfUserBase.getStaffId());
        sysUser.setFullName(hrsfUserBase.getFullName());
        sysUser.setUserId(hrsfUserBase.getStaffId());
        sysUser.setLockFlag("0");
        sysUser.setPhoto(hrsfUserBase.getPhoto());

        return sysUser;
    }

    HrsfPmsUserPhotoVO toHrsfPmsUserPhotoVO(HrsfPmsuserBase p);

    HrsfUserFilterDTO toUserDTO(HrsfPmsUserRelation userDTO);

    default PerformanceRatingVO toPerformanceRatingVO(JSONObject obj) {
        PerformanceRatingVO performanceRatingVO = new PerformanceRatingVO();
        performanceRatingVO.setCustYear(obj.getStr("cust_year"));
        performanceRatingVO.setCustType(obj.getStr("cust_type"));
        performanceRatingVO.setCustRating(obj.getStr("cust_rating"));
        String startDate = obj.getStr("effectiveStartDate");
        if (startDate != null) {
            performanceRatingVO.setEffectiveStartDate(Long.valueOf(startDate.substring(startDate.indexOf("(") + 1, startDate.indexOf(")"))));
        }
        performanceRatingVO.setCustTrend(obj.getJSONObject("cust_trendNav") == null ? null : obj.getJSONObject("cust_trendNav").getStr("label_en_US"));
        performanceRatingVO.setCustPerank(obj.getJSONObject("cust_perankNav") == null ? null : obj.getJSONObject("cust_perankNav").getStr("label_en_US"));
        performanceRatingVO.setCustPorank(obj.getJSONObject("cust_porankNav") == null ? null : obj.getJSONObject("cust_porankNav").getStr("label_en_US"));
        return performanceRatingVO;
    }

    HrsfPmsUserCompetency toHrsfPmsUserCompetency(HrsfPmsUserCompetency competency);
}