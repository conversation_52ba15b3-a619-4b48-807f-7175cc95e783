/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.HrsfRoleUserDTO;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.entity.SysUserRole;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.mapper.SysUserRoleMapper;
import com.bbac.hrsf.admin.service.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 用户角色表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Service
@RequiredArgsConstructor
public class SysUserRoleServiceImpl extends ServiceImpl<SysUserRoleMapper, SysUserRole> implements SysUserRoleService {


    private final IHrsfUserService hrsfUserService;

    private final IHrsfUserBaseService hrsfUserBaseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRoleUserInfo(HrsfRoleUserDTO hrsfRoleUserDTO) {
        if (hrsfRoleUserDTO.getRoleId() == null) {
            return false;
        }
        baseMapper.deleteByRoleId(hrsfRoleUserDTO.getRoleId());
        List<SysUserRole> userRoleList = new ArrayList<>();
        hrsfRoleUserDTO.getUserId().stream().forEach(o -> {
            userRoleList.add(new SysUserRole(o, hrsfRoleUserDTO.getRoleId()));
        });
        return saveBatch(userRoleList);
    }

    @Override
    public List<HrsfUserVO> getRoleUserInfo(Long roleId, String fullName) {
        if (roleId == null) {
            return null;
        }
        List<SysUserRole> list = list(Wrappers.<SysUserRole>lambdaQuery().eq(SysUserRole::getRoleId, roleId));
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        List<HrsfUserBase> hrsfUsers = hrsfUserBaseService.listByIds(list.stream()
                .map(SysUserRole::getUserId).collect(Collectors.toSet()));
        if (StrUtil.isNotBlank(fullName)) {
            hrsfUsers = hrsfUsers.stream().filter(f -> f.getFullName().toLowerCase().contains(fullName.toLowerCase()) || f.getUserName().toLowerCase().contains(fullName.toLowerCase())).collect(Collectors.toList());
        }
        return hrsfUsers.stream().map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
    }

    @Override
    public Boolean deleteRoleUserInfo(Long roleId, String userId) {
        if (roleId == null || userId == null) {
            return false;
        }
        return remove(Wrappers.<SysUserRole>lambdaQuery().eq(SysUserRole::getRoleId, roleId).eq(SysUserRole::getUserId, userId));
    }
}
