package com.bbac.hrsf.admin.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserScoreTask;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskCountingVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTemplateVo;
import com.bbac.hrsf.admin.handle.SFApiManager;
import com.bbac.hrsf.admin.mapper.HrsfPmsUserScoreTaskMapper;
import com.bbac.hrsf.admin.service.IHrsfUserScoreDuplicateService;
import com.bbac.hrsf.common.core.constant.CacheConstants;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.mybatis.config.MybatisPlusMetaObjectHandler;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class HrsfUserScoreDuplicateServiceImpl extends ServiceImpl<HrsfPmsUserScoreTaskMapper, HrsfPmsUserScoreTask> implements IHrsfUserScoreDuplicateService {

    private final HrsfPmsUserScoreTaskMapper hrsfPmsUserScoreTaskMapper;
    private final MybatisPlusMetaObjectHandler mybatisPlusMetaObjectHandler;
    private final SFApiManager sfApiManager;
    private final RedisTemplate redisTemplate;
    // 历史绩效分数任务线程池
    private final ThreadPoolTaskExecutor userScoreDuplicateTaskExecutor;


    @Value("${sf.sync.usernameSDAPI:SDAPI@beijingbenT1}")
    private String sfUserName;
    /** 业绩得分 itemId：评分 Rating（%） */
    @Value("${sf.sync.itemId}")
    private String sfYearScoreItemId;
    /** 年度小分指标 itemId1：业绩-工作任务完成 Performance-Completion of work */
    @Value("${sf.sync.itemId1}")
    private String sfYearScoreItemId1;
    /** 年度小分指标 itemId2：业绩-质量目标 Performance-Work Quality */
    @Value("${sf.sync.itemId2}")
    private String sfYearScoreItemId2;
    /** 年度小分指标 itemId3： 业绩-安全生产 Performance-Production Safety*/
    @Value("${sf.sync.itemId3}")
    private String sfYearScoreItemId3;
    /** 年度小分指标 itemId4：业绩-出勤 Performance-Attendance */
    @Value("${sf.sync.itemId4}")
    private String sfYearScoreItemId4;
    /** 年度小分指标 itemId5：业绩-成本控制 Performance-Cost Control */
    @Value("${sf.sync.itemId5}")
    private String sfYearScoreItemId5;
    /** 年度小分指标 itemId6：能力-人际沟通 Ability-Communication */
    @Value("${sf.sync.itemId6}")
    private String sfYearScoreItemId6;
    /** 年度小分指标 itemId7：能力-团队协作 Ability-Team Work */
    @Value("${sf.sync.itemId7}")
    private String sfYearScoreItemId7;
    /** 年度小分指标 itemId8：能力-专业技能 Ability-Professional */
    @Value("${sf.sync.itemId8}")
    private String sfYearScoreItemId8;
    /** 年度小分指标 itemId9：态度-敬业负责 Attitude-Responsibility */
    @Value("${sf.sync.itemId9}")
    private String sfYearScoreItemId9;
    /** 年度小分指标 itemId10：态度-严谨规范 Attitude-Discipline */
    @Value("${sf.sync.itemId10}")

    private String sfYearScoreItemId10;
    /** 蓝领季度小分指标 itemId11：质量目标 Work Quality */
    @Value("${sf.sync.itemId11}")
    private String sfBlueQuarterScoreItemId1;
    /** 蓝领季度小分指标 itemId12：成本控制 Cost Control */
    @Value("${sf.sync.itemId12}")
    private String sfBlueQuarterScoreItemId2;
    /** 蓝领季度小分指标 itemId13：工作任务完成 Completion of Work */
    @Value("${sf.sync.itemId13}")
    private String sfBlueQuarterScoreItemId3;
    /** 蓝领季度小分指标 itemId14：安全生产 Production Safety */
    @Value("${sf.sync.itemId14}")
    private String sfBlueQuarterScoreItemId4;
    /** 蓝领季度小分指标 itemId15：出勤 Attendance */
    @Value("${sf.sync.itemId15}")
    private String sfBlueQuarterScoreItemId5;

    /** 白领季度小分指标 itemId16：质量目标 Work Quality */
    @Value("${sf.sync.itemId16}")
    private String sfWhiteQuarterScoreItemId1;
    /** 白领季度小分指标 itemId17：成本控制 Cost Control */
    @Value("${sf.sync.itemId17}")
    private String sfWhiteQuarterScoreItemId2;
    /** 白领季度小分指标 itemId18：工作任务完成 Completion of Work */
    @Value("${sf.sync.itemId18}")
    private String sfWhiteQuarterScoreItemId3;
    /** 白领季度小分指标 itemId19：安全生产 Production Safety */
    @Value("${sf.sync.itemId19}")
    private String sfWhiteQuarterScoreItemId4;
    /** 白领季度小分指标 itemId20：出勤 Attendance */
    @Value("${sf.sync.itemId20}")
    private String sfWhiteQuarterScoreItemId5;
    @Value("${sf.sync.formTemplateId1}")
    private String formTemplateId1;
    @Value("${sf.sync.formTemplateId2}")
    private String formTemplateId2;
    @Value("${sf.sync.formTemplateId3}")
    private String formTemplateId3;
    @Value("${sf.sync.formTemplateId4}")
    private String formTemplateId4;
    @Value("${sf.sync.formTemplateId5}")
    private String formTemplateId5;
    @Value("${sf.sync.formTemplateId6}")
    private String formTemplateId6;

    @Override
    public String startUserScoreTask(Long currTaskId) {
        // 从缓存判断任务是否在执行
        Object cacheId = redisTemplate.opsForValue().get(CacheConstants.CURRENT_USER_SCORE_TASK_ID_DUP);
        if(cacheId != null){
            log.info("startDuplicatePeriodTask processing, taskId:{}", cacheId);
            // 任务处理中
            return CommonConstants.PROCESSING_MESSAGE;
        }
        // 获取当前任务状态
        HrsfPmsUserScoreTask currentScoreTask = getCurrentTaskStatus();
        Long taskId = currentScoreTask != null ? currentScoreTask.getId() : null;
        // 从数据库判断任务是否在执行
        String userName = mybatisPlusMetaObjectHandler.getUserName();
        LocalDateTime operateTime = LocalDateTime.now();
        HrsfPmsUserScoreTask scoreTask = null;
        if(currTaskId != null){
            // 重新推送失败任务
            if(!currTaskId.equals(taskId) || currentScoreTask.getStatus() != 3) {
                log.info("startDuplicatePeriodTask invalid operation, taskId: {}", taskId);
                return String.format("当前任务状态或任务ID不正确，不可以操作 Invalid operation due to invalid task id(%d)", taskId);
            }
            scoreTask = currentScoreTask;
            // 初始化任务明细
            hrsfPmsUserScoreTaskMapper.batchUpdateFailedTaskDetail(taskId,0, userName, operateTime);
            // 回写明细数量及最大任务Id
            HrsfPmsUserScoreTaskCountingVo taskCountingVo = hrsfPmsUserScoreTaskMapper.obtainTaskDetailCounting(taskId);
            scoreTask.setMaxDetailId(taskCountingVo.getTaskDetailMaxId());
            scoreTask.setStatus(1);
            scoreTask.setUpdateBy(userName);
            scoreTask.setUpdateTime(operateTime);
            hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
            log.info("startDuplicatePeriodTask restarted, taskId: {}", taskId);
        }else {
            if(currentScoreTask != null && currentScoreTask.getStatus() == 3){
                // 终止失败的任务
                hrsfPmsUserScoreTaskMapper.batchUpdateFailedTaskDetail(taskId,4, userName, operateTime);
                currentScoreTask.setStatus(4);
                currentScoreTask.setUpdateBy(userName);
                currentScoreTask.setUpdateTime(operateTime);
                hrsfPmsUserScoreTaskMapper.updateById(currentScoreTask);
            }
            scoreTask = new HrsfPmsUserScoreTask();
            scoreTask.setTaskType(2);
            scoreTask.setStatus(1);
            scoreTask.setFormDataPullFlag(2);
            scoreTask.setStartTime(operateTime);
            scoreTask.setCreateBy(userName);
            scoreTask.setCreateTime(operateTime);
            // 创建任务
            hrsfPmsUserScoreTaskMapper.insert(scoreTask);
            taskId = scoreTask.getId();
            // 初始化任务明细
            int effectCount = this.initTaskDetail(scoreTask);
            if(effectCount == 0){
                scoreTask.setStatus(2);
                scoreTask.setUpdateBy(userName);
                scoreTask.setUpdateTime(operateTime);
                // 更新数据库状态并清空缓存
                hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
                return CommonConstants.SUCCESS_FLAG;
            }
            log.info("startDuplicatePeriodTask initTaskDetail taskId:{}, userName:{}, operateTime:{}", taskId, userName, operateTime);
            // 回写明细数量及最大任务Id
            HrsfPmsUserScoreTaskCountingVo taskCountingVo = hrsfPmsUserScoreTaskMapper.obtainTaskDetailCounting(taskId);
            scoreTask.setProcessNumber(taskCountingVo.getTaskDetailCount());
            scoreTask.setMaxDetailId(taskCountingVo.getTaskDetailMaxId());
            hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
            log.info("startDuplicatePeriodTask started, taskId: {}", taskId);
        }
        // 缓存任务状态
        redisTemplate.opsForValue().set(CacheConstants.CURRENT_USER_SCORE_TASK_ID_DUP, taskId,
                CacheConstants.CURRENT_USER_SCORE_TASK_ID_CACHE_TIME, TimeUnit.MINUTES);
        HrsfPmsUserScoreTask finalScoreTask = scoreTask;
        CompletableFuture.runAsync(() -> handleHrsfPmsDuplicatePeriodTask(finalScoreTask));
        return CommonConstants.SUCCESS_FLAG;
    }

    /**
     * 检查绩效复制任务
     */
    public void checkUserScoreTask(){
        // 从缓存判断任务是否在执行
        Object cacheId = redisTemplate.opsForValue().get(CacheConstants.CURRENT_USER_SCORE_TASK_ID_DUP);
        if(cacheId != null){
            return;
        }
        HrsfPmsUserScoreTask currentTask = getCurrentTaskStatus();
        if(currentTask == null || currentTask.getStatus() != 1){
            return;
        }
        Long taskId = currentTask.getId();
        log.info("checkScoreDuplicateTaskStatus restart, taskId:{}", currentTask.getId());
        // 当前操作人及时间
        String userName = mybatisPlusMetaObjectHandler.getUserName();
        LocalDateTime operateTime = LocalDateTime.now();
        // 更新阻塞任务明细状态
        hrsfPmsUserScoreTaskMapper.batchUpdatePendingTaskDetail(taskId, userName, operateTime);
        // 回写明细数量及最大任务Id
        HrsfPmsUserScoreTaskCountingVo taskCountingVo = hrsfPmsUserScoreTaskMapper.obtainTaskDetailCounting(taskId);
        currentTask.setMaxDetailId(taskCountingVo.getTaskDetailMaxId());
        currentTask.setStatus(1);
        currentTask.setUpdateBy(userName);
        currentTask.setUpdateTime(operateTime);
        hrsfPmsUserScoreTaskMapper.updateById(currentTask);
        // 缓存任务状态
        redisTemplate.opsForValue().set(CacheConstants.CURRENT_USER_SCORE_TASK_ID_DUP, taskId,
                CacheConstants.CURRENT_USER_SCORE_TASK_ID_CACHE_TIME, TimeUnit.MINUTES);
        CompletableFuture.runAsync(() -> handleHrsfPmsDuplicatePeriodTask(currentTask));
    }

    /**
     * 初始化任务
     * @param scoreTask 任务
     */
    private int initTaskDetail(HrsfPmsUserScoreTask scoreTask){
        int size = 100;
        int i = 0;
        int effect = 0;
        while (i < 1000) {
            int count = this.fetchAssessmentStaff(scoreTask,i * size, size);
            if(count == 0){
                break;
            }
            effect += count;
            i++;
        }
        log.info("HrsfUserScoreDuplicate initTaskDetail effect: {}, ", effect);
        return effect;
    }

    /**
     * 获取评估中的员工，并保存为任务
     * @param scoreTask
     * @param current
     * @param size
     * @return
     */
    private int fetchAssessmentStaff(HrsfPmsUserScoreTask scoreTask, int current, int size) {
        // 获取待考核员工
        JSONArray jsonArray = sfApiManager.fetchAssessmentStaff(current, size);
        if(jsonArray.isEmpty()){
            return 0;
        }
        List<HrsfPmsUserScoreTaskDetailVo> taskDetailVos = new ArrayList<>(jsonArray.size());
        for (Object obj : jsonArray) {
            JSONObject jsonObj = (JSONObject) obj;
            String formSubjectId = jsonObj.getStr("formSubjectId");
            String formDataId = jsonObj.getStr("formDataId");
            if(formSubjectId.equals(scoreTask.getLastStaffId())){
                log.info("fetchAssessmentStaff have same staffId:{}, formDataId:{}, it been ignored", formSubjectId, formDataId);
                continue;
            }else{
                scoreTask.setLastStaffId(formSubjectId);
            }
            String formTemplateId = jsonObj.getStr("formTemplateId");
            HrsfPmsUserScoreTaskDetailVo detailVo = new HrsfPmsUserScoreTaskDetailVo();
            taskDetailVos.add(detailVo);
            detailVo.setTaskId(scoreTask.getId());
            detailVo.setTaskStatus(0);
            detailVo.setStaffId(formSubjectId);
            detailVo.setFormDataId(formDataId);
            // 检测并确定上一周期，以第一次检查结果为准
            this.checkAndConfirmPreviousPeriod(scoreTask, formTemplateId);
            if (formTemplateId1.equals(formTemplateId) || formTemplateId2.equals(formTemplateId) || formTemplateId3.equals(formTemplateId)
                    || formTemplateId4.equals(formTemplateId)) {
                detailVo.setStaffType(1);
            } else if (formTemplateId5.equals(formTemplateId) || formTemplateId6.equals(formTemplateId)) {
                detailVo.setStaffType(2);
            }
            if(detailVo.getStaffType() == null){
                log.info("====invalid staffType with formTemplateId:{}, assesType:{}, staffId:{}",
                        formTemplateId, scoreTask.getAssesType(), detailVo.getStaffId());
            }
        }
        if(!taskDetailVos.isEmpty()){
            hrsfPmsUserScoreTaskMapper.batchInsertTaskDetail(taskDetailVos, scoreTask.getCreateBy(), scoreTask.getCreateTime());
        }
        return taskDetailVos.size();
    }

    /**
     * 检测并确定上一周期，以第一次检查结果为准
     * @param scoreTask 任务
     * @param formTemplateId SF Template
     */
    synchronized void checkAndConfirmPreviousPeriod(HrsfPmsUserScoreTask scoreTask, String formTemplateId){
        log.debug("===debug checkAndConfirmPreviousPeriod scoreTask.assesType:{}, formTemplateId:{}", scoreTask.getAssesType(), formTemplateId);
        if(scoreTask.getAssesType() != null){
            return;
        }
        String assesType = null;
        if (formTemplateId1.equals(formTemplateId) || formTemplateId2.equals(formTemplateId) || formTemplateId5.equals(formTemplateId)) {
            assesType = "Qx";
        } else if (formTemplateId3.equals(formTemplateId) || formTemplateId4.equals(formTemplateId) || formTemplateId6.equals(formTemplateId)) {
            assesType = "YEAR";
        }
        // 当前绩效考核
        HrsfPmstart lastHrsfPmstart = null;
        if("YEAR".equals(assesType)){
            lastHrsfPmstart = hrsfPmsUserScoreTaskMapper.getLastHrsfPmstartYear();
        } else {
            lastHrsfPmstart = hrsfPmsUserScoreTaskMapper.getLastHrsfPmstartQuarter();
        }
        String assesYear = lastHrsfPmstart.getAssesYear();
        assesType = lastHrsfPmstart.getAssesType();
        /*
        if("YEAR".equals(assesType)) {
            assesYear = (Long.parseLong(assesYear) - 1) + "";
        } else if("Q1".equals(assesType)){
            assesYear = (Long.parseLong(assesYear) - 1) + "";
            assesType = "Q3";
        }else if("Q2".equals(assesType)){
            assesType = "Q1";
        }else if("Q3".equals(assesType)){
            assesType = "Q2";
        }
        */
        log.debug("===debug checkAndConfirmPreviousPeriod assesYear:{}, assesType:{}", assesYear, assesType);
        scoreTask.setAssesYear(assesYear);
        scoreTask.setAssesType(assesType);
        hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
    }

    /**
     * 启动历史绩效分数任务
     * @param scoreTask 任务
     */
    private void handleHrsfPmsDuplicatePeriodTask(HrsfPmsUserScoreTask scoreTask){
        Long taskId = scoreTask.getId();
        Integer[] statuses = new Integer[]{0};
        try {
            while (true) {
                List<HrsfPmsUserScoreTaskDetailVo> detailVos = hrsfPmsUserScoreTaskMapper
                        .queryProcessingDetailList(taskId, statuses);
                if(detailVos.isEmpty()){
                    break;
                }
                detailVos.forEach(vo -> vo.setTaskStatus(1));
                hrsfPmsUserScoreTaskMapper.batchUpdateStatus(detailVos, sfUserName, LocalDateTime.now());
                log.info("handeHrsfPmsDuplicatePeriodTask processing. taskId:{}, detailCount:{}", taskId, detailVos.size());
                List<String> userIdList = detailVos.stream().map(HrsfPmsUserScoreTaskDetailVo::getStaffId)
                        .collect(Collectors.toList());
                List<HrsfPmsUserScoreTemplateVo> scoreTemplateVos = null;
                String assesYear = scoreTask.getAssesYear();
                String assesType = scoreTask.getAssesType();
                if ("YEAR".equals(scoreTask.getAssesType())) {
                    // 获取模板所需 年度分数
                    scoreTemplateVos = hrsfPmsUserScoreTaskMapper.queryUserScoreYearList(assesYear, userIdList);
                } else {
                    // 获取模板所需 季度分数
                    scoreTemplateVos = hrsfPmsUserScoreTaskMapper.queryUserScoreQuarterList(assesYear, assesType, userIdList);
                }
                // 聚合单个用户的评分
                Map<String, HrsfPmsUserScoreTemplateVo> templateVoMap = scoreTemplateVos.stream()
                        .collect(Collectors.toMap(HrsfPmsUserScoreTemplateVo::getStaffId, vo -> vo, (v1, v2) -> v1));
                // 多线程处理模板及上传
                int i = detailVos.size() - 1;
                String operator = mybatisPlusMetaObjectHandler.getUserName();
                while (i >= 0) {
                    try {
                        HrsfPmsUserScoreTaskDetailVo detailVo = detailVos.get(i);
                        HrsfPmsUserScoreTemplateVo templateVo = templateVoMap.get(detailVo.getStaffId());
                        if (templateVo == null) {
                            log.info("handeHrsfPmsUserScoreTask templateVo is empty, staffId:{}", detailVo.getStaffId());
                            detailVo.setTaskStatus(3);
                            detailVo.setTaskErrorMessage("无模板数据 Not found the template data");
                            scoreTask.setStatus(3);
                            // 更新任务状态
                            handleTaskStatus(scoreTask, detailVo, operator);
                            i--;
                            continue;
                        } else if ((templateVo.getStaffType() == 1 && (detailVo.getStaffType() == 2 || detailVo.getStaffType() == 4))
                                || (templateVo.getStaffType() == 2 && (detailVo.getStaffType() == 1 || detailVo.getStaffType() == 3))
                                || (templateVo.getStaffType() == 3 && (detailVo.getStaffType() == 2 || detailVo.getStaffType() == 4))
                                || (templateVo.getStaffType() == 4 && (detailVo.getStaffType() == 1 || detailVo.getStaffType() == 3))) {
                            log.info("handeHrsfPmsUserScoreTask staff bc_wc is shifted, staffId:{}", detailVo.getStaffId());
                            if(StringUtils.isEmpty(detailVo.getTaskErrorMessage())){
                                detailVo.setTaskStatus(2);
                            }
                            detailVo.setTaskErrorMessage("蓝白领身份发生转换 | Staff bc_wc is shifted");
                            // 更新任务状态
                            handleTaskStatus(scoreTask, detailVo, operator);
                            i--;
                            continue;
                        }

                        // 默认正常状态
                        scoreTask.setStatus(2);
                        detailVo.setTaskStatus(2);
                        // 提交任务
                        userScoreDuplicateTaskExecutor.execute(() -> {
                            // 处理并上传考核汇总报表
                            writePerformanceToSf(scoreTask, detailVo, templateVo);
                            // 更新任务状态
                            handleTaskStatus(scoreTask, detailVo, operator);
                        });
                    } catch (RejectedExecutionException e) {
                        try {
                            log.info("handeHrsfPmsUserScoreTask sleeping, taskId:{}", taskId);
                            Thread.sleep(2000L);
                            continue;
                        } catch (InterruptedException ex) {
                            log.warn("handeHrsfPmsUserScoreTask sleep failed, unhandled size: {}", scoreTemplateVos.size());
                        }
                    } catch (RuntimeException e) {
                        log.warn("handeHrsfPmsUserScoreTask error, taskId:{}", taskId, e);
                    }
                    i--;
                }
            }
        } catch (RuntimeException e) {
            log.warn("handleHrsfPmsDuplicatePeriodTask failed, scoreTaskId:{}", scoreTask.getId(), e);
        }
    }

    /**
     *
     * 写入考核数据到SF
     * @param scoreTask 任务
     * @param detailVo 任务明细
     * @param templateVo 考核数据
     * @return true-异常，false-正常
     */
    private void writePerformanceToSf(HrsfPmsUserScoreTask scoreTask, HrsfPmsUserScoreTaskDetailVo detailVo,
                                      HrsfPmsUserScoreTemplateVo templateVo) {
        try{
            String formContentId = null;
            String previousFormDataId = templateVo.getFormId();
            // 获取ContentID
            try{
                formContentId = sfApiManager.getFormContentId(detailVo.getStaffId(), detailVo.getFormDataId());
                if(StringUtils.isEmpty(formContentId)){
                    log.warn("SF formContentId is empty, staffId:{}", detailVo.getStaffId());
                    detailVo.setTaskStatus(3);
                    detailVo.setTaskErrorMessage("formContentId为空 | formContentId is empty, formDataId:" + templateVo.getFormId());
                    scoreTask.setStatus(3);
                    return;
                }
                templateVo.setFormContentId(formContentId);
                templateVo.setFormId(detailVo.getFormDataId());
            }catch (RuntimeException e){
                log.warn("SF formContentId get failed, staffId:{}", detailVo.getStaffId(), e);
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage("调用SF接口获取'formContentId'失败 | Get SF formContentId Failed");
                scoreTask.setStatus(3);
                return;
            }
            // 获取当前负责人
            String directManagerId = null;
            try {
                directManagerId = sfApiManager.getDirectManagerId(detailVo.getStaffId(), detailVo.getFormDataId());
            } catch (Exception e) {
                log.warn("SF directManagerId get failed, staffId:{}", detailVo.getStaffId(), e);
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage(e.getMessage());
                scoreTask.setStatus(3);
                return;
            }
            // 写入小分
            String reqBody = null;
            try {
                reqBody = this.formatItemScoreBody(detailVo, templateVo);
                sfApiManager.upsertItemScore(detailVo.getStaffId(), previousFormDataId, directManagerId, reqBody);
            } catch (Exception e) {
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage(e.getMessage());
                scoreTask.setStatus(3);
            }
            // 写入总分
            try {
                sfApiManager.upsertFinalScore(detailVo.getStaffId(), previousFormDataId, directManagerId, templateVo);
            } catch (Exception e) {
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage(e.getMessage());
                scoreTask.setStatus(3);
            }
            // 写入绩效等级
            try {
                sfApiManager.upsertPerformanceLevel(detailVo.getStaffId(), previousFormDataId, directManagerId, templateVo);
            } catch (Exception e) {
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage(e.getMessage());
                scoreTask.setStatus(3);
            }
            // 获取前周期formContentId
            String previousFormContentId = sfApiManager.getFormContentId(detailVo.getStaffId(), previousFormDataId);
            if(previousFormContentId != null){
                try {
                    // 获取业绩得分
                    BigDecimal ratingScore = sfApiManager.getRatingScore(previousFormContentId, previousFormDataId);
                    // 写入业绩得分
                    templateVo.setRatingScore(ratingScore);
                    templateVo.setRatingScoreItemId(sfYearScoreItemId);
                    reqBody = sfApiManager.processTemplate(SFApiManager.UPSERT_RATING_SCORE_BODY_TEMPLATE, templateVo);
                    sfApiManager.upsertRatingScore(detailVo.getStaffId(), previousFormDataId, directManagerId, reqBody);
                } catch (Exception e) {
                    detailVo.setTaskStatus(3);
                    detailVo.setTaskErrorMessage(e.getMessage());
                    scoreTask.setStatus(3);
                }
                // 判断白领年度
                if("YEAR".equals(templateVo.getAssessType()) && (detailVo.getStaffType() == 1 || detailVo.getStaffType() == 3)){
                    try {
                        // 获取潜能等级和可变动性
                        Map<String, String> potentialMap = sfApiManager.getPotentialMap(detailVo.getStaffId(), previousFormDataId, previousFormContentId);
                        templateVo.setPotentialCategory(potentialMap.get("ele_1"));
                        templateVo.setAvailability(potentialMap.get("ele_2"));
                        templateVo.setAvailableFromYear(potentialMap.get("ele_3"));
                        templateVo.setAvailableFromMonth(potentialMap.get("ele_4"));
                        // 写入潜能等级和可变动性
                        sfApiManager.upsertPotentialLevel(detailVo.getStaffId(), previousFormDataId, directManagerId, templateVo);
                    }catch (RuntimeException e){
                        detailVo.setTaskStatus(3);
                        detailVo.setTaskErrorMessage(e.getMessage());
                        scoreTask.setStatus(3);
                    }
                    try {
                        // 获取主要成就
                        String majorAccomplishments = sfApiManager.getMajorAccomplishments(detailVo.getStaffId(), previousFormDataId, previousFormContentId);
                        templateVo.setAnnualMajorAccomplishments(majorAccomplishments);
                        // 写入主要成就
                        sfApiManager.upsertMajorAccomplishments(detailVo.getStaffId(), previousFormDataId, directManagerId, templateVo);
                    } catch (Exception e) {
                        detailVo.setTaskStatus(3);
                        detailVo.setTaskErrorMessage(e.getMessage());
                        scoreTask.setStatus(3);
                    }
                }
                if(!"YEAR".equals(templateVo.getAssessType())) {
                    try {
                        // 获取轻微违规违纪扣减说明
                        String regulationDeductedRemark = sfApiManager.getRegulationDeductedRemark(detailVo.getStaffId(), previousFormDataId, previousFormContentId);
                        templateVo.setRegulationDeductedRemark(regulationDeductedRemark);
                        // 写入轻微违规违纪扣减说明
                        sfApiManager.upsertDeductedRemark(detailVo.getStaffId(), previousFormDataId, directManagerId, templateVo);
                    } catch (Exception e) {
                        detailVo.setTaskStatus(3);
                        detailVo.setTaskErrorMessage(e.getMessage());
                        scoreTask.setStatus(3);
                    }
                }
            }

            log.info("handleHrsfPmsDuplicatePeriodTemplate handled, templateId:{}, detailId: {}, staffId: {}",
                    templateVo.getId(), detailVo.getId(), detailVo.getStaffId());
        } catch (RuntimeException e) {
            detailVo.setTaskStatus(3);
            detailVo.setTaskErrorMessage(e.getMessage());
            log.info("handleHrsfPmsDuplicatePeriodTemplate failed, detailId: {}, staffId: {}", detailVo.getId(), detailVo.getStaffId());
            scoreTask.setStatus(3);
        }
    }

    /**
     * 格式化小分指标请求body
     * @param detailVo detailVo
     * @param templateVo templateVo
     * @return body
     */
    private String formatItemScoreBody(HrsfPmsUserScoreTaskDetailVo detailVo, HrsfPmsUserScoreTemplateVo templateVo) {
        // 格式化字符串
        String reqBody = null;
        Map<String, Object> itemIdMap = new HashMap<>();
        BigDecimal rate = new BigDecimal(100);
        if(templateVo.getWorkQualityScore() != null){
            templateVo.setWorkQualityScore(templateVo.getWorkQualityScore().multiply(rate));
        }
        if(templateVo.getCostControlScore() != null){
            templateVo.setCostControlScore(templateVo.getCostControlScore().multiply(rate));
        }
        if(templateVo.getCompletionOfWorkScore() != null){
            templateVo.setCompletionOfWorkScore(templateVo.getCompletionOfWorkScore().multiply(rate));
        }
        if(templateVo.getProductionSafetyScore() != null){
            templateVo.setProductionSafetyScore(templateVo.getProductionSafetyScore().multiply(rate));
        }
        if(templateVo.getAttendanceScore() != null){
            templateVo.setAttendanceScore(templateVo.getAttendanceScore().multiply(rate));
        }
        if(templateVo.getCommunicationScore() != null){
            templateVo.setCommunicationScore(templateVo.getCommunicationScore().multiply(rate));
        }
        if(templateVo.getTeamWorkScore() != null){
            templateVo.setTeamWorkScore(templateVo.getTeamWorkScore().multiply(rate));
        }
        if(templateVo.getProfessionalScore() != null){
            templateVo.setProfessionalScore(templateVo.getProfessionalScore().multiply(rate));
        }
        if(templateVo.getResponsibilityScore() != null){
            templateVo.setResponsibilityScore(templateVo.getResponsibilityScore().multiply(rate));
        }
        if(templateVo.getDisciplineScore() != null){
            templateVo.setDisciplineScore(templateVo.getDisciplineScore().multiply(rate));
        }
        if("YEAR".equals(templateVo.getAssessType())){
            // 年度小分
            reqBody = sfApiManager.processTemplate(SFApiManager.UPSERT_YEAR_BODY_TEMPLATE, templateVo);
            itemIdMap.put("workQualityScoreItemId", sfYearScoreItemId2);
            itemIdMap.put("costControlScoreItemId", sfYearScoreItemId5);
            itemIdMap.put("completionOfWorkScoreItemId", sfYearScoreItemId1);
            itemIdMap.put("productionSafetyScoreItemId", sfYearScoreItemId3);
            itemIdMap.put("attendanceScoreItemId", sfYearScoreItemId4);
            itemIdMap.put("communicationScoreItemId", sfYearScoreItemId6);
            itemIdMap.put("teamWorkScoreItemId", sfYearScoreItemId7);
            itemIdMap.put("professionalScoreItemId", sfYearScoreItemId8);
            itemIdMap.put("responsibilityScoreItemId", sfYearScoreItemId9);
            itemIdMap.put("disciplineScoreItemId", sfYearScoreItemId10);
        }else{
            reqBody = sfApiManager.processTemplate(SFApiManager.UPSERT_QUARTER_BODY_TEMPLATE, templateVo);
            if(detailVo.getStaffType() == 1 || detailVo.getStaffType() == 3){
                // 白领季度小分
                itemIdMap.put("workQualityScoreItemId", sfWhiteQuarterScoreItemId1);
                itemIdMap.put("costControlScoreItemId", sfWhiteQuarterScoreItemId2);
                itemIdMap.put("completionOfWorkScoreItemId", sfWhiteQuarterScoreItemId3);
                itemIdMap.put("productionSafetyScoreItemId", sfWhiteQuarterScoreItemId4);
                itemIdMap.put("attendanceScoreItemId", sfWhiteQuarterScoreItemId5);
            }else{
                //蓝领季度小分
                itemIdMap.put("workQualityScoreItemId", sfBlueQuarterScoreItemId1);
                itemIdMap.put("costControlScoreItemId", sfBlueQuarterScoreItemId2);
                itemIdMap.put("completionOfWorkScoreItemId", sfBlueQuarterScoreItemId3);
                itemIdMap.put("productionSafetyScoreItemId", sfBlueQuarterScoreItemId4);
                itemIdMap.put("attendanceScoreItemId", sfBlueQuarterScoreItemId5);
            }
        }
        reqBody = sfApiManager.processTemplate(reqBody, itemIdMap);
        return reqBody;
    }

    /**
     *  任务状态处理
     * @param scoreTask 主任务
     * @param detailVo 任务明细
     * @param operator 操作人
     */
    private void handleTaskStatus(HrsfPmsUserScoreTask scoreTask, HrsfPmsUserScoreTaskDetailVo detailVo, String operator) {
        LocalDateTime now = LocalDateTime.now();
        // 更新task detail状态
        hrsfPmsUserScoreTaskMapper.updateStatus(detailVo, operator, now);
        // 结束任务处理
        if(detailVo.getId() >= scoreTask.getMaxDetailId()){
            int errorCount = hrsfPmsUserScoreTaskMapper.getScoreTaskDetailErrorCount(scoreTask.getId());
            if(errorCount > 0){
                scoreTask.setStatus(3);
            }else {
                scoreTask.setStatus(2);
            }
            scoreTask.setEndTime(now);
            scoreTask.setUpdateBy(operator);
            scoreTask.setUpdateTime(now);
            // 更新数据库状态并清空缓存
            hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
            redisTemplate.opsForValue().getAndDelete(CacheConstants.CURRENT_USER_SCORE_TASK_ID_DUP);
            log.info("handeHrsfPmsDuplicatePeriodTask completed. taskId:{}", scoreTask.getId());
        } else {
            // 延长任务过期时间
            redisTemplate.opsForValue().getAndExpire(CacheConstants.CURRENT_USER_SCORE_TASK_ID_DUP,
                    CacheConstants.CURRENT_USER_SCORE_TASK_ID_CACHE_TIME, TimeUnit.MINUTES);
        }
    }

    @Override
    public HrsfPmsUserScoreTask getCurrentTaskStatus() {
        return hrsfPmsUserScoreTaskMapper.selectOne(Wrappers.<HrsfPmsUserScoreTask>lambdaQuery()
                .eq(HrsfPmsUserScoreTask::getTaskType, 2)
                .orderByDesc(HrsfPmsUserScoreTask::getCreateTime)
                .last("FETCH FIRST 1 ROWS ONLY"));
    }

}
