package com.bbac.hrsf.admin.handle;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.handler.context.SheetWriteHandlerContext;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnumNew;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.DataValidation;
import org.apache.poi.ss.usermodel.DataValidationConstraint;
import org.apache.poi.ss.usermodel.DataValidationHelper;
import org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Arrays;
import java.util.List;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.handle.CustomSheetWriteHandler</li>
 * <li>CreateTime : 2022/04/12 17:17:41</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class CustomSheetWriteHandler implements SheetWriteHandler {
    private int num;

    public CustomSheetWriteHandler(int size) {
        this.num = size;

    }

    @Override
    public void afterSheetCreate(SheetWriteHandlerContext context) {
        if (num > 0) {
            List<Integer> colSelect = Arrays.asList(5, 6, 7, 8, 9);
            colSelect.stream().forEach(p -> {
                // 区间设置 第一列第一行和第二行的数据。由于第一行是头，所以第一、二行的数据实际上是第二三行
                CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 2000, p, p);
                DataValidationHelper helper = context.getWriteSheetHolder().getSheet().getDataValidationHelper();
                DataValidationConstraint constraint = helper.createExplicitListConstraint(
                        new String[]{"0", "0.1", "0.2", "0.3", "0.4", "0.5", "0.6", "0.7", "0.8", "0.9", "1.0", "1.1", "1.2", "1.3"});
                DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
                /**
                 dataValidation.setEmptyCellAllowed(true);
                 dataValidation.setShowPromptBox(true);
                 dataValidation.createPromptBox("提示", "只能选择下拉框里面的数据");
                 */
                dataValidation.setShowErrorBox(true);
                dataValidation.createErrorBox("错误提醒 Error Message", "数据格式有误，请选择下拉框内的值 This value doesn't match the data validation restrictions defined for this cell.");
                context.getWriteSheetHolder().getSheet().addValidationData(dataValidation);
            });
            CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(1, 2000, 10, 10);
            DataValidationHelper helper = context.getWriteSheetHolder().getSheet().getDataValidationHelper();
            DataValidationConstraint constraint = helper.createExplicitListConstraint(
                    new String[]{PmsLevelEnumNew.Insufficient.getDescription()
                            , PmsLevelEnumNew.Inconsistent.getDescription()
                            , PmsLevelEnumNew.Successful.getDescription(),
                            PmsLevelEnumNew.Excellent.getDescription(),
                            PmsLevelEnumNew.Outstanding.getDescription()});
            DataValidation dataValidation = helper.createValidation(constraint, cellRangeAddressList);
            dataValidation.setShowErrorBox(true);
            dataValidation.createErrorBox("错误提醒 Error Message", "数据格式有误，请选择下拉框内的值 This value doesn't match the data validation restrictions defined for this cell.");
            context.getWriteSheetHolder().getSheet().addValidationData(dataValidation);
        }
        //context.getWriteSheetHolder().getSheet().protectSheet("intelligence-password");
    }
}