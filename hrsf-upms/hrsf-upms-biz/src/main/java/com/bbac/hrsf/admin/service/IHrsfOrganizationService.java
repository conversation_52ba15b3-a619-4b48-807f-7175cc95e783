package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfOrganization;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
public interface IHrsfOrganizationService extends IService<HrsfOrganization> {

    HrsfOrganizationVO selectOneData();

    void syncOrganizationHandler();

    void syncFinalWriteDataBackErrorHandler();

    void syncPmsJobHandlerAgain();

    void retryErrorSendEmail();
}
