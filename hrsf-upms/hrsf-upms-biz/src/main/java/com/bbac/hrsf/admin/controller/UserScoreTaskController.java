package com.bbac.hrsf.admin.controller;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserScoreTaskDTO;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserScoreTaskQueryDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserScoreTask;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;
import com.bbac.hrsf.admin.service.IHrsfUserScoreDuplicateService;
import com.bbac.hrsf.admin.service.IHrsfUserScoreTaskService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.util.R;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025-01-08
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/userScoreTask")
@Api(value = "user", tags = "员工绩效考核变更模块")
public class UserScoreTaskController {

    private final IHrsfUserScoreTaskService hrsfUserScoreTaskService;
    private final IHrsfUserScoreDuplicateService hrsfUserScoreDuplicateService;

    /**
     * 启动历史绩效分数任务
     *
     * @return R
     */
    @GetMapping("/start")
    public R<String> start(@RequestParam(value = "taskId", required = false) Long taskId) {
        String message = hrsfUserScoreTaskService.startUserScoreTask(taskId);
        if (Objects.equals(message, CommonConstants.SUCCESS_FLAG)) {
            return R.ok();
        }
        return R.failed(message);
    }

    /**
     * 获取任务任务及时间
     *
     * @return R
     */
    @GetMapping("/currentStatus")
    public R<HrsfPmsUserScoreTaskDTO> currentStatus() {
        HrsfPmsUserScoreTask userScoreTask = hrsfUserScoreTaskService.getCurrentTaskStatus();
        HrsfPmsUserScoreTaskDTO hrsfPmsUserScoreTaskDTO = new HrsfPmsUserScoreTaskDTO();
        if(userScoreTask == null){
            return R.ok(hrsfPmsUserScoreTaskDTO);
        }
        hrsfPmsUserScoreTaskDTO.setId(userScoreTask.getId());
        hrsfPmsUserScoreTaskDTO.setStatus(userScoreTask.getStatus());
        hrsfPmsUserScoreTaskDTO.setStartTime(userScoreTask.getStartTime());
        return R.ok(hrsfPmsUserScoreTaskDTO);
    }

    /**
     * 查询失败的任务明细列表
     * @param taskId 任务ID
     * @return R
     */
    @GetMapping("/queryFailedDetailList")
    public R<List<HrsfPmsUserScoreTaskDetailVo>> queryFailedDetailList(@RequestParam(value = "taskId", required = false) String taskId) {
        List<HrsfPmsUserScoreTaskDetailVo> scoreTaskDetailVoList = hrsfUserScoreTaskService.queryFailedTaskDetailList(taskId);
        return R.ok(scoreTaskDetailVoList);
    }
    /**
     * 查询失败的任务明细列表
     * @param taskId 任务ID
     * @return R
     */
    @PostMapping("/queryFailedDetailList")
    public R<IPage<HrsfPmsUserScoreTaskDetailVo>> queryFailedDetailList(@RequestBody HrsfPmsUserScoreTaskQueryDTO queryDTO) {
        queryDTO.setStatus(3);
        IPage<HrsfPmsUserScoreTaskDetailVo> pageObj = hrsfUserScoreTaskService.queryTaskDetailList(queryDTO);
        return R.ok(pageObj);
    }

    /**
     * 复制上周期绩效
     *
     * @return R
     */
    @GetMapping("/duplicatePreviousPeriod")
    public R<String> duplicatePreviousPeriod(@RequestParam(value = "taskId", required = false) Long taskId) {
        String message = hrsfUserScoreDuplicateService.startUserScoreTask(taskId);
        if (Objects.equals(message, CommonConstants.SUCCESS_FLAG)) {
            return R.ok();
        }
        return R.failed(message);
    }
    /**
     * 获取任务任务及时间
     *
     * @return R
     */
    @GetMapping("/duplicatePreviousPeriod/currentStatus")
    public R<HrsfPmsUserScoreTaskDTO> duplicatePreviousPeriodCurrentStatus() {
        HrsfPmsUserScoreTask userScoreTask = hrsfUserScoreDuplicateService.getCurrentTaskStatus();
        HrsfPmsUserScoreTaskDTO hrsfPmsUserScoreTaskDTO = new HrsfPmsUserScoreTaskDTO();
        if(userScoreTask == null){
            return R.ok(hrsfPmsUserScoreTaskDTO);
        }
        hrsfPmsUserScoreTaskDTO.setId(userScoreTask.getId());
        hrsfPmsUserScoreTaskDTO.setStatus(userScoreTask.getStatus());
        hrsfPmsUserScoreTaskDTO.setStartTime(userScoreTask.getStartTime());
        return R.ok(hrsfPmsUserScoreTaskDTO);
    }

}
