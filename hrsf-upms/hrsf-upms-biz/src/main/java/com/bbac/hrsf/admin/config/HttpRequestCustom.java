package com.bbac.hrsf.admin.config;

import cn.hutool.http.HttpRequest;

import java.net.InetSocketAddress;
import java.net.Proxy;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.config.HttpRequestCustom</li>
 * <li>CreateTime : 2022/07/19 11:11:30</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class HttpRequestCustom extends HttpRequest {

    public HttpRequestCustom(String url) {
        super(url);
    }

    public HttpRequest setHttpProxy(String host, int port, boolean proxyEnable) {
        if (proxyEnable) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            return this.setProxy(proxy).timeout(50000);
        } else {
            return this;
        }

    }

    /**
     * 设置小的超时时间
     * 防止批量请求
     * 导致超时时间过长，影响前端页面展示
     * @param host
     * @param port
     * @param proxyEnable
     * @return
     */
    public HttpRequest setHttpProxyShortTime(String host, int port, boolean proxyEnable) {
        if (proxyEnable) {
            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
            return this.setProxy(proxy).timeout(3000);
        } else {
            return this;
        }

    }

}