package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCard;
import org.apache.ibatis.annotations.Param;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 实现功能：人才卡数据处理功能
 */
public interface IHrsfPmsTalentCardService extends IService<HrsfPmsTalentCard> {
    /**
     * 人才卡数据下载
     */
    void processJob(List<String> list, HttpServletResponse response);

    /**
     * 根据员工号查询人才卡信息
     * @param staffId
     * @return
     */
    HrsfPmsTalentCard selectTalentCardByStaffId(@Param("staffId") String staffId);
}
