package com.bbac.hrsf.admin.controller;

import com.bbac.hrsf.admin.service.EnterpriseWeChatService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.exception.HrsfDeniedException;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.annotation.Inner;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/enterpriseWeChat")
@Api(value = "enterpriseWeChat", tags = "企业微信接口")
public class EnterpriseWeChatController {

    private final EnterpriseWeChatService enterpriseWeChatService;

    /**
     * 删除表单ID
     *
     * @param userId
     * @return
     */
    @GetMapping("/performanceRating")
    public R<Object> performanceRating(@RequestParam(value = "userId") String userId) {
        try {
            return R.ok(enterpriseWeChatService.performanceRating(userId));
        } catch (HrsfDeniedException e) {
            return R.restResult(null, CommonConstants.NO_PERMISSION, "该用户没有权限查看数据,请联系管理员");
        }
    }

    /**
     * 获取用户ID
     *
     * @param
     * @return
     */
    @GetMapping("/getUserIdByCode")
    @Inner(value = false)
    public R<Object> getUserIdByCode(@RequestParam(value = "code") String code, @RequestParam(value = "state") String state) {
        return R.ok(enterpriseWeChatService.getUserIdByCode(code, state));
    }
}
