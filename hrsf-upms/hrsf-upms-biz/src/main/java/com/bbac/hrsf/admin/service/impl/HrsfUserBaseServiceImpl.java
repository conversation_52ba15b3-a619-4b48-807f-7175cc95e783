package com.bbac.hrsf.admin.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseFilterDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserBaseFilterDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserFilterDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelationInfo;
import com.bbac.hrsf.admin.api.entity.HrsfPmsWriteDataBackLog;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserSimpleVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.mapper.HrsfPmsuserBaseMapper;
import com.bbac.hrsf.admin.mapper.HrsfUserBaseMapper;
import com.bbac.hrsf.admin.mapper.SysUserRoleMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsUserRelationInfoService;
import com.bbac.hrsf.admin.service.IHrsfPmsWriteDataBackLogService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.enums.ChineseForeignTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.EmailStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.EmployeeTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.ErrorFlag;
import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import com.bbac.hrsf.common.core.util.DatetimeUtils;
import com.bbac.hrsf.performance.api.feign.RemoteEmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HrsfUserBaseServiceImpl extends ServiceImpl<HrsfUserBaseMapper, HrsfUserBase> implements IHrsfUserBaseService {
    @Value("${sf.sync.userInfo}")
    private String sfSyncUserInfo;

    @Value("${sf.sync.photo}")
    private String sfSyncPhoto;
    @Value("${sf.sync.username}")
    private String userName;
    @Value("${sf.sync.pwd}")
    private String pwd;

    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;

    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;

    @Value("${sf.sync.operation.email}")
    private String operationEmail;

    @Value("${sf.sync.deepLink}")
    private String deepLink;

    private final SysUserRoleMapper sysUserRoleMapper;

    private final IHrsfPmsUserRelationInfoService hrsfPmsUserRelationInfoService;

    private final IHrsfPmsWriteDataBackLogService writeDataBackLogService;

    private final RemoteEmailService remoteEmailService;

    private final HrsfUserBaseMapper hrsfUserBaseMapper;

    private final HrsfPmsuserBaseMapper hrsfPmsuserBaseMapper;


    @Override
    public IPage<HrsfUserVO> getUserPageInfo(Page page, HrsfUserBaseFilterDTO hrsfUserDTO) {
        Page pageObj = baseMapper.selectPage(page, buildQueryWrapper(hrsfUserDTO));
        List<HrsfUserVO> hrsfUserVOList = (List<HrsfUserVO>) pageObj.getRecords().stream().map(p ->
                HrsfUpmsConvert.INSTANCE.toHrsfUserVO((HrsfUserBase) p))
                .collect(Collectors.toList());
        pageObj.setRecords(hrsfUserVOList);
        return pageObj;
    }

    @Override
    public List<HrsfUserVO> getUserInfoList(HrsfUserFilterDTO hrsfUserDTO) {
        return baseMapper.selectList(buildQueryWrapperSpecial(hrsfUserDTO)).stream().map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
    }

    private Wrapper<HrsfUserBase> buildQueryWrapperSpecial(HrsfUserFilterDTO hrsfUserDTO) {
        LambdaQueryWrapper<HrsfUserBase> wrapper = Wrappers.lambdaQuery();
        /**
         * 这里特殊处理一下
         * 只获取括号中的内容
         */

        if (StrUtil.isNotBlank(hrsfUserDTO.getSystem())) {
            String system = hrsfUserDTO.getSystem();
            system = system.substring(system.lastIndexOf("("), system.lastIndexOf(")") + 1);
            if (StrUtil.isNotBlank(system)) {
                wrapper.likeLeft(HrsfUserBase::getSystem, system);
            }
        }

        if (StrUtil.isNotBlank(hrsfUserDTO.getDepartment())) {
            String department = hrsfUserDTO.getDepartment();
            department = department.substring(department.lastIndexOf("("), department.lastIndexOf(")") + 1);
            if (StrUtil.isNotBlank(department)) {
                wrapper.likeLeft(HrsfUserBase::getDepartment, department);
            }
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getUserLevel())) {
            wrapper.eq(HrsfUserBase::getUserLevel, hrsfUserDTO.getUserLevel());
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getFullName())) {
            wrapper.like(HrsfUserBase::getFullName, hrsfUserDTO.getFullName()).or().like(
                    HrsfUserBase::getUserName, hrsfUserDTO.getFullName()
            );
        }
        /**
         * 若筛选条件"助理”选择为否，抓取员工主数据中助理字段为空的员工；
         * 若筛选条件“助理”为是，抓取员工主数据中助理字段为Y的员工；
         */
        if (CommonConstants.FLAG_NO.equals(hrsfUserDTO.getAssistant())) {
            wrapper.isNull(HrsfUserBase::getAssistant);
        } else {
            wrapper.eq(HrsfUserBase::getAssistant, CommonConstants.FLAG_Y);
        }
        /**
         * 若跨级直属L2选择为是，仅抓取系统=XX，且部门字段为空的员工同时判断职级和助理信息，获取匹配的员工；
         * 若跨级直属L2选择为否，仅抓取系统=XX，且部门字段有值的员工同时判断职级和助理信息，获取匹配的员工；
         */
        if (CommonConstants.FLAG_Y.equals(hrsfUserDTO.getDirectlyUnder())) {
            wrapper.isNull(HrsfUserBase::getDepartment);
        } else {
            wrapper.isNotNull(HrsfUserBase::getDepartment);
        }

        wrapper.select(HrsfUserBase::getStaffId, HrsfUserBase::getUserName, HrsfUserBase::getFullName, HrsfUserBase::getSystem, HrsfUserBase::getDepartment
                , HrsfUserBase::getSection, HrsfUserBase::getUserGroup, HrsfUserBase::getUserLevel, HrsfUserBase::getUpdateTime, HrsfUserBase::getUserId);
        return wrapper;
    }

    /**
     * 根据员工姓名从主数据表中获取员工信息
     *
     * @param fullName
     * @return
     */
    @Override
    public List<HrsfUserVO> getUserBaseSelectName(String fullName) {
        if (StrUtil.isNotBlank(fullName)) {
            List<HrsfUserBase> hrsfUserBases = hrsfUserBaseMapper.selectByFullName(fullName);
            return hrsfUserBases.stream()
                    .map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
        }
        return new ArrayList<>();

    }

    @Override
    public List<HrsfUserSimpleVO> getAllSimpleInfo() {
        return hrsfUserBaseMapper.selectSimpleInfo();
    }

    @Override
    public List<HrsfUserVO> getUserInfoByStaffId(List<String> staffIdList) {
        Set<String> staffSet = staffIdList.stream().filter(data -> StrUtil.isNotBlank(data)).collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(staffSet)) {
            return new ArrayList<>();
        }
        return baseMapper.selectList(Wrappers.<HrsfUserBase>lambdaQuery().select(HrsfUserBase::getStaffId, HrsfUserBase::getUserId
                , HrsfUserBase::getStatus, HrsfUserBase::getUserName
                , HrsfUserBase::getFullName, HrsfUserBase::getSystem
                , HrsfUserBase::getDepartment, HrsfUserBase::getSection, HrsfUserBase::getUserGroup
                , HrsfUserBase::getPosition, HrsfUserBase::getJobTitle, HrsfUserBase::getUserLevel
                , HrsfUserBase::getPositionLevel).in(HrsfUserBase::getStaffId, staffSet))
                .stream().map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
    }

    @Override
    public Set<String> selectByRoleCode(Long roleCode) {
        return sysUserRoleMapper.selectByRoleCode(roleCode);
    }

    /**
     * 根据员工ID去绩效协调员的关系表中获取协调员的ID
     *
     * @param staffIdList
     * @return
     */
    @Override
    public Set<String> selectByStaffIdList(Set<String> staffIdList) {
        Set<HrsfPmsUserRelationInfo> relationInfoSet = hrsfPmsUserRelationInfoService.list(Wrappers.<HrsfPmsUserRelationInfo>lambdaQuery()
                .in(HrsfPmsUserRelationInfo::getStaffId, staffIdList)).stream().collect(Collectors.toSet());

        /**
         * 根据员工号分组,分组后将绩效员的ID用逗号拼接起来
         * 列入<1 : 2,3>,<2 : 4,5>
         */
        Map<String, List<String>> staffIdMap = relationInfoSet.stream().collect(Collectors.groupingBy(HrsfPmsUserRelationInfo::getStaffId,
                Collectors.mapping(HrsfPmsUserRelationInfo::getPmsStaffId, Collectors.toList())));

        /**
         * 判断这些人的绩效协调员是否一致，如果不一致则返回HR角色
         */
        ArrayList<String> dataList = new ArrayList<>();
        staffIdMap.values().stream().forEach(obj -> {
            String dataStr = obj.stream().sorted().collect(Collectors.joining(","));
            dataList.add(dataStr);
        });
        log.info("获取到的绩效协调员列表为:{}", JSONUtil.toJsonStr(dataList));
        AtomicBoolean consistentFlag = new AtomicBoolean(false);
        //绩效协调员的员工号,用逗号拼接
        Optional<String> optional = dataList.stream().findFirst();
        if (optional.isPresent()) {
            String pmsStaffIdJoin = optional.get();
            dataList.stream().forEach(data -> {
                if (!pmsStaffIdJoin.equals(data)) {
                    consistentFlag.set(true);
                    return;
                }
            });
        } else {
            consistentFlag.set(true);
        }
        /**
         * flag为true则表示绩效协调员里不一致
         */
        if (consistentFlag.get()) {
            return sysUserRoleMapper.selectByRoleCode(RoleTypeEnum.HR.getType());
        }
        return Arrays.asList(optional.get().split(",")).stream().collect(Collectors.toSet());


    }

    @Override
    public List<HrsfUserBase> selectEmailAddressByStaffIdList(Set<String> staffIdList) {
        if (CollUtil.isEmpty(staffIdList)) {
            return new ArrayList<>();
        }
        return list(Wrappers.<HrsfUserBase>lambdaQuery().select(HrsfUserBase::getEmail, HrsfUserBase::getStaffId)
                .in(HrsfUserBase::getStaffId, staffIdList));
    }


    private LambdaQueryWrapper buildQueryWrapperList(HrsfPmsUserBaseFilterDTO dto) {
        LambdaQueryWrapper<HrsfUserBase> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(dto.getSystem())) {
            if (CommonConstants.DEFAULT_NA.equals(dto.getSystem())) {
                wrapper.isNull(HrsfUserBase::getSystem);
            } else {
                wrapper.eq(HrsfUserBase::getSystem, dto.getSystem());
            }
        }
        if (StrUtil.isNotBlank(dto.getDepartment())) {
            if (CommonConstants.DEFAULT_NA.equals(dto.getDepartment())) {
                wrapper.isNull(HrsfUserBase::getDepartment);
            } else {
                wrapper.eq(HrsfUserBase::getDepartment, dto.getDepartment());
            }
        }
        if (StrUtil.isNotBlank(dto.getSection())) {
            if (CommonConstants.DEFAULT_NA.equals(dto.getSection())) {
                wrapper.isNull(HrsfUserBase::getSection);
            } else {
                wrapper.eq(HrsfUserBase::getSection, dto.getSection());
            }
        }
        if (StrUtil.isNotBlank(dto.getUserGroup())) {
            if (CommonConstants.DEFAULT_NA.equals(dto.getUserGroup())) {
                wrapper.isNull(HrsfUserBase::getUserGroup);
            } else {
                wrapper.eq(HrsfUserBase::getUserGroup, dto.getUserGroup());
            }
        }
        if (StrUtil.isNotBlank(dto.getUserLevel())) {
            wrapper.eq(HrsfUserBase::getUserLevel, dto.getUserLevel());
        }
        if (CollUtil.isNotEmpty(dto.getStaffIds())) {
            wrapper.in(HrsfUserBase::getStaffId, dto.getStaffIds());
        }
        if (StrUtil.isNotBlank(dto.getEmploymentType())) {
            if (ChineseForeignTypeEnum.Foreign.getType().equals(dto.getEmploymentType())) {
                wrapper.eq(HrsfUserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            } else {
                wrapper.ne(HrsfUserBase::getEmploymentType, EmployeeTypeEnum.J.getType());
            }
        }
        return wrapper;
    }

    private LambdaQueryWrapper buildQueryWrapper(HrsfUserBaseFilterDTO hrsfUserDTO) {
        LambdaQueryWrapper<HrsfUserBase> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(hrsfUserDTO.getSystem())) {
            if (CommonConstants.DEFAULT_NA.equals(hrsfUserDTO.getSystem())) {
                wrapper.isNull(HrsfUserBase::getSystem);
            } else {
                wrapper.eq(HrsfUserBase::getSystem, hrsfUserDTO.getSystem());
            }
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getDepartment())) {
            if (CommonConstants.DEFAULT_NA.equals(hrsfUserDTO.getDepartment())) {
                wrapper.isNull(HrsfUserBase::getDepartment);
            } else {
                wrapper.eq(HrsfUserBase::getDepartment, hrsfUserDTO.getDepartment());
            }
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getSection())) {
            if (CommonConstants.DEFAULT_NA.equals(hrsfUserDTO.getSection())) {
                wrapper.isNull(HrsfUserBase::getSection);
            } else {
                wrapper.eq(HrsfUserBase::getSection, hrsfUserDTO.getSection());
            }
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getUserGroup())) {
            if (CommonConstants.DEFAULT_NA.equals(hrsfUserDTO.getUserGroup())) {
                wrapper.isNull(HrsfUserBase::getUserGroup);
            } else {
                wrapper.eq(HrsfUserBase::getUserGroup, hrsfUserDTO.getUserGroup());
            }
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getUserLevel())) {
            wrapper.eq(HrsfUserBase::getUserLevel, hrsfUserDTO.getUserLevel());
        }
        if (StrUtil.isNotBlank(hrsfUserDTO.getFullName())) {
            wrapper.like(HrsfUserBase::getFullName, hrsfUserDTO.getFullName()).or().like(
                    HrsfUserBase::getUserName, hrsfUserDTO.getFullName()
            );
        }
        wrapper.select(HrsfUserBase::getStaffId, HrsfUserBase::getUserName, HrsfUserBase::getFullName, HrsfUserBase::getSystem, HrsfUserBase::getDepartment
                , HrsfUserBase::getSection, HrsfUserBase::getUserGroup, HrsfUserBase::getUserLevel, HrsfUserBase::getUpdateTime, HrsfUserBase::getUserId);
        return wrapper;
    }

    @Override
    public void syncAgain() {
        List<String> type = new ArrayList<>();
        type.add(EmailStatusEnum.ZERO.getType());
        type.add(EmailStatusEnum.ONE.getType());
        type.add(EmailStatusEnum.TWO.getType());
        List<HrsfPmsWriteDataBackLog> list = writeDataBackLogService.list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery().in(HrsfPmsWriteDataBackLog::getEmailStatus, type).eq(HrsfPmsWriteDataBackLog::getFlag, ErrorFlag.SYNC.getType()));

        List<HrsfPmsWriteDataBackLog> collect = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.TWO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            for (HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog : collect) {
                String errorInfo = hrsfPmsWriteDataBackLog.getErrorInfo();
                JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                long current = jsonObject.getLong("current");
                long size = jsonObject.getLong("size");
                sync(current, size, EmailStatusEnum.THREE.getType(), hrsfPmsWriteDataBackLog.getId());
            }
        }
        List<HrsfPmsWriteDataBackLog> collect1 = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ONE.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect1)) {
            for (HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog : collect1) {
                String errorInfo = hrsfPmsWriteDataBackLog.getErrorInfo();
                JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                long current = jsonObject.getLong("current");
                long size = jsonObject.getLong("size");
                sync(current, size, EmailStatusEnum.TWO.getType(), hrsfPmsWriteDataBackLog.getId());
            }
        }

        List<HrsfPmsWriteDataBackLog> collect2 = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ZERO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect2)) {
            for (HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog : collect2) {
                String errorInfo = hrsfPmsWriteDataBackLog.getErrorInfo();
                JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                long current = jsonObject.getLong("current");
                long size = jsonObject.getLong("size");
                sync(current, size, EmailStatusEnum.ONE.getType(), hrsfPmsWriteDataBackLog.getId());
            }
        }
    }

    @Override
    public void syncFormIdAgain() {
        List<String> type = new ArrayList<>();
        type.add(EmailStatusEnum.ZERO.getType());
        type.add(EmailStatusEnum.ONE.getType());
        type.add(EmailStatusEnum.TWO.getType());
        List<HrsfPmsWriteDataBackLog> list = writeDataBackLogService.list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery().in(HrsfPmsWriteDataBackLog::getEmailStatus, type).eq(HrsfPmsWriteDataBackLog::getFlag, ErrorFlag.syncFormId.getType()));

        List<HrsfPmsWriteDataBackLog> collect = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.TWO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            for (HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog : collect) {
                String errorInfo = hrsfPmsWriteDataBackLog.getErrorInfo();
                JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                String url = jsonObject.getStr("url");
                String assessYear = jsonObject.getStr("assessYear");
                String assessType = jsonObject.getStr("assessType");
                syncFormId(url, assessYear, assessType, EmailStatusEnum.THREE.getType(), hrsfPmsWriteDataBackLog.getId());
            }
        }
        List<HrsfPmsWriteDataBackLog> collect1 = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ONE.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect1)) {
            for (HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog : collect1) {
                String errorInfo = hrsfPmsWriteDataBackLog.getErrorInfo();
                JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                String url = jsonObject.getStr("url");
                String assessYear = jsonObject.getStr("assessYear");
                String assessType = jsonObject.getStr("assessType");
                syncFormId(url, assessYear, assessType, EmailStatusEnum.TWO.getType(), hrsfPmsWriteDataBackLog.getId());
            }
        }

        List<HrsfPmsWriteDataBackLog> collect2 = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ZERO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect2)) {
            for (HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog : collect2) {
                String errorInfo = hrsfPmsWriteDataBackLog.getErrorInfo();
                JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                String url = jsonObject.getStr("url");
                String assessYear = jsonObject.getStr("assessYear");
                String assessType = jsonObject.getStr("assessType");
                syncFormId(url, assessYear, assessType, EmailStatusEnum.ONE.getType(), hrsfPmsWriteDataBackLog.getId());
            }
        }
    }

    @Override
    public List<HrsfUserVO> getUserBaseSelectByStaffId(String staffId) {
        if (StrUtil.isNotBlank(staffId)) {
            List<HrsfUserBase> hrsfUserBases = hrsfUserBaseMapper.selectByStaffId(staffId);
            return hrsfUserBases.stream()
                    .map(p -> HrsfUpmsConvert.INSTANCE.toHrsfUserVO(p)).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public IPage<HrsfUserVO> getUserPage(HrsfPmsUserBaseFilterDTO hrsfPmsUserBaseDTO) {
        Page pageObj = baseMapper.selectPage(hrsfPmsUserBaseDTO, buildQueryWrapperList(hrsfPmsUserBaseDTO));
        List<HrsfUserVO> hrsfUserVOList = (List<HrsfUserVO>) pageObj.getRecords().stream().map(p ->
                HrsfUpmsConvert.INSTANCE.toHrsfUserVO((HrsfUserBase) p))
                .collect(Collectors.toList());
        pageObj.setRecords(hrsfUserVOList);
        return pageObj;
    }

    @Override
    public HrsfOrganizationVO getCalibrationPmsSelect() {
        //分别去员工基础表中查询相应的系统、部门、科室、群组
        return new HrsfOrganizationVO(
                list(Wrappers.<HrsfUserBase>query().select("DISTINCT SYSTEM").lambda()
                        .isNotNull(HrsfUserBase::getSystem).orderByDesc(HrsfUserBase::getSystem)).stream().map(HrsfUserBase::getSystem).collect(Collectors.toList()),
                list(Wrappers.<HrsfUserBase>query().select("DISTINCT DEPARTMENT").lambda()
                        .isNotNull(HrsfUserBase::getDepartment).orderByDesc(HrsfUserBase::getDepartment)).stream().map(HrsfUserBase::getDepartment).collect(Collectors.toList()),
                list(Wrappers.<HrsfUserBase>query().select("DISTINCT SECTION").lambda()
                        .isNotNull(HrsfUserBase::getSection).orderByDesc(HrsfUserBase::getSection)).stream().map(HrsfUserBase::getSection).collect(Collectors.toList()),
                list(Wrappers.<HrsfUserBase>query().select("DISTINCT USER_GROUP").lambda()
                        .isNotNull(HrsfUserBase::getUserGroup).orderByDesc(HrsfUserBase::getUserGroup)).stream().map(HrsfUserBase::getUserGroup).collect(Collectors.toList()),
                list(Wrappers.<HrsfUserBase>query().select("DISTINCT USER_LEVEL").lambda()
                        .isNotNull(HrsfUserBase::getUserLevel).orderByDesc(HrsfUserBase::getUserLevel)).stream().map(HrsfUserBase::getUserLevel).collect(Collectors.toList()));

    }


    /**
     * 同步员工信息
     * 返回false表示,返回数据为空,不需要继续执行下去
     *
     * @return
     */
    @Override
    public Boolean sync(long current, long size, String i, Long id) {
        try {
            log.info("同步员工数据sfSyncUserInfo:{}", sfSyncUserInfo);
            String format = StrUtil.format(sfSyncUserInfo, current, size);
            log.info("同步员工数据url:{}", format);
            String body;
            try {
                body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            } catch (HttpException e) {
                log.error("同步员工数据失败自动重试一次");
                body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            }
            JSONObject jsonObject = JSONUtil.parseObj(body);
            log.error("同步员工数据url:{}", body);
            JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
            log.info("本次同步员工数据条数:{}", jsonArray.size());
            List<HrsfUserBase> userBaseList = new ArrayList<>();
            if (jsonArray.size() > 0) {
                userBaseList = jsonArray.stream().map(obj ->
                        HrsfUpmsConvert.INSTANCE.toHrsfUserBase((JSONObject) obj)).collect(Collectors.toList());
            }
            if (CollectionUtil.isNotEmpty(userBaseList)) {
                saveOrUpdateBatch(userBaseList);
            }
            /**
             * 判断id是否为空，如果不为空表示是重试操作
             * 重试成功 修改状态
             */
            if (Objects.nonNull(id)) {
                Map<String, Long> map = new HashMap<>();
                map.put("current", current);
                map.put("size", size);
                HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
                hrsfPmsWriteDataBackLog.setEmailStatus(EmailStatusEnum.SUCCESS.getType());
                hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.SYNC.getType());
                hrsfPmsWriteDataBackLog.setErrorInfo(JSONUtil.toJsonStr(map));
                hrsfPmsWriteDataBackLog.setId(id);
                hrsfPmsWriteDataBackLog.setName("同步员工信息:" + current + "至" + size);
                writeDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
            }
            return jsonArray.size() > 0;
        } catch (Exception e) {
            log.error("同步员工数据失败:{}", e);
            Map<String, Long> map = new HashMap<>();
            map.put("current", current);
            map.put("size", size);
            HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
            hrsfPmsWriteDataBackLog.setEmailStatus(i);
            hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.SYNC.getType());
            hrsfPmsWriteDataBackLog.setErrorInfo(JSONUtil.toJsonStr(map));
            hrsfPmsWriteDataBackLog.setId(id);
            hrsfPmsWriteDataBackLog.setName("同步员工信息:" + current + "至" + size);
            writeDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
            return false;
        }
    }

    /**
     * 同步表单ID流程
     * 返回false表示,返回数据为空,不需要继续执行下去
     *
     * @return
     */
    public Boolean syncFormId(String url, String assessYear, String assessType, String i, Long id) {
        try {
            ArrayList<HrsfPmsuserBase> pmsUserBaseList = new ArrayList<>();
            log.info("重新获取表单ID、Content ID的url:{}", url);
            String body = new HttpRequestCustom(url).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                    .basicAuth(userName, pwd).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
            Map<String, Object> dataMap = jsonArray.parallelStream().collect(Collectors.groupingBy(obj -> ((JSONObject) obj).getStr("formSubjectId")
                    , Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> {
                        String creationDate1 = ((JSONObject) c1).getStr("creationDate");
                        String creationDate2 = ((JSONObject) c2).getStr("creationDate");
                        return DatetimeUtils.DateStrToLong(creationDate1) > DatetimeUtils.DateStrToLong(creationDate2) ? c1 : c2;
                    }), Optional::get)));
            dataMap.values().stream().forEach(obj -> {
                //员工ID
                String formSubjectId = ((JSONObject) obj).getStr("formSubjectId");
                String formDataId = ((JSONObject) obj).getStr("formDataId");
                //更新员工deepLink、表单ID、Content ID到数据表中
                pmsUserBaseList.add(new HrsfPmsuserBase(assessYear, assessType, formDataId, formSubjectId, StrUtil.format(deepLink, formSubjectId)));
            });
            if (CollectionUtil.isNotEmpty(pmsUserBaseList)) {
                hrsfPmsuserBaseMapper.updateFormIdBatchById(pmsUserBaseList);
            }
            //重试成功 修改状态
            if (Objects.nonNull(id)) {
                HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
                hrsfPmsWriteDataBackLog.setEmailStatus(EmailStatusEnum.SUCCESS.getType());
                hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.syncFormId.getType());
                hrsfPmsWriteDataBackLog.setId(id);
                writeDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
            }
        } catch (Exception e) {
            log.error("同步FormId数据失败:{}", e);
            HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
            hrsfPmsWriteDataBackLog.setEmailStatus(i);
            hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.syncFormId.getType());
            hrsfPmsWriteDataBackLog.setId(id);
            writeDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
        }
        return true;
    }

    /**
     * 同步员工头像信息
     *
     * @return
     */
    @Override
    public Boolean syncPhoto(long current, long size) {
        List<HrsfUserBase> userBaseList = new ArrayList<>();
        Page page = new Page(current, size);
        Page<HrsfUserBase> pageObj = baseMapper.selectPage(page,
                Wrappers.<HrsfUserBase>lambdaQuery().select(HrsfUserBase::getStaffId));
        pageObj.getRecords().stream().forEach(data -> {
            String staffId = data.getStaffId();
            String photoStr = StrUtil.format(sfSyncPhoto, staffId);
            try {
                String body = new HttpRequestCustom(photoStr).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
                JSONObject jsonObject = JSONUtil.parseObj(body);
                JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
                if (jsonArray.size() > 0) {
                    String photo = ((JSONObject) jsonArray.get(0)).getStr("photo");
                    userBaseList.add(new HrsfUserBase(staffId, photo));
                }
            } catch (HttpException e) {
                log.error("同步头像超时异常", e.getMessage());
            } catch (Exception e) {
                log.error("同步员工头像数据异常:{}", e);
            }
        });
        try {
            if (CollectionUtil.isNotEmpty(userBaseList)) {
                getBaseMapper().updatePhotoBatchById(userBaseList);
            }
        } catch (Exception e) {
            log.error("更新员工头像数据异常:{}", e);
        }
        log.info("同步员工头像数据成功{}次,{}", current, pageObj.hasNext());

        return pageObj.hasNext();
    }
}
