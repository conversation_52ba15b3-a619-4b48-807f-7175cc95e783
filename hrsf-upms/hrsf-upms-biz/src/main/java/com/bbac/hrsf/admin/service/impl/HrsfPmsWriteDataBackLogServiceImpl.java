package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.entity.HrsfPmsWriteDataBackLog;
import com.bbac.hrsf.admin.mapper.HrsfPmsWriteDataBackLogMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsWriteDataBackLogService;
import com.bbac.hrsf.common.core.constant.enums.EmailStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.ErrorFlag;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: liu, jie
 * @create: 2022-06-08
 **/
@Service
@AllArgsConstructor
public class HrsfPmsWriteDataBackLogServiceImpl extends ServiceImpl<HrsfPmsWriteDataBackLogMapper, HrsfPmsWriteDataBackLog> implements IHrsfPmsWriteDataBackLogService {
    @Override
    public boolean selectErrorInfoByTaskIds(List<String> taskId) {
        if (CollUtil.isNotEmpty(taskId)) {
            List<HrsfPmsWriteDataBackLog> list = list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery().in(HrsfPmsWriteDataBackLog::getTaskId, taskId));
            if (CollUtil.isNotEmpty(list)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean selectErrorInfoByEmailStatus(String type) {
        if (StringUtils.isNotBlank(type)) {
            List<HrsfPmsWriteDataBackLog> list = list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery().eq(HrsfPmsWriteDataBackLog::getEmailStatus, type));
            if (CollUtil.isNotEmpty(list)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<HrsfPmsWriteDataBackLog> selectByEmailStatus() {
        List<String> type = new ArrayList<>();
        type.add(EmailStatusEnum.ZERO.getType());
        type.add(EmailStatusEnum.ONE.getType());
        type.add(EmailStatusEnum.TWO.getType());

        List<String> flag = new ArrayList<>();
        flag.add(ErrorFlag.writeDataBack.getType());
        flag.add(ErrorFlag.whiteYearWriteDataBack.getType());
        flag.add(ErrorFlag.whiteYearFinalWriteDataBack.getType());
        flag.add(ErrorFlag.finalWriteDataBack.getType());

        return list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery().select(HrsfPmsWriteDataBackLog::getEmailStatus,
                HrsfPmsWriteDataBackLog::getId,HrsfPmsWriteDataBackLog::getErrorInfo,HrsfPmsWriteDataBackLog::getFlag).in(HrsfPmsWriteDataBackLog::getEmailStatus, type).in(HrsfPmsWriteDataBackLog::getFlag, flag));

    }

    @Override
    public boolean updateEmailStatus(String type, String type2) {
        return lambdaUpdate()
                .set(HrsfPmsWriteDataBackLog::getEmailStatus, type)
                .eq(HrsfPmsWriteDataBackLog::getEmailStatus, type2)
                .update();
    }

    @Override
    public boolean updateEmailStatusById(String type, List<Long> idList) {
        return lambdaUpdate()
                .set(HrsfPmsWriteDataBackLog::getEmailStatus, type)
                .in(HrsfPmsWriteDataBackLog::getId, idList)
                .update();
    }

    @Override
    public void updateEmailStatus2(String type, String type1, String type2) {
        lambdaUpdate()
                .set(HrsfPmsWriteDataBackLog::getEmailStatus, type)
                .eq(HrsfPmsWriteDataBackLog::getEmailStatus, type1)
                .eq(HrsfPmsWriteDataBackLog::getFlag, type2)
                .update();
    }
}
