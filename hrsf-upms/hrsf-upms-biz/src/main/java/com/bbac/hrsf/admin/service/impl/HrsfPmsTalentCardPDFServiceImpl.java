package com.bbac.hrsf.admin.service.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCard;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardCourseRecord;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardKeyProject;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardPDF;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserCompetency;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfUserSimpleVO;
import com.bbac.hrsf.admin.mapper.HrsfPmsTalentCardMapper;
import com.bbac.hrsf.admin.mapper.HrsfPmsTalentCardPDFMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardCourseRecordService;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardKeyProjectService;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardPDFService;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardService;
import com.bbac.hrsf.admin.service.IHrsfPmsUserCompetencyService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.openqa.selenium.By;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.Point;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 人才卡下载相关服务
 */
@Slf4j
@Service
public class HrsfPmsTalentCardPDFServiceImpl extends ServiceImpl<HrsfPmsTalentCardPDFMapper, HrsfPmsTalentCardPDF> implements IHrsfPmsTalentCardPDFService {

    @Autowired
    private IHrsfUserBaseService hrsfUserBaseService;

    @Autowired
    private IHrsfPmsTalentCardService hrsfPmsTalentCardService;

    @Autowired
    private IHrsfPmsTalentCardCourseRecordService hrsfPmsTalentCardCourseRecordService;
    @Autowired
    private IHrsfPmsTalentCardKeyProjectService hrsfPmsTalentCardKeyProjectService;
    @Lazy//防止可能的循环引用错误
    @Autowired
    private IHrsfPmsUserCompetencyService hrsfPmsUserCompetencyService;
    @Value("${chrome.driver.config}")
    private String chromeDriverConfig;

    //已测试
    @Override
    public void download(List<String> list, HttpServletResponse response) {
        /**
         * 先删除在生成
         * 下载的时候在生成PDF
         */

        list.stream().forEach(obj -> {
            try {
                getBaseMapper().deleteInfoById(obj);
                createPdf(obj);
            } catch (Exception exception) {
                if (exception instanceof InterruptedException) {
                    Thread.currentThread().interrupt();
                }
                log.error(exception.getMessage(), exception);
            }
        });
        if (CollectionUtils.isEmpty(list)) {
            log.info("入参工号集合为空了");
            return;
        }
        OutputStream outputStream = null;
        ZipOutputStream zipOutputStream = null;
        try {
            //已经预处理好存储到数据库中了，直接查询下载即可
            List<HrsfPmsTalentCardPDF> pdfs = this.list(Wrappers.<HrsfPmsTalentCardPDF>lambdaQuery().in(HrsfPmsTalentCardPDF::getStaffId, list));
            outputStream = response.getOutputStream();
            zipOutputStream = new ZipOutputStream(outputStream);
            Set<String> nameSet = Sets.newHashSet();
            for (HrsfPmsTalentCardPDF pdf : pdfs) {
                String name = pdf.getPdfName();
                for (int i = 2; i < Integer.MAX_VALUE; i++) {
                    if (nameSet.contains(name)) {
                        name = i + name;
                    } else {
                        nameSet.add(name);
                        break;
                    }
                }
                zipOutputStream.putNextEntry(new ZipEntry(name));//方法名不可重复
                zipOutputStream.write(pdf.getPdfDATA());
                zipOutputStream.closeEntry();
            }
            zipOutputStream.close();
            response.setContentType("application/x-download");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(UUID.randomUUID().toString().replace("-", ""), "UTF-8"));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            IOUtils.closeQuietly(outputStream);
            IOUtils.closeQuietly(zipOutputStream);
        }
    }

    @Override
    public void createPdf() {
        getBaseMapper().truncateCardPdf();
        for (HrsfUserSimpleVO hrsfUserSimpleVO : hrsfUserBaseService.getAllSimpleInfo()) {
            try {
                createPdf(hrsfUserSimpleVO.getStaffId());
            } catch (Exception exception) {
                if (exception instanceof InterruptedException) {
                    Thread.currentThread().interrupt();
                }
                log.error(exception.getMessage(), exception);
            }
        }
    }

    //test
    public static void main(String[] args) throws Exception {
        //new HrsfPmsTalentCardPDFServiceImpl().createPdf("111");
        //调用浏览器截图并生成pdf,返回pdf文件内容
        String path1 = "C:\\work\\projects\\bbac\\test\\1.html";
        new HrsfPmsTalentCardPDFServiceImpl().screenshot(path1);
    }

//test  CommandLineRunner.run
//    @Override
//    public void run(String... args) throws Exception {
//        createPdf("SD30634");
//    }

    @Override
    public void createPdf(String staffId) throws IOException, DocumentException, InterruptedException {
        //数据抓取
        HrsfUserBase hrsfUserBase = hrsfUserBaseService.getOne(Wrappers.<HrsfUserBase>lambdaQuery().eq(HrsfUserBase::getStaffId, staffId));
        List<HrsfPmsUserCompetency> hrsfPmsUserCompetencies = hrsfPmsUserCompetencyService.list(Wrappers.<HrsfPmsUserCompetency>lambdaQuery().eq(HrsfPmsUserCompetency::getStaffId, staffId));
        HrsfPmsTalentCard talentCard = hrsfPmsTalentCardService.selectTalentCardByStaffId(staffId);//hrsfPmsTalentCardService.getOne(Wrappers.<HrsfPmsTalentCard>lambdaQuery().eq(HrsfPmsTalentCard::getStaffId, staffId));
        List<HrsfPmsTalentCardCourseRecord> courseRecordList = hrsfPmsTalentCardCourseRecordService.list(Wrappers.<HrsfPmsTalentCardCourseRecord>lambdaQuery().eq(HrsfPmsTalentCardCourseRecord::getStaffId, staffId));
        List<HrsfPmsTalentCardKeyProject> keyProjectList = hrsfPmsTalentCardKeyProjectService.list(Wrappers.<HrsfPmsTalentCardKeyProject>lambdaQuery().eq(HrsfPmsTalentCardKeyProject::getStaffId, staffId));

        //构建html模板需要的数据
        //基础数据
        String basicInformation = getBasicInformation(hrsfUserBase);
        //职位信息
        String positionInformation = getPositionInformation(hrsfUserBase);
        //能力数据 雷达图
        String cerDataArr = getCerDataArr(hrsfPmsUserCompetencies);
        //能力得分 总分
        String competenciesScore = getCompetenciesScore(talentCard);
        //能力条目数据 条形图数据
        String abilityToEntryDate = getAbilityToEntryDate(hrsfPmsUserCompetencies);
        //过往人才盘点结果（九宫格数据）
        String matrix1LabelNum = getMatrix1Label(talentCard);
        String matrix1LabelName = getMatrix1LabelName(talentCard);
        //评价中心数据 列表
        String assessmentCenterEvaluationResultData = getAssessmentCenterEvaluationResultData(talentCard);
        //参与的重点项目 列表
        String keyProjectsInvolvedData = getKeyProjectsInvolvedData(keyProjectList);
        //培训记录 列表
        String courseRecordPositionData = getCourseRecordPositionData(courseRecordList);
        //html模板数据加载 由于模板较大，已手工切分成2部分，part2需要填充数据
        String templatePart2 = readResource2String("templates/talentCard_PDF_templete_part2.html");
        templatePart2 = templatePart2.replace("###basicInformation###", basicInformation);
        templatePart2 = templatePart2.replace("###positionInformation###", positionInformation);
        templatePart2 = templatePart2.replace("###cerDataArr###", cerDataArr);
        templatePart2 = templatePart2.replace("###competenciesScore###", competenciesScore);
        templatePart2 = templatePart2.replace("###abilityToEntryDate###", abilityToEntryDate);
        templatePart2 = templatePart2.replace("###matrix1Label###", matrix1LabelNum);
        templatePart2 = templatePart2.replace("###matrix1LabelName###", matrix1LabelName);
        templatePart2 = templatePart2.replace("###assessmentCenterEvaluationResultData###", assessmentCenterEvaluationResultData);
        templatePart2 = templatePart2.replace("###keyProjectsInvolvedData###", keyProjectsInvolvedData);
        templatePart2 = templatePart2.replace("###courseRecordPositionData###", courseRecordPositionData);
        //模板合并,并生成临时文件
        File file = null;
        try {
            file = mergeTemplate(staffId, templatePart2, "templates/talentCard_PDF_templete_part1.html");
            if (log.isDebugEnabled()) {
                File tmpFile = new File(file.getAbsolutePath() + staffId + "-" + System.currentTimeMillis() + ".html");
                FileUtils.copyFile(file, tmpFile);
                log.debug("模板填充数据后的文件另存为: ", tmpFile.getAbsolutePath());
            }
            //调用浏览器截图并生成pdf,返回pdf文件内容
            byte[] pdfData = screenshot(file.getAbsolutePath());
            //存储pdf
            HrsfPmsTalentCardPDF hrsfPmsTalentCardPDF = new HrsfPmsTalentCardPDF();
            hrsfPmsTalentCardPDF.setStaffId(staffId);
            hrsfPmsTalentCardPDF.setPdfDATA(pdfData);
            hrsfPmsTalentCardPDF.setPdfName(staffId + ".pdf");
            hrsfPmsTalentCardPDF.setDelFlag("0");
            hrsfPmsTalentCardPDF.setCreateTime(LocalDateTime.now());

            this.save(hrsfPmsTalentCardPDF);
            log.info("staffId: {} pdf文件生成成功.", staffId);
        } finally {
            //清理临时目录
            /**
             * 为了测试先将此代码注释掉
             * 后面上线将代码还原
             */
            try {
                if (file != null) {
                    for (File f : file.getParentFile().listFiles()) {
                        Files.deleteIfExists(f.toPath());
                    }
                    Files.deleteIfExists(file.getParentFile().toPath());
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }

    }

    private String getBasicInformation(HrsfUserBase hrsfUserBase) {
        int idx = hrsfUserBase.getFullName().indexOf(" ");//按第一个空格区分中英文名称
        String name = idx == -1 ? hrsfUserBase.getFullName() : hrsfUserBase.getFullName().substring(0, idx);
        String enName = idx == -1 ? "" : hrsfUserBase.getFullName().substring(idx + 1);
        JSONObject jsonObject = new JSONObject();
        setValue(jsonObject, "name", name);
        setValue(jsonObject, "nameEn", enName);
        setValue(jsonObject, "userId", hrsfUserBase.getStaffId());
        setValue(jsonObject, "age", hrsfUserBase.getAge());
        setValue(jsonObject, "levelNav", hrsfUserBase.getHightestDegree());
        setValue(jsonObject, "levelNavEn", "");//TODO
        idx = hrsfUserBase.getEvaluator1().indexOf(" ");//按第一个空格区分中英文名称
        name = idx == -1 ? hrsfUserBase.getEvaluator1() : hrsfUserBase.getEvaluator1().substring(0, idx);
        enName = idx == -1 ? "" : hrsfUserBase.getEvaluator1().substring(idx + 1);
        setValue(jsonObject, "manager", name);
        setValue(jsonObject, "managerEn", enName);
        setValue(jsonObject, "addressLine1", hrsfUserBase.getLenofsInCurrentPosition());//现职位工作时长
        setValue(jsonObject, "addressLine2", hrsfUserBase.getLenofsInBbac());//BBAC工作时长
        setValue(jsonObject, "addressLine3", hrsfUserBase.getLenofsInCurrnetLevel());//现级别工作时长
        return jsonObject.toString();
    }

    private String getPositionInformation(HrsfUserBase hrsfUserBase) {
        JSONObject jsonObject = new JSONObject();
        setValue(jsonObject, "position", hrsfUserBase.getPosition());
        setValue(jsonObject, "positionEn", "");
        setValue(jsonObject, "system", hrsfUserBase.getSystem());
        setValue(jsonObject, "systemEn", "");
        setValue(jsonObject, "department", hrsfUserBase.getDepartment());
        setValue(jsonObject, "departmentEn", "");
        setValue(jsonObject, "section", hrsfUserBase.getSection());
        setValue(jsonObject, "sectionEn", "");
        setValue(jsonObject, "group", hrsfUserBase.getUserGroup());
        setValue(jsonObject, "groupEn", "");
        return jsonObject.toString();
    }

    private String getCerDataArr(List<HrsfPmsUserCompetency> hrsfPmsUserCompetencies) {
        JSONArray jsonArray = new JSONArray();
        if (!CollectionUtils.isEmpty(hrsfPmsUserCompetencies)) {
            // 能力条目展示需要按能力名称前缀进行归类且排序如下：领导力》差异核心能力》专业能力
            for (HrsfPmsUserCompetency item : hrsfPmsUserCompetencies) {
                item.setCompetencyCategoryWeight(4);
                if (item.getCompetencyCategory() != null) {
                    if (item.getCompetencyCategory().startsWith("领导力")) {
                        item.setCompetencyCategoryWeight(1);
                    } else if (item.getCompetencyCategory().startsWith("差异核心能力")) {
                        item.setCompetencyCategoryWeight(2);
                    } else if (item.getCompetencyCategory().startsWith("领导力")) {
                        item.setCompetencyCategoryWeight(3);
                    }
                }
            }
            //先按分类排序，再按每个类型排序
            hrsfPmsUserCompetencies.sort(Comparator.comparingInt(HrsfPmsUserCompetency::getCompetencyCategoryWeight).thenComparing(HrsfPmsUserCompetency::getCompetencyWeight));

            for (HrsfPmsUserCompetency hrsfPmsUserCompetency : hrsfPmsUserCompetencies) {
                JSONObject jsonObject = new JSONObject();
                setValue(jsonObject, "competencyNameCn", hrsfPmsUserCompetency.getCompetencyNameCN() + "\n" + hrsfPmsUserCompetency.getCompetencyNameEN());
                setValue(jsonObject, "competencyNameEn", "");
                setValue(jsonObject, "scoreStandard", hrsfPmsUserCompetency.getScoreStandard());
                setValue(jsonObject, "score", hrsfPmsUserCompetency.getScore());
                jsonArray.add(jsonObject);
            }
        }
        return jsonArray.toString();
    }

    private String getCompetenciesScore(HrsfPmsTalentCard talentCard) {
        return talentCard == null ? "" : talentCard.getCompetencyScore() == null ? "" : talentCard.getCompetencyScore();
    }

    //条形表格数据
    private String getAbilityToEntryDate(List<HrsfPmsUserCompetency> hrsfPmsUserCompetencies) {
        JSONArray jsonArray = new JSONArray();
        if (!CollectionUtils.isEmpty(hrsfPmsUserCompetencies)) {
            JSONObject jsonObject = new JSONObject();
            JSONArray children = new JSONArray();
            String category = null;
            for (HrsfPmsUserCompetency userCompetency : hrsfPmsUserCompetencies) {
                if (category == null || !category.equals(userCompetency.getCompetencyCategory())) {
                    jsonObject = new JSONObject();
                    children = new JSONArray();
                    category = userCompetency.getCompetencyCategory();
                    setValue(jsonObject, "competencyCategory", userCompetency.getCompetencyCategory());
                    setValue(jsonObject, "competencyCategoryEn", userCompetency.getCompetencyCategoryEn());
                    jsonObject.set("children", children);
                    jsonArray.add(jsonObject);
                }
                JSONObject child = new JSONObject();
                setValue(child, "competencyNameCn", userCompetency.getCompetencyNameCN());
                setValue(child, "competencyNameEn", userCompetency.getCompetencyNameEN());
                setValue(child, "competencyWeight", userCompetency.getCompetencyWeight() + "");
                setValue(child, "scoreStandard", userCompetency.getScoreStandard());
                setValue(child, "score", userCompetency.getScore());
                children.add(child);
            }
        }
        return jsonArray.toString();
    }

    /**
     * 1   2   3
     * 4   5   6
     * 7   8   9
     */
    private String getMatrix1Label(HrsfPmsTalentCard talentCard) {
        if (talentCard == null) {
            return "0";//都不高亮
        }
        if (talentCard.getReviewResult() == null) {
            return "0";//都不高亮
        }
        if (talentCard.getReviewResult().startsWith("低潜能、高业绩")) {
            return "1";
        } else if (talentCard.getReviewResult().startsWith("中潜能、高业绩")) {
            return "2";
        } else if (talentCard.getReviewResult().startsWith("高潜能、高业绩")) {
            return "3";
        } else if (talentCard.getReviewResult().startsWith("低潜能、中业绩")) {
            return "4";
        } else if (talentCard.getReviewResult().startsWith("中潜能、中业绩")) {
            return "5";
        } else if (talentCard.getReviewResult().startsWith("高潜能、中业绩")) {
            return "6";
        } else if (talentCard.getReviewResult().startsWith("低潜能、低业绩")) {
            return "7";
        } else if (talentCard.getReviewResult().startsWith("中潜能、低业绩")) {
            return "8";
        } else if (talentCard.getReviewResult().startsWith("高潜能、低业绩")) {
            return "9";
        }
        return "0";//都不高亮
    }

    private String getMatrix1LabelName(HrsfPmsTalentCard talentCard) {
        return talentCard == null ? "" : talentCard.getReviewResult() == null ? "" : talentCard.getReviewResult();
    }


    private String getAssessmentCenterEvaluationResultData(HrsfPmsTalentCard talentCard) {
        JSONArray jsonArray = new JSONArray();
        if (talentCard != null) {
            JSONObject jsonObject = new JSONObject();
            setValue(jsonObject, "evaluationType", talentCard.getPromoteType());
            setValue(jsonObject, "evaluationTypeEN", "");
            setValue(jsonObject, "evaluationResult", talentCard.getEvaluateResult());
            setValue(jsonObject, "evaluationResultEN", "");
            setValue(jsonObject, "evaluationDate", talentCard.getEvaluateDate());
            jsonArray.add(jsonObject);
        }
        return jsonArray.toString();
    }

    /**
     * [
     * 左：[
     * {
     * "project": "类型1雨燕计划1",
     * "projectEN": "yuyanjihua1",
     * "participateDate": "1588291200000"
     * }
     * 。。。
     * ],
     * 右：[
     * {
     * "project": "类型2雨燕计划1",
     * "projectEN": "yuyanjihua1",
     * "participateDate": "1588291200000"
     * },
     * 。。。
     * ]
     * ]
     *
     * @return
     */
    private String getKeyProjectsInvolvedData(List<HrsfPmsTalentCardKeyProject> keyProjectList) {
        JSONArray jsonArray = new JSONArray();
        JSONArray jsonArrayLeft = new JSONArray();
        JSONArray jsonArrayRight = new JSONArray();
        if (!CollectionUtils.isEmpty(keyProjectList)) {
            for (HrsfPmsTalentCardKeyProject keyProject : keyProjectList) {
                JSONObject jsonObject = new JSONObject();
                setValue(jsonObject, "project", keyProject.getProject());
                setValue(jsonObject, "projectEN", keyProject.getProjectEN());
                setValue(jsonObject, "participateDate", keyProject.getParticipateDate());
                if (BooleanUtils.isTrue(keyProject.getLeftFlag())) {
                    jsonArrayLeft.add(jsonObject);
                } else {
                    jsonArrayRight.add(jsonObject);
                }
            }
        }
        jsonArray.add(jsonArrayLeft);
        jsonArray.add(jsonArrayRight);
        return jsonArray.toString();
    }

    /**
     * [
     * {
     * "startDate": "1588291200000",
     * "endDate": "1588291200000",
     * "course": "培训课程 Training Course1",
     * "courseEN": "课程名称英文",
     * "status": "1",
     * "statusEN": "课程状态英文"
     * },
     * .....
     * ]
     */
    private String getCourseRecordPositionData(List<HrsfPmsTalentCardCourseRecord> courseRecordList) {
        JSONArray jsonArray = new JSONArray();
        if (!CollectionUtils.isEmpty(courseRecordList)) {
            for (HrsfPmsTalentCardCourseRecord courseRecord : courseRecordList) {
                JSONObject jsonObject = new JSONObject();
                setValue(jsonObject, "course", courseRecord.getCourse());
                setValue(jsonObject, "courseEN", courseRecord.getCourseEN());
                setValue(jsonObject, "status", courseRecord.getStatus());
                setValue(jsonObject, "statusEN", "");
                setValue(jsonObject, "startDate", courseRecord.getStartDate());
                setValue(jsonObject, "endDate", courseRecord.getEndDate());
                jsonArray.add(jsonObject);
            }
        }
        return jsonArray.toString();
    }


    /**
     * 需要再服务器上安装一个chrome浏览器，并且在安装一个chromedriver.exe
     * html转图片,图片转PDF
     */
    public byte[] screenshot(String path) throws InterruptedException, IOException, DocumentException {
        //驱动地址 第二个参数可修改与实际路径一致
        System.setProperty("webdriver.chrome.driver", chromeDriverConfig);

        WebDriver webDriver = null;
        ChromeOptions options = new ChromeOptions();
        //设置 chrome 的无头模式
        options.addArguments("--headless");
        options.addArguments("--disable-gpu");
        options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");
        options.addArguments("--start-maximized");
        //因为页面必须滚动才能全部展示，这里直接给个很大的高度
        options.addArguments("--window-size=2300,10000");//截图的宽和高
        //启动一个 chrome 实例
        webDriver = new ChromeDriver(options);

        //访问网址
        webDriver.get("file:///" + path);
        log.info("webDriver.get(file:///{}", path);
        Thread.sleep(3500);

        //全屏截图
        byte[] fileBytes = ((TakesScreenshot) webDriver).getScreenshotAs(OutputType.BYTES);
        FileUtils.writeByteArrayToFile(new File(path + "." + System.currentTimeMillis() + ".png"), fileBytes);
        //全屏图片
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(fileBytes));
        int maxHeight = image.getHeight();//最大高度 10000
        int maxWidth = image.getWidth();//最大宽度 2300
        //pdf
        ByteArrayOutputStream pdfDataStream = new ByteArrayOutputStream();
        Document pdfDoc = getPDFDocument(pdfDataStream);

        //生成第一页pdf需要的图片 第一页必须包含基本信息和雷达图
        //定位section元素
        WebElement element = webDriver.findElement(By.className("a4-page-1"));//根据模板获取第一页
        Point p = element.getLocation();
        int x = p.getX();
        int y = p.getY();
        log.info("a4-page-1: {} {},{},{}", x, y, element.getSize().getWidth(), element.getSize().getHeight());
        int width = element.getSize().getWidth();
        int height = element.getSize().getHeight();
        //截取子图片
        BufferedImage subimage1 = image.getSubimage(x, y, width, height);
        ByteArrayOutputStream tmpStream = new ByteArrayOutputStream();
        ImageIO.write(subimage1, "png", tmpStream);

        Image pdfImage1 = Image.getInstance(tmpStream.toByteArray());
        pdfImage1.setAlignment(Image.MIDDLE);
        //缩放比例
        int percent = getPercent(subimage1.getHeight(), subimage1.getWidth());
        pdfImage1.scalePercent(percent);
        pdfDoc.newPage();
        pdfDoc.add(pdfImage1);//添加第一页PDF


        //从第二页开始按dev高度分页,共5个模块： a4-page-2-1 a4-page-2-2 a4-page-2-3 a4-page-2-4 a4-page-2-5
        WebElement element2 = webDriver.findElement(By.className("a4-page-2"));
        //有数据的最高位置 超过这个位置就不用截下一张了
        int maxDataHeight = element2.getLocation().getY() + element2.getSize().getHeight() + y;
        //截图的宽度 +150的目的是按页面效果来的，否则右边宽度不够
        width = element2.getSize().getWidth() + 300 > maxWidth ? maxWidth : element2.getSize().getWidth() + 300;
        //每页图片高度的标准高度 按A4的长宽比计算应剪切的高度
        Float itemHeight = PageSize.A4.getHeight() / PageSize.A4.getWidth() * width;
        //累计已截图的高度
        int totalHeight = height + y;

        //先把元素信息都抓取出来
        Map<String, WebElement> elementMap = Maps.newHashMap();
        for (int i = 1; i < 6; i++) {
            String key = "a4-page-2-" + i;
            WebElement webElement = webDriver.findElement(By.className(key));
            elementMap.put(key, webElement);
            log.info("======>a4-page-{}: {} {} {} {}", i, webElement.getLocation().getX(), webElement.getLocation().getY(), webElement.getSize().getWidth(), webElement.getSize().getHeight());
        }
        //当前pdf截图到那dev个位置了
        int currentElementi = 1;
        WebElement currentElement = elementMap.get("a4-page-2-1");
        //当前div的最后位置
        int currentElementHeight = currentElement.getLocation().getY() + currentElement.getSize().getHeight();
        for (int i = 0; i < 20; i++) {
            log.info("================pdf page:{}==================", i + 2);
            //当前页应该截取的图片的高度
            int subImageHeight = totalHeight + itemHeight.intValue() > maxHeight ? maxHeight - totalHeight : itemHeight.intValue();
            log.info("subImageHeight: {}", subImageHeight);
            //如果当前剪切的内容高度超过了当前div 并且未完全包含下一个div(可能是下几个) 则少截点把下一个div踢出去
            if (totalHeight + subImageHeight > currentElementHeight) {
                //共5个模块： a4-page-2-1 a4-page-2-2 a4-page-2-3 a4-page-2-4 a4-page-2-5
                int nextElementi = currentElementi + 1;
                for (int j = nextElementi; j < 6; j++) {
                    WebElement nextElement = elementMap.get("a4-page-2-" + j);
                    int nextElementY = nextElement.getLocation().getY();
                    int nextElementHeight = nextElementY + nextElement.getSize().getHeight();
                    currentElementi = j;
                    currentElementHeight = nextElementHeight;
                    //如果当前页包含了此div的qu全部
                    if (totalHeight + subImageHeight - 5 >= nextElementHeight) {
                        continue;
                    }
                    //如果当前页包含了此div的一部分
                    if (totalHeight + subImageHeight - 5 > nextElementY && totalHeight + subImageHeight - 5 < nextElementHeight) {
                        //当前图片高度 = 当前图片高度 - nextElement包含的高度
                        subImageHeight = subImageHeight - (totalHeight + subImageHeight - nextElementY);
                        break;
                    }
                }
            }
            log.info("subImageHeight 修正: {}", subImageHeight);
            if(subImageHeight>0){
                BufferedImage subImage = image.getSubimage(x, totalHeight, width, subImageHeight);
                tmpStream.reset();
                ImageIO.write(subImage, "png", tmpStream);
                //创建com.itextpdf.text.Image
                Image pdfImageItem = Image.getInstance(tmpStream.toByteArray());
                pdfImage1.setAlignment(Image.MIDDLE);
                //按比例缩放
                pdfImageItem.scalePercent(getPercent(pdfImageItem.getWidth()+150));
                //添加新页
                pdfDoc.newPage();
                pdfDoc.add(pdfImageItem);
                totalHeight += subImageHeight;
                //如果当前页已经到达数据的底端，则退出循环
                if (totalHeight >= maxDataHeight) {
                    break;
                }
            }
        }
        //退出
        webDriver.quit();
        //刷新PDF
        pdfDoc.close();
        //测试打印看效果
//        if( log.isDebugEnabled() ) {
        File file = new File(path + "." + System.currentTimeMillis() + ".pdf");
        log.info("pdf: {}", file.getAbsolutePath());
        FileUtils.writeByteArrayToFile(file, pdfDataStream.toByteArray());
//        }
        return pdfDataStream.toByteArray();
    }

    /**
     * 等比压缩，获取压缩百分比
     *
     * @param weight 图片的宽度
     * @return 压缩百分比
     */
    private static int getPercent(float weight) {
        return Math.round(PageSize.A4.getWidth() / weight * 100);
    }

    private static int getPercent(float height, float weight) {
        float percent;
        if (height > weight) {
            percent = PageSize.A4.getHeight() / height * 100;
        } else {
            percent = PageSize.A4.getWidth() / weight * 100;
        }
        return Math.round(percent);
    }

    private static String getTempFileDir() {
        String s = System.getProperty("java.io.tmpdir");
        s = s.endsWith(File.separator) ? s : s + File.separator;
        s = s + System.currentTimeMillis() + "_" + UUID.randomUUID().toString();
        new File(s).mkdirs();
        log.debug("创建临时目录: {}", s);
        return s;
    }

    private String readResource2String(String resourceName) throws IOException {
        try (InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream(resourceName)) {
            return IOUtils.toString(resourceAsStream, StandardCharsets.UTF_8.name());
        }
    }

    private File mergeTemplate(String staffId, String part2, String templatePart1ResourceName) throws IOException {
        File file = new File(getTempFileDir() + File.separator + staffId + "_result.html");
        file.deleteOnExit();
        if (!file.createNewFile()) {
            log.error("模板合并后的临时文件传教失败");
        }
        try (FileOutputStream fileOutputStream = new FileOutputStream(file, true);
             InputStream resourceAsStream = this.getClass().getClassLoader().getResourceAsStream(templatePart1ResourceName)) {
            IOUtils.copyLarge(resourceAsStream, fileOutputStream);
            IOUtils.write(part2, fileOutputStream, StandardCharsets.UTF_8.name());
            return file;
        }
    }

    private Document getPDFDocument(ByteArrayOutputStream pdfDataStream) throws DocumentException {
        Document pdfDoc = new Document(PageSize.A4, 20, 20, 20, 20);
        PdfWriter.getInstance(pdfDoc, pdfDataStream);
        pdfDoc.open();
        return pdfDoc;
    }

    private JSONObject setValue(JSONObject jsonObject, String key, String value) {
        jsonObject.set(key, value == null ? "" : value.trim());
        return jsonObject;
    }
}
