package com.bbac.hrsf.admin.handle;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bbac.hrsf.admin.mapper.HrsfUserBaseMapper;
import com.bbac.hrsf.admin.service.*;
import com.bbac.hrsf.common.core.util.R;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.handle.DemoJobHandler</li>
 * <li>CreateTime : 2022/06/02 13:13:39</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class HrsfJobHandler {
    private final IHrsfUserBaseService iHrsfUserBaseService;

    private final IHrsfPmsuserBaseService iHrsfPmsuserBaseService;

    private final IHrsfOrganizationService iHrsfOrganizationService;

    private final IHrsfUserScoreTaskService hrsfUserScoreTaskService;

    private final IHrsfUserScoreDuplicateService hrsfUserScoreDuplicateService;
    private final RedisTemplate redisTemplate;

    private final HrsfUserBaseMapper hrsfUserBaseMapper;

    private final long size = 1000;

    /**
     * 1、简单任务示例（Bean模式）
     */
    @XxlJob("demoJobHandler")
    public ReturnT<String> demoJobHandler(String param) {
        log.info("test job handler");
        return ReturnT.SUCCESS;
    }

    /**
     * 1、同步员工数据调度任务
     */
    @XxlJob("syncInfoJobHandler")
    public ReturnT<String> syncInfoJobHandler(String param) {
        log.info("syncInfoJobHandler开始执行1");
        /**
         * 这里的current入参是从0开始，是SF平台接口的限制
         */
        try{
            hrsfUserBaseMapper.truncateUserRecord();
        }catch (Exception e){
            e.printStackTrace();
        }

        log.info("syncInfoJobHandler开始执行2");
        int i = 0;
        Boolean sync = true;
        while (sync) {
            sync = iHrsfUserBaseService.sync(i * size, size, "0", null);
            i++;
        }
        log.info("syncInfoJobHandler开始执行3");
        return ReturnT.SUCCESS;
    }

    /**
     * 6、重试 同步员工数据调度任务
     */
    @XxlJob("syncInfoJobHandlerAgain")
    public ReturnT<String> syncInfoJobHandlerAgain(String param) {

        iHrsfUserBaseService.syncAgain();

        return ReturnT.SUCCESS;
    }

    /**
     * 6、重试 同步员工数据调度任务
     */
    @XxlJob("syncFormIdAgain")
    public ReturnT<String> syncFormIdAgain(String param) {

        iHrsfUserBaseService.syncFormIdAgain();

        return ReturnT.SUCCESS;
    }

    /**
     * 2、同步照片调度任务
     */
    @XxlJob("syncPhotoJobHandler")
    public ReturnT<String> syncPhotoJobHandler(String param) {
        int i = 1;
        Boolean sync = true;
        while (sync) {
            sync = iHrsfUserBaseService.syncPhoto(i, size);
            i++;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * 3、根据员工身上的数据同步组织数结构
     */
    @XxlJob("syncOrganizationHandler")
    public ReturnT<String> syncOrganizationHandler(String param) {
        iHrsfOrganizationService.syncOrganizationHandler();
        return ReturnT.SUCCESS;
    }

    /**
     * 4、同步绩效协调员调度任务
     */
    @XxlJob("syncPmsJobHandler")
    public ReturnT<String> syncPmsJobHandler(String param) {
        iHrsfPmsuserBaseService.syncPmsJobHandler();
        return ReturnT.SUCCESS;
    }

    /**
     * 4、同步绩效协调员调度任务(不推送到SF)
     */
    @XxlJob("syncPmsJobHandlerNoPushToSF")
    public ReturnT<String> syncPmsJobHandlerNoPushToSF(String param) {
        iHrsfPmsuserBaseService.syncPmsJobHandlerNoPushToSF();
        return ReturnT.SUCCESS;
    }

    /**
     * 7、重试-同步绩效协调员调度任务
     */
    @XxlJob("syncPmsJobHandlerAgain")
    public ReturnT<String> syncPmsJobHandlerAgain(String param) {
        iHrsfOrganizationService.syncPmsJobHandlerAgain();
        return ReturnT.SUCCESS;
    }

    /**
     * 5、数据回写失败
     */
    @XxlJob("syncFinalWriteDataBackErrorHandler")
    public ReturnT<String> syncFinalWriteDataBackErrorHandler(String param) {
        iHrsfOrganizationService.syncFinalWriteDataBackErrorHandler();
        return ReturnT.SUCCESS;
    }

    /**
     * 8、重试三次后发送邮件
     */
    @XxlJob("retryErrorSendEmail")
    public ReturnT<String> retryErrorSendEmail(String param) {
        iHrsfOrganizationService.retryErrorSendEmail();
        return ReturnT.SUCCESS;
    }

    /**
     * 6、容错补偿机制
     *
     * @param
     * @return
     */
    @XxlJob("compensator")
    public R<Boolean> compensator(String assessYear, String assessType, Long calibrationBaseId) {
        return R.ok(iHrsfPmsuserBaseService.compensator(assessYear, assessType, calibrationBaseId));
    }

    /**
     * 6、创建完校准会议后,定时同步绩效员工基础表的基本信息
     *
     * @param
     * @return
     */
    @XxlJob("syncUserBaseInfoJobHandler")
    public ReturnT<String> syncUserBaseInfoJobHandler(String param) {
        int i = 1;
        Boolean sync = true;
        while (sync) {
            sync = iHrsfPmsuserBaseService.syncUserBaseInfo(i, size);
            i++;
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 检查历史绩效分数XXL-Job任务
     * @param param String for taskId
     * @return
     */
    @XxlJob("checkHrsfPmsUserScoreHistoryTaskJob")
    public ReturnT<String> handleHrsfPmsUserScoreHistoryTaskJob(String param) {
        hrsfUserScoreTaskService.checkUserScoreTask();
        return ReturnT.SUCCESS;
    }

    /**
     * 检查绩效复制XXL-Job任务
     * @param param String for taskId
     * @return
     */
    @XxlJob("checkHrsfPmsUserScoreDuplicateTaskJob")
    public ReturnT<String> checkHrsfPmsUserScoreDuplicateTaskJob(String param) {
        hrsfUserScoreDuplicateService.checkUserScoreTask();
        return ReturnT.SUCCESS;
    }
}