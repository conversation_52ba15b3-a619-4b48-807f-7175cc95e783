package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseDownloadService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.FormStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnumNew;
import com.bbac.hrsf.common.core.constant.enums.PotentialLevelEnum;
import com.bbac.hrsf.common.core.constant.enums.ScoreSourceEnum;
import com.bbac.hrsf.common.core.constant.enums.ShareJoinBbacEnum;
import com.bbac.hrsf.common.core.constant.enums.VariableEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.core.util.ppt.PptCreateUtil;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import com.bbac.hrsf.performance.api.dto.HrsfCalibrationBaseDTO;
import com.bbac.hrsf.performance.api.feign.RemotePmsService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.xslf.usermodel.XSLFShape;
import org.apache.poi.xslf.usermodel.XSLFSlide;
import org.apache.poi.xslf.usermodel.XSLFTable;
import org.apache.poi.xslf.usermodel.XSLFTableCell;
import org.apache.poi.xslf.usermodel.XSLFTableRow;
import org.apache.poi.xslf.usermodel.XSLFTextParagraph;
import org.apache.poi.xslf.usermodel.XSLFTextRun;
import org.apache.poi.xslf.usermodel.XSLFTextShape;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.awt.Color;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static org.apache.poi.sl.usermodel.TextParagraph.TextAlign.CENTER;
import static org.apache.poi.sl.usermodel.TextParagraph.TextAlign.LEFT;

/**
 * @author: liu, jie
 * @create: 2022-06-20
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class HrsfPmsuserBaseDownloadServiceImpl implements IHrsfPmsuserBaseDownloadService {

    static String[] HEAD = new String[]{"工号 Staff ID", "姓名 Full Name", "系统System", "部门 Department", "科室 Section", "工段/组 Group", "职级 Level", "最终总分 Final Result", "业绩等级 Performance Category", "潜能等级 Potential Category", "可变动性 Availability", "分数来源 Rating Source", "新入职员工 New Hire", "新晋升员工 New Promotion", "股东方加入BBAC人员 Shareholders Join BBAC", "出勤率 Attendance Rate"
            , "产假 Maternity Leave", "旷工 Absence", "惩处信息Punishment", "季度均分+5% Average(Q1+Q2+Q3)+5%","表单状态 Form Status"};
    static String[] HEAD_QUARTER = new String[]{"工号 Staff ID", "姓名 Full Name", "系统System", "部门 Department", "科室 Section", "工段/组 Group", "职级 Level", "最终总分 Final Result", "业绩等级 Performance Category", "分数来源 Rating Source", "新入职员工 New Hire", "新晋升员工 New Promotion", "股东方加入BBAC人员 Shareholders Join BBAC", "出勤率 Attendance Rate"
            , "产假 Maternity Leave", "旷工 Absence", "惩处信息 Punishment","表单状态 Form Status"};

    static String[] HEAD2 = new String[]{"工号 Staff ID", "姓名 Full Name", "系统System", "部门 Department", "科室 Section", "工段/组 Group", "职级 Level", "最终总分 Final Result", "业绩等级 Performance Category", "潜能等级 Potential Category", "可变动性 Availability", "分数来源 Rating Source", "新入职员工 New Hire", "新晋升员工 New Promotion", "股东方加入BBAC人员 Shareholders Join BBAC", "出勤率 Attendance Rate"
            , "产假 Maternity Leave", "旷工 Absence", "惩处信息Punishment", "季度均分+5% Average(Q1+Q2+Q3)+5%", "表单编号 Form ID", "表单状态 Form Status", "评分人 Rater"};
    static String[] HEAD2_QUARTER = new String[]{"工号 Staff ID", "姓名 Full Name", "系统System", "部门 Department", "科室 Section", "工段/组 Group", "职级 Level", "最终总分 Final Result", "业绩等级 Performance Category", "分数来源 Rating Source", "新入职员工 New Hire", "新晋升员工 New Promotion", "股东方加入BBAC人员 Shareholders Join BBAC", "出勤率 Attendance Rate"
            , "产假 Maternity Leave", "旷工 Absence", "惩处信息 Punishment", "表单编号 Form ID", "表单状态 Form Status", "评分人 Rater"};


    private final RemotePmsService remotePmsService;

    private final IHrsfPmsuserBaseService pmsUserBaseService;

    private static final String CALIBRATION_NAME_TEMPLATE = "{}年{}考核 {} {} Review";

    @Override
    public void reportCalibrationExcel(AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception {
        List<HrsfPmsuserBase> hrsfPmsuserBases = pmsUserBaseService.selectByBaseId(addCalibrationUserDTO.getCalibrationId());
        reportDownloadExcel(addCalibrationUserDTO, hrsfPmsuserBases, 1, response);
    }

    @Override
    public void reportOverviewExcel(AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception {
        if (StringUtils.isBlank(addCalibrationUserDTO.getAssesYear()) || StringUtils.isBlank(addCalibrationUserDTO.getAssesType())) {
            throw new CheckedException("assesYear和assesType不能为空");
        }
        List<HrsfPmsuserBase> hrsfPmsuserBases = pmsUserBaseService.selectByCalibrationUserDTO(addCalibrationUserDTO);
        reportDownloadExcel(addCalibrationUserDTO, hrsfPmsuserBases, 2, response);
    }

    @Override
    public void reportDownloadPpt(AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) {
        if (addCalibrationUserDTO.getCalibrationId() == null) {
            throw new CheckedException("calibrationBaseId为空,请稍后再试!");
        }
        R<HrsfCalibrationBaseDTO> calibration = remotePmsService.getCalibrationInner(addCalibrationUserDTO.getCalibrationId(), SecurityConstants.FROM_IN);
        String fileName;
        HrsfCalibrationBaseDTO data;
        if (calibration.getCode() == 0) {
            data = calibration.getData();
            fileName = Objects.nonNull(data) ? data.getName() : "校准会议";
        } else {
            throw new CheckedException("校准会议查询失败");
        }

        List<HrsfPmsuserBase> hrsfPmsUserBases = pmsUserBaseService.selectByBaseId(addCalibrationUserDTO.getCalibrationId());
        List<HrsfPmsuserBase> collect = hrsfPmsUserBases.stream().filter(e -> Objects.nonNull(e.getTotalScore())).collect(Collectors.toList());
        int size = hrsfPmsUserBases.size();
        String totalNumber = collect.size() + "/" + size;

        BigDecimal total = BigDecimal.ZERO;
        for (HrsfPmsuserBase dto : hrsfPmsUserBases) {
            if (Objects.nonNull(dto.getTotalScore())) {
                total = total.add(dto.getTotalScore() != null ? dto.getTotalScore() : BigDecimal.ZERO);
            }
        }
        BigDecimal divide;
        if (CollUtil.isNotEmpty(collect)) {
            divide = total.divide(BigDecimal.valueOf(collect.size()), 2, RoundingMode.HALF_UP);
        } else {
            divide = BigDecimal.ZERO;
        }
        InputStream fis = null;
        try {
            //2022-07-29 添加公司校准会议的判断
            if (Objects.equals(data.getTemplate(), CalibrationTemplateEnum.TWO.getType()) || Objects.equals(data.getTemplate(), CalibrationTemplateEnum.THREE.getType())) {
                fis = this.getClass().getClassLoader().getResourceAsStream("PPT_Template.pptx");
            } else {
                fis = this.getClass().getClassLoader().getResourceAsStream("PPT_Template_1.pptx");
            }

            XMLSlideShow ppt = new XMLSlideShow(fis != null ? fis : null);
            // 获取模板中的每一页的ppt
            List<XSLFSlide> slides = ppt.getSlides();
            /**
             * 计算建议人数
             */
            /**
             * fix bug 190
             * 2022-07-05
             * 如果总人数<=1的话,则返回null
             */
            BigDecimal outTotal;
            BigDecimal outExcelTotal;
            if (size <= 1) {
                outTotal = null;
                outExcelTotal = null;
            } else {
                BigDecimal outStanding = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.1)).setScale(0, BigDecimal.ROUND_HALF_UP);
                BigDecimal exelOutTotalTemp = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.35)).setScale(0, BigDecimal.ROUND_HALF_UP);
                BigDecimal exelOutPart = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.1)).setScale(0, BigDecimal.ROUND_HALF_UP)
                        .add(BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.25)).setScale(0, BigDecimal.ROUND_HALF_UP));
                outTotal = size > 4 ? outStanding : BigDecimal.valueOf(1);
                outExcelTotal = exelOutTotalTemp.compareTo(exelOutPart) == 1 ? exelOutTotalTemp : exelOutPart;
            }

            //第一页
            XSLFSlide xslfShapes = slides.get(0);
            for (XSLFShape shape : xslfShapes) {
                if (shape instanceof XSLFTextShape) {
                    XSLFTextShape txShape = (XSLFTextShape) shape;
                    // 替换文字内容
                    XSLFTextRun xslfTextRun;
                    if (txShape.getText().contains("{totalNumber}")) {
                        xslfTextRun = txShape.setText(txShape.getText().replace("{totalNumber}", totalNumber));
                        setTextStyle(xslfTextRun);
                    } else if (txShape.getText().contains("{averageRating}")) {
                        xslfTextRun = txShape.setText(txShape.getText().replace("{averageRating}", divide + "%"));
                        setTextStyle(xslfTextRun);
                    } else if (txShape.getText().contains("{CalibrationName}")) {
                        xslfTextRun = txShape.setText(txShape.getText().replace("{CalibrationName}", fileName));
                        setTextStyle14(xslfTextRun);
                    } else if (txShape.getText().contains("{outTotal}") && outTotal != null) {
                        xslfTextRun = txShape.setText(txShape.getText().replace("{outTotal}", outTotal.toString()));
                        setTextStyle(xslfTextRun);
                    } else if (txShape.getText().contains("{outExcelTotal}") && outExcelTotal != null) {
                        xslfTextRun = txShape.setText(txShape.getText().replace("{outExcelTotal}", outExcelTotal.toString()));
                        setTextStyle(xslfTextRun);
                    }
                } else if (shape instanceof XSLFTable) {
                    if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
                        XSLFTable xslfTable = (XSLFTable) shape;
                        setRowOne(xslfTable, 0, getPmsLeveStr(hrsfPmsUserBases,addCalibrationUserDTO.getAssesYear()));

                        XSLFTableRow xslfTableCells = xslfTable.getRows().get(2);
                        List<XSLFTableCell> cells = xslfTableCells.getCells();
                        Map<String, List<String>> pmsLeveNameStr = getPmsLeveNameStr(hrsfPmsUserBases,addCalibrationUserDTO.getAssesYear());
                        for (int i = 0; i < cells.size(); i++) {
                            XSLFTableCell tableCell = cells.get(i);
                            List<String> pmsLevel = new ArrayList<>();
                            String assessYear = addCalibrationUserDTO.getAssesYear();
                            log.info("pmsLeveNameStr====>assesYear:{}",assessYear);
                            int year = 2025; // 默认值
                            if (StringUtils.isNotBlank(assessYear)) {
                                year = Integer.parseInt(assessYear);
                            }
                            if(year < 2025){
                                if (i == 0) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Insufficient.name());
                                } else if (i == 1) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Inconsistent.name());
                                } else if (i == 2) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Successful.name() + "Max");
                                } else if (i == 3) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Successful.name() + "Min");
                                } else if (i == 4) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Excellent.name() + "Max");
                                } else if (i == 5) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Excellent.name() + "Min");
                                } else if (i == 6) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnum.Outstanding.name());
                                }
                            }else  {
                                if (i == 0) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Insufficient.name());
                                } else if (i == 1) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Inconsistent.name());
                                } else if (i == 2) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Successful.name() + "Max");
                                } else if (i == 3) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Successful.name() + "Min");
                                } else if (i == 4) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Excellent.name() + "Max");
                                } else if (i == 5) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Excellent.name() + "Min");
                                } else if (i == 6) {
                                    pmsLevel = pmsLeveNameStr.get(PmsLevelEnumNew.Outstanding.name());
                                }
                            }
                            for (int j = 0; j < pmsLevel.size(); j++) {
                                XSLFTextParagraph paragraph;
                                if (j == 0) {
                                    paragraph = tableCell.getTextParagraphs().get(0);
                                } else {
                                    paragraph = tableCell.addNewTextParagraph();
                                }
                                paragraph.setTextAlign(LEFT);
                                XSLFTextRun xslfTextRun = paragraph.addNewTextRun();
                                xslfTextRun.setText(pmsLevel.get(j));
                                setTextStyle7(xslfTextRun);
                            }
                        }
                    }
                }
            }
            //第二页
            if (CollUtil.isNotEmpty(hrsfPmsUserBases) && (Objects.equals(data.getTemplate(), CalibrationTemplateEnum.TWO.getType()) || Objects.equals(data.getTemplate(), CalibrationTemplateEnum.THREE.getType()))) {
                /**
                 * fix bug 190
                 * 2022-07-05
                 * 如果总人数<=1的话,则返回null
                 */

                BigDecimal readyStep;
                BigDecimal readyStepPro;
                if (size <= 1) {
                    readyStep = null;
                    readyStepPro = null;
                } else {
                    BigDecimal outStanding = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.1)).setScale(0, BigDecimal.ROUND_HALF_UP);
                    BigDecimal exelOutTotalTemp = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.35)).setScale(0, BigDecimal.ROUND_HALF_UP);
                    BigDecimal exelOutPart = BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.1)).setScale(0, BigDecimal.ROUND_HALF_UP)
                            .add(BigDecimal.valueOf(size).multiply(BigDecimal.valueOf(0.25)).setScale(0, BigDecimal.ROUND_HALF_UP));
                    readyStep = size > 4 ? outStanding : BigDecimal.valueOf(1);
                    readyStepPro = exelOutTotalTemp.compareTo(exelOutPart) == 1 ? exelOutTotalTemp : exelOutPart;
                }
                XSLFSlide xslfShapes2 = slides.get(1);
                for (XSLFShape shape : xslfShapes2) {
                    if (shape instanceof XSLFTextShape) {
                        XSLFTextShape txShape = (XSLFTextShape) shape;
                        // 替换文字内容
                        XSLFTextRun xslfTextRun;
                        if (txShape.getText().contains("{totalNumber}")) {
                            xslfTextRun = txShape.setText(txShape.getText().replace("{totalNumber}", totalNumber));
                            setTextStyle(xslfTextRun);
                        } else if (txShape.getText().contains("{CalibrationName}")) {
                            xslfTextRun = txShape.setText(txShape.getText().replace("{CalibrationName}", fileName));
                            setTextStyle14(xslfTextRun);
                        } else if (txShape.getText().contains("{readyStep}") && readyStep != null) {
                            xslfTextRun = txShape.setText(txShape.getText().replace("{readyStep}", readyStep.toString()));
                            setTextStyle(xslfTextRun);
                        } else if (txShape.getText().contains("{readyStepPro}") && readyStepPro != null) {
                            xslfTextRun = txShape.setText(txShape.getText().replace("{readyStepPro}", readyStepPro.toString()));
                            setTextStyle(xslfTextRun);
                        }
                    } else if (shape instanceof XSLFTable) {
                        XSLFTable xslfTable = (XSLFTable) shape;
                        setRowOne(xslfTable, 1, getPotentialLevelStr(hrsfPmsUserBases));
                        XSLFTableRow xslfTableCells = xslfTable.getRows().get(2);
                        List<XSLFTableCell> cells = xslfTableCells.getCells();

                        Map<String, List<String>> baseMap = hrsfPmsUserBases.stream().collect(Collectors.groupingBy(e -> e.getVariable() + "#" + e.getPotentialLevel(),
                                Collectors.mapping(HrsfPmsuserBase::getFullName, Collectors.toList())));
                        setCellParagraph(cells.get(1), baseMap.get(VariableEnum.STAY.getType() + "#" + PotentialLevelEnum.A.getType()));
                        setCellParagraph(cells.get(5), baseMap.get(VariableEnum.STAY.getType() + "#" + PotentialLevelEnum.C.getType()));
                        setCellParagraph(cells.get(6), baseMap.get(VariableEnum.STAY.getType() + "#" + PotentialLevelEnum.D.getType()));
                        setCellParagraph(cells.get(7), baseMap.get(VariableEnum.STAY.getType() + "#" + PotentialLevelEnum.E.getType()));
                        setCellParagraph123(cells.get(2), cells.get(3), cells.get(4), baseMap.get(VariableEnum.STAY.getType() + "#" + PotentialLevelEnum.B.getType()));

                        XSLFTableRow xslfTableCells3 = xslfTable.getRows().get(3);
                        List<XSLFTableCell> cells3 = xslfTableCells3.getCells();
                        setCellParagraph(cells3.get(1), baseMap.get(VariableEnum.MOVE.getType() + "#" + PotentialLevelEnum.A.getType()));
                        setCellParagraph(cells3.get(5), baseMap.get(VariableEnum.MOVE.getType() + "#" + PotentialLevelEnum.C.getType()));
                        setCellParagraph(cells3.get(6), baseMap.get(VariableEnum.MOVE.getType() + "#" + PotentialLevelEnum.D.getType()));
                        setCellParagraph(cells3.get(7), baseMap.get(VariableEnum.MOVE.getType() + "#" + PotentialLevelEnum.E.getType()));
                        setCellParagraph123(cells3.get(2), cells3.get(3), cells3.get(4), baseMap.get(VariableEnum.MOVE.getType() + "#" + PotentialLevelEnum.B.getType()));
                    }
                }
            }
            this.setResponseHeader(response, fileName + ".pptx");
            PptCreateUtil.pptWirteOut(ppt, response.getOutputStream());
        } catch (Exception e) {
            throw new CheckedException(e);
        } finally {
            try {
                fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    public void reportDownloadExcel(AddCalibrationUserDTO addCalibrationUserDTO, List<HrsfPmsuserBase> hrsfPmsUserBases, int flag, HttpServletResponse response) throws Exception {
        String overviewName = addCalibrationUserDTO.getAssesYear() + "年考核";
        String fileName;
        if (addCalibrationUserDTO.getCalibrationId() == null) {
            fileName = overviewName;
        } else {
            R<HrsfCalibrationBaseDTO> calibration = remotePmsService.getCalibrationInner(addCalibrationUserDTO.getCalibrationId(), SecurityConstants.FROM_IN);
            if (calibration.getCode() == 0) {
                HrsfCalibrationBaseDTO data = calibration.getData();
                fileName = Objects.nonNull(data) ? data.getName() : overviewName;
                addCalibrationUserDTO.setAssesYear(data.getAssesYear());
                addCalibrationUserDTO.setAssesType(data.getAssesType());
            } else {
                fileName = overviewName;
            }
        }
        String descByType = AssessTypeEnum.getDescByType(addCalibrationUserDTO.getAssesType());
        HSSFWorkbook workbook = new HSSFWorkbook();
        // 生成sheet
        Sheet sheet = workbook.createSheet("列表视图");
        sheet.setDefaultColumnWidth(9);
        Row sheetRow1 = sheet.createRow(0);
        CellStyle cellStyle2 = getTitleCellStyle(workbook);
        CellRangeAddress region;
        Row sheetRow = sheet.createRow(1);
        sheetRow.setHeight((short) 1800);
        CellStyle cellStyle = getHeaderCellStyle(workbook);
        CellStyle rowCellStyle = getRowCellStyle(workbook);
        /**
         * 这里区分年度和季度
         */
        if (AssessTypeEnum.YEAR.getType().equals(addCalibrationUserDTO.getAssesType())) {
            if (flag == 1) {
                region = new CellRangeAddress(0, 0, 0, 20);
                sheet.addMergedRegion(region);
                for (int j = 0; j < HEAD.length; j++) {
                    Cell cell = sheetRow1.createCell(j);
                    cell.setCellValue(fileName);
                    cell.setCellStyle(cellStyle2);
                }
                //设置自动换行
                for (int j = 0; j < HEAD.length; j++) {
                    Cell cell = sheetRow.createCell(j);
                    cell.setCellValue(HEAD[j]);
                    cell.setCellStyle(cellStyle);
                }
                for (int j = 0; j < HEAD.length; j++) {
                    Cell cell = sheetRow.createCell(j);
                    cell.setCellValue(HEAD[j]);
                    cell.setCellStyle(cellStyle);
                }
            } else {
                region = new CellRangeAddress(0, 0, 0, 22);
                sheet.addMergedRegion(region);

                for (int j = 0; j < HEAD2.length; j++) {
                    Cell cell = sheetRow1.createCell(j);
                    /**
                     * 重写fileName
                     */
                    String descEnByType = AssessTypeEnum.getDescEnByType(addCalibrationUserDTO.getAssesType());
                    fileName = StrUtil.format(CALIBRATION_NAME_TEMPLATE, addCalibrationUserDTO.getAssesYear()
                            , descByType, addCalibrationUserDTO.getAssesYear(), descEnByType);
                    cell.setCellValue(fileName);
                    cell.setCellStyle(cellStyle2);
                }
                //设置自动换行
                for (int j = 0; j < HEAD2.length; j++) {
                    Cell cell = sheetRow.createCell(j);
                    cell.setCellValue(HEAD2[j]);
                    cell.setCellStyle(cellStyle);
                }
            }

            List<List<Object>> dataList = Lists.newArrayList();
            if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
                BigDecimal total = BigDecimal.ZERO;
                for (HrsfPmsuserBase dto : hrsfPmsUserBases) {
                    List<Object> data = new ArrayList<>();
                    data.add(dto.getStaffId());
                    data.add(dto.getFullName());
                    data.add(dto.getSystem());
                    data.add(dto.getDepartment());
                    data.add(dto.getSection());
                    data.add(dto.getUserGroup());
                    //2022-07-29 添加职级字段
                    data.add(dto.getUserLevel());
                    String totalScore = Objects.nonNull(dto.getTotalScore()) ? dto.getTotalScore().toString() + "%" : "";
                    data.add(totalScore);
                    String assessYear = addCalibrationUserDTO.getAssesYear();
                    int year = 2025; // 默认值
                    if (StringUtils.isNotBlank(assessYear)) {
                        year = Integer.parseInt(assessYear);
                    }
                    if (year < 2025) {
                        data.add(PmsLevelEnum.getDescByCode(dto.getPmsLevel()));
                    } else {
                        data.add(PmsLevelEnumNew.getDescByCode(dto.getPmsLevel()));
                    }
                    data.add(PotentialLevelEnum.getDescByType(dto.getPotentialLevel()));
                    data.add(VariableEnum.getDescByType(dto.getVariable()));
                    data.add(ScoreSourceEnum.getDescByCode(dto.getScoreSource()));
                    if (Objects.equals(dto.getNewHire(), "N")) {
                        data.add("");
                    } else {
                        data.add(dto.getNewHire());
                    }
                    if (Objects.equals(dto.getNewPromotion(), "N")) {
                        data.add("");
                    } else {
                        data.add(dto.getNewPromotion());
                    }
                    data.add(ShareJoinBbacEnum.getDescByType(dto.getShareholdersJoinBbac()));
                    data.add(dto.getAttendanceRate());
                    data.add(dto.getMaternityLeave());
                    data.add(dto.getAbsence());
                    data.add(dto.getPunishInfo());
                    String s = Objects.nonNull(dto.getAverageScore()) ? dto.getAverageScore().toString() + "%" : "";
                    data.add(s);
                    if(flag==1){
                        data.add(FormStatusEnum.getByCode(dto.getFormStatus()).getDescription());
                    }
                    if (flag == 2) {
                        data.add(dto.getFormId());
                        data.add(FormStatusEnum.getByCode(dto.getFormStatus()).getDescription());
                        data.add(dto.getEvaluators());
                    }
                    dataList.add(data);
                    if (Objects.nonNull(dto.getTotalScore())) {
                        total = total.add(dto.getTotalScore());
                    }
                }
                for (int i = 0; i < dataList.size(); i++) {
                    Row row = sheet.createRow(i + 2);
                    row.setHeight((short) 3000);
                    List<Object> objects = dataList.get(i);
                    for (int j = 0; j < objects.size(); j++) {
                        Cell cell = row.createCell(j);
                        cell.setCellValue(objects.get(j) != null ? objects.get(j).toString() : "");
                        cell.setCellStyle(rowCellStyle);
                    }
                }

                int row1 = dataList.size() + 2;
                createTitle(workbook, sheet, row1, "已评分 Rated");
                int rated = hrsfPmsUserBases.stream().filter(e -> Objects.nonNull(e.getTotalScore())).collect(Collectors.toList()).size();
                String value1 = rated + "/" + hrsfPmsUserBases.size();
                createValue(workbook, sheet, row1 + 1, value1);
                createTitle(workbook, sheet, row1 + 2, "绩效均分Average Rating");
                if (rated != 0) {
                    BigDecimal divide = total.divide(BigDecimal.valueOf(rated), 2, RoundingMode.HALF_UP);
                    createValue(workbook, sheet, row1 + 3, divide + "%");
                } else {
                    createValue(workbook, sheet, row1 + 3, "");
                }
                createTitle2(workbook, sheet, row1 + 4, "业绩等级分布 Performance Distribution");

                List<List<String>> totalList = new ArrayList<>();
                List<String> total1 = new ArrayList<>();
                total1.add("各绩效等级分布 Distribution");
                log.info("11addCalibrationUserDTO.getAssesYear() = {}",addCalibrationUserDTO.getAssesYear());
                String assessYear = addCalibrationUserDTO.getAssesYear();
                int year = 2025; // 默认值
                if (StringUtils.isNotBlank(assessYear)) {
                    year = Integer.parseInt(assessYear);
                }
                if (year < 2025) {
                    total1.add("完全不达标 \nInsufficient");
                    total1.add("不完全达标 \nInconsistent");
                    total1.add("完全达标 Successful");
                } else {
                    total1.add("需改进 \nImprovement Required");
                    total1.add("及格 \nSufficient");
                    total1.add("良好 Successful");
                }
                total1.add("优秀 Excellent");
                total1.add("杰出 Outstanding");

                totalList.add(total1);
                List<String> total2 = new ArrayList<>();
                total2.add("人数\n" +
                        "Number of Persons");
                if(year < 2025){
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Insufficient.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Inconsistent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Successful.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Excellent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Outstanding.getType()));
                }else{
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Insufficient.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Inconsistent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Successful.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Excellent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Outstanding.getType()));
                }
                totalList.add(total2);
                List<String> total3 = new ArrayList<>();
                total3.add("比例\n" +
                        "Percentage");
                if (year < 2025) {
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Insufficient.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Inconsistent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Successful.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Excellent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Outstanding.getType(), hrsfPmsUserBases.size()));
                }else{
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Insufficient.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Inconsistent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Successful.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Excellent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Outstanding.getType(), hrsfPmsUserBases.size()));
                }
                totalList.add(total3);
                createValue2(workbook, sheet, row1 + 5, totalList);
                createTitle2(workbook, sheet, row1 + 8, "潜能等级分布 Potential Distribution");
                List<List<String>> totalList2 = new ArrayList<>();
                List<String> total4 = new ArrayList<>();
                total4.add("各绩效等级分布 Distribution");
                total4.add("配置有待评审 \n" +
                        "Placement to be reviewed");
                total4.add("在同级别发展 \n" +
                        "Development on the same level");
                total4.add("有发展前途的人才 Promising talent");
                total4.add("一个发展步骤 \n" +
                        "1-Step");
                total4.add("就绪 \n" +
                        "Ready");
                totalList2.add(total4);
                List<String> total5 = new ArrayList<>();
                total5.add("人数\n" +
                        "Number of Persons");
                total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.A.getType()));
                total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.B.getType()));
                total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.C.getType()));
                total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.D.getType()));
                total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.E.getType()));
                totalList2.add(total5);
                List<String> total6 = new ArrayList<>();
                total6.add("比例\n" +
                        "Percentage");
                total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.A.getType(), hrsfPmsUserBases.size()));
                total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.B.getType(), hrsfPmsUserBases.size()));
                total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.C.getType(), hrsfPmsUserBases.size()));
                total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.D.getType(), hrsfPmsUserBases.size()));
                total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.E.getType(), hrsfPmsUserBases.size()));
                totalList2.add(total6);
                createValue2(workbook, sheet, row1 + 9, totalList2);
            }
        } else {
            if (flag == 1) {
                region = new CellRangeAddress(0, 0, 0, 17);
                sheet.addMergedRegion(region);
                for (int j = 0; j < HEAD_QUARTER.length; j++) {
                    Cell cell = sheetRow1.createCell(j);
                    cell.setCellValue(fileName);
                    cell.setCellStyle(cellStyle2);
                }
                //设置自动换行
                for (int j = 0; j < HEAD_QUARTER.length; j++) {
                    Cell cell = sheetRow.createCell(j);
                    cell.setCellValue(HEAD_QUARTER[j]);
                    cell.setCellStyle(cellStyle);
                }
                for (int j = 0; j < HEAD_QUARTER.length; j++) {
                    Cell cell = sheetRow.createCell(j);
                    cell.setCellValue(HEAD_QUARTER[j]);
                    cell.setCellStyle(cellStyle);
                }
            } else {
                region = new CellRangeAddress(0, 0, 0, 19);
                sheet.addMergedRegion(region);

                for (int j = 0; j < HEAD2_QUARTER.length; j++) {
                    Cell cell = sheetRow1.createCell(j);
                    /**
                     * 重写fileName
                     */
                    String descEnByType = AssessTypeEnum.getDescEnByType(addCalibrationUserDTO.getAssesType());
                    fileName = StrUtil.format(CALIBRATION_NAME_TEMPLATE, addCalibrationUserDTO.getAssesYear()
                            , descByType, addCalibrationUserDTO.getAssesYear(), descEnByType);
                    cell.setCellValue(fileName);
                    cell.setCellValue(fileName);
                    cell.setCellStyle(cellStyle2);
                }
                //设置自动换行
                for (int j = 0; j < HEAD2_QUARTER.length; j++) {
                    Cell cell = sheetRow.createCell(j);
                    cell.setCellValue(HEAD2_QUARTER[j]);
                    cell.setCellStyle(cellStyle);
                }
            }
            List<List<Object>> dataList = Lists.newArrayList();
            if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
                BigDecimal total = BigDecimal.ZERO;
                for (HrsfPmsuserBase dto : hrsfPmsUserBases) {
                    List<Object> data = new ArrayList<>();
                    data.add(dto.getStaffId());
                    data.add(dto.getFullName());
                    data.add(dto.getSystem());
                    data.add(dto.getDepartment());
                    data.add(dto.getSection());
                    data.add(dto.getUserGroup());
                    //2022-07-29 添加职级字段
                    data.add(dto.getUserLevel());
                    String totalScore = Objects.nonNull(dto.getTotalScore()) ? dto.getTotalScore().toString() + "%" : "";
                    data.add(totalScore);
                    String assessYear = addCalibrationUserDTO.getAssesYear();
                    int year = 2025; // 默认值
                    if (StringUtils.isNotBlank(assessYear)) {
                        year = Integer.parseInt(assessYear);
                    }
                    if (year < 2025) {
                        data.add(PmsLevelEnum.getDescByCode(dto.getPmsLevel()));
                    } else {
                        data.add(PmsLevelEnumNew.getDescByCode(dto.getPmsLevel()));
                    }
                    /**
                     * 季度考核不需要添加这两项
                     data.add(PotentialLevelEnum.getDescByType(dto.getPotentialLevel()));
                     data.add(VariableEnum.getDescByType(dto.getVariable()));
                     */
                    data.add(ScoreSourceEnum.getDescByCode(dto.getScoreSource()));
                    if (Objects.equals(dto.getNewHire(), "N")) {
                        data.add("");
                    } else {
                        data.add(dto.getNewHire());
                    }
                    if (Objects.equals(dto.getNewPromotion(), "N")) {
                        data.add("");
                    } else {
                        data.add(dto.getNewPromotion());
                    }
                    data.add(ShareJoinBbacEnum.getDescByType(dto.getShareholdersJoinBbac()));
                    data.add(dto.getAttendanceRate());
                    data.add(dto.getMaternityLeave());
                    data.add(dto.getAbsence());
                    data.add(dto.getPunishInfo());
                    /**
                     * 季度考核不需要添加这两项
                     String s = Objects.nonNull(dto.getAverageScore()) ? dto.getAverageScore().toString() + "%" : "";
                     data.add(s);
                     */
                    if(flag==1){
                        data.add(FormStatusEnum.getByCode(dto.getFormStatus()).getDescription());
                    }
                    if (flag == 2) {
                        data.add(dto.getFormId());
                        data.add(FormStatusEnum.getByCode(dto.getFormStatus()).getDescription());
                        data.add(dto.getEvaluators());
                    }
                    dataList.add(data);
                    if (Objects.nonNull(dto.getTotalScore())) {
                        total = total.add(dto.getTotalScore());
                    }
                }
                for (int i = 0; i < dataList.size(); i++) {
                    Row row = sheet.createRow(i + 2);
                    row.setHeight((short) 3000);
                    List<Object> objects = dataList.get(i);
                    for (int j = 0; j < objects.size(); j++) {
                        Cell cell = row.createCell(j);
                        cell.setCellValue(objects.get(j) != null ? objects.get(j).toString() : "");
                        cell.setCellStyle(rowCellStyle);
                    }
                }

                int row1 = dataList.size() + 2;
                createTitle(workbook, sheet, row1, "已评分 Rated");
                int rated = hrsfPmsUserBases.stream().filter(e -> Objects.nonNull(e.getTotalScore())).collect(Collectors.toList()).size();
                String value1 = rated + "/" + hrsfPmsUserBases.size();
                createValue(workbook, sheet, row1 + 1, value1);
                createTitle(workbook, sheet, row1 + 2, "绩效均分Average Rating");
                if (rated != 0) {
                    BigDecimal divide = total.divide(BigDecimal.valueOf(rated), 2, RoundingMode.HALF_UP);
                    createValue(workbook, sheet, row1 + 3, divide + "%");
                } else {
                    createValue(workbook, sheet, row1 + 3, "");
                }
                createTitle2(workbook, sheet, row1 + 4, "业绩等级分布 Performance Distribution");

                List<List<String>> totalList = new ArrayList<>();
                List<String> total1 = new ArrayList<>();
                total1.add("各绩效等级分布 Distribution");
                log.info("22addCalibrationUserDTO.getAssesYear() = {}",addCalibrationUserDTO.getAssesYear());
                String assessYear = addCalibrationUserDTO.getAssesYear();
                int year = 2025; // 默认值
                if (StringUtils.isNotBlank(assessYear)) {
                    year = Integer.parseInt(assessYear);
                }
                if (year < 2025) {
                    total1.add("完全不达标 \nInsufficient");
                    total1.add("不完全达标 \nInconsistent");
                    total1.add("完全达标 Successful");
                } else {
                    total1.add("需改进 \nImprovement Required");
                    total1.add("及格 \nSufficient");
                    total1.add("良好 Successful");
                }
                total1.add("优秀 Excellent");
                total1.add("杰出 Outstanding");

                totalList.add(total1);
                List<String> total2 = new ArrayList<>();
                total2.add("人数\n" +
                        "Number of Persons");
                if(year < 2025){
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Insufficient.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Inconsistent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Successful.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Excellent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnum.Outstanding.getType()));
                }else{
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Insufficient.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Inconsistent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Successful.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Excellent.getType()));
                    total2.add(getPmsLeveSize(hrsfPmsUserBases, PmsLevelEnumNew.Outstanding.getType()));
                }
                totalList.add(total2);
                List<String> total3 = new ArrayList<>();
                total3.add("比例\n" +
                        "Percentage");
                if(year < 2025){
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Insufficient.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Inconsistent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Successful.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Excellent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnum.Outstanding.getType(), hrsfPmsUserBases.size()));
                }else{
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Insufficient.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Inconsistent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Successful.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Excellent.getType(), hrsfPmsUserBases.size()));
                    total3.add(getPmsLeveSizePer(hrsfPmsUserBases, PmsLevelEnumNew.Outstanding.getType(), hrsfPmsUserBases.size()));
                }
                totalList.add(total3);
                createValue2(workbook, sheet, row1 + 5, totalList);
                /**
                 * createTitle2(workbook, sheet, row1 + 8, "潜能等级分布 Potential Distribution");
                 List<List<String>> totalList2 = new ArrayList<>();
                 List<String> total4 = new ArrayList<>();
                 total4.add("各绩效等级分布 Distribution");
                 total4.add("配置有待评审 \n" +
                 "Placement to be reviewed");
                 total4.add("在同级别发展 \n" +
                 "Development on the same level");
                 total4.add("有发展前途的人才 Promising talent");
                 total4.add("一个发展步骤 \n" +
                 "1-Step");
                 total4.add("就绪 \n" +
                 "Ready");
                 totalList2.add(total4);
                 List<String> total5 = new ArrayList<>();
                 total5.add("人数\n" +
                 "Number of Persons");
                 total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.A.getType()));
                 total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.B.getType()));
                 total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.C.getType()));
                 total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.D.getType()));
                 total5.add(getPotentialLevelSize(hrsfPmsUserBases, PotentialLevelEnum.E.getType()));
                 totalList2.add(total5);
                 List<String> total6 = new ArrayList<>();
                 total6.add("比例\n" +
                 "Percentage");
                 total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.A.getType(), hrsfPmsUserBases.size()));
                 total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.B.getType(), hrsfPmsUserBases.size()));
                 total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.C.getType(), hrsfPmsUserBases.size()));
                 total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.D.getType(), hrsfPmsUserBases.size()));
                 total6.add(getPotentialLevelSizePre(hrsfPmsUserBases, PotentialLevelEnum.E.getType(), hrsfPmsUserBases.size()));
                 totalList2.add(total6);
                 createValue2(workbook, sheet, row1 + 9, totalList2);*/
            }
        }


        /**
         * 特殊处理总览下载页面的名称
         * fixbug 问题清单389
         */
        if (flag == 2) {
            String descEnByType = AssessTypeEnum.getDescEnByType(addCalibrationUserDTO.getAssesType());
            fileName = StrUtil.format(CALIBRATION_NAME_TEMPLATE, addCalibrationUserDTO.getAssesYear()
                    , descByType, addCalibrationUserDTO.getAssesYear(), descEnByType);
        }

        this.setResponseHeader(response, fileName + ".xls");
        try {
            OutputStream os = response.getOutputStream();
            workbook.write(os);
            os.flush();
            os.close();
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String getPotentialLevelSizePre(List<HrsfPmsuserBase> hrsfPmsUserBases, String type, int rated) {
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            int size = hrsfPmsUserBases.stream().filter(e -> Objects.equals(e.getPotentialLevel(), type)
                    && Objects.nonNull(e.getTotalScore())).collect(Collectors.toList()).size();
            if (rated != 0) {
                BigDecimal multiply = BigDecimal.valueOf(size).divide(BigDecimal.valueOf(rated), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2);
                return multiply + "%";
            }
        }
        return "";
    }

    private String getPotentialLevelSize(List<HrsfPmsuserBase> hrsfPmsUserBases, String type) {
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            Long size = hrsfPmsUserBases.stream().filter(e -> Objects.equals(e.getPotentialLevel(), type)
                    && Objects.nonNull(e.getTotalScore())).count();
            return String.valueOf(size);
        }
        return "";
    }

    private String getPmsLeveSizePer(List<HrsfPmsuserBase> hrsfPmsUserBases, BigDecimal type, int rated) {
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            long size = hrsfPmsUserBases.stream().filter(e -> Objects.equals(e.getPmsLevel(), type)
                    && Objects.nonNull(e.getTotalScore())).count();
            if (rated != 0) {
                BigDecimal multiply = BigDecimal.valueOf(size).divide(BigDecimal.valueOf(rated), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2);
                return multiply + "%";
            }
            return "";
        }
        return "";
    }

    private String getPmsLeveSize(List<HrsfPmsuserBase> hrsfPmsUserBases, BigDecimal type) {
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            long size = hrsfPmsUserBases.stream().filter(e -> Objects.equals(e.getPmsLevel(), type)
                    && Objects.nonNull(e.getTotalScore())).count();
            return String.valueOf(size);
        }
        return "";
    }

    private void createValue(HSSFWorkbook workbook, Sheet sheet, int row1, String value) {
        Row row = sheet.createRow(row1);
        row.setHeight((short) 400);
        CellRangeAddress region = new CellRangeAddress(row1, row1, 0, 11);
        sheet.addMergedRegion(region);
        CellStyle rowCellStyle = getValueCellStyle(workbook);
        for (int i = 0; i <= 11; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(value);
            cell.setCellStyle(rowCellStyle);
        }
    }

    private void createValue2(HSSFWorkbook workbook, Sheet sheet, int beginRow, List<List<String>> totalList) {
        CellStyle headerCellStyle = getHeaderCellStyle(workbook);
        CellStyle valueCellStyle1 = getValueCellStyle(workbook);
        for (int i = 0; i < totalList.size(); i++) {
            Row row = sheet.createRow(beginRow + i);
            row.setHeight((short) 900);

            List<String> total = totalList.get(i);
            for (int j = 0; j < total.size(); j++) {
                CellRangeAddress region = new CellRangeAddress(beginRow + i, beginRow + i, 2 * j, 2 * j + 1);
                sheet.addMergedRegion(region);

                Cell cell = row.createCell(2 * j);
                cell.setCellValue(total.get(j));
                if (j == 0) {
                    cell.setCellStyle(headerCellStyle);
                } else {
                    cell.setCellStyle(valueCellStyle1);
                }

                Cell cell1 = row.createCell(2 * j + 1);
                cell1.setCellStyle(valueCellStyle1);
            }
        }

    }

    private void createTitle(HSSFWorkbook workbook, Sheet sheet, int row1, String value) {
        Row row = sheet.createRow(row1);
        row.setHeight((short) 400);
        CellRangeAddress region = new CellRangeAddress(row1, row1, 0, 11);
        sheet.addMergedRegion(region);
        CellStyle rowCellStyle = getHeaderCellStyle(workbook);
        for (int i = 0; i <= 11; i++) {
            Cell cell = row.createCell(i);
            cell.setCellValue(value);
            cell.setCellStyle(rowCellStyle);
        }

    }


    private void createTitle2(HSSFWorkbook workbook, Sheet sheet, int i, String value) {
        Row row = sheet.createRow(i);
        row.setHeight((short) 900);
        CellRangeAddress region = new CellRangeAddress(i, i, 0, 11);
        sheet.addMergedRegion(region);
        CellStyle rowCellStyle = getHeaderCellStyle(workbook);
        for (int j = 0; j <= 11; j++) {
            Cell cell = row.createCell(j);
            cell.setCellValue(value);
            cell.setCellStyle(rowCellStyle);
        }
    }

    private CellStyle getHeaderCellStyle(HSSFWorkbook workbook) {
        HSSFCellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        font.setFontHeightInPoints((short) 10);
        cellStyle.setFont(font);

        //取色板
        HSSFPalette palette = workbook.getCustomPalette();
        palette.setColorAtIndex(IndexedColors.LIME.getIndex(), (byte) 221, (byte) 235, (byte) 247);
        //设置颜色
        cellStyle.setFillForegroundColor(IndexedColors.LIME.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);

        //边框
        cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyle.setBorderTop(BorderStyle.THIN);//上边框
        cellStyle.setBorderRight(BorderStyle.THIN);//右边框

        return cellStyle;
    }


    private CellStyle getValueCellStyle(HSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
        font.setFontHeightInPoints((short) 10);
        cellStyle.setFont(font);

        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        //边框
        cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyle.setBorderTop(BorderStyle.THIN);//上边框
        cellStyle.setBorderRight(BorderStyle.THIN);//右边框
        return cellStyle;
    }

    private CellStyle getTitleCellStyle(HSSFWorkbook workbook) {
        CellStyle cellStyle = workbook.createCellStyle();
        org.apache.poi.ss.usermodel.Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 14);
        cellStyle.setFont(font);

        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        //边框
        cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyle.setBorderTop(BorderStyle.THIN);//上边框
        cellStyle.setBorderRight(BorderStyle.THIN);//右边框
        return cellStyle;
    }

    private CellStyle getRowCellStyle(HSSFWorkbook workbook) {

        CellStyle cellStyle = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("宋体");
        font.setFontHeightInPoints((short) 9);
        cellStyle.setFont(font);

        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setWrapText(true);
        //边框
        cellStyle.setBorderBottom(BorderStyle.THIN); //下边框
        cellStyle.setBorderLeft(BorderStyle.THIN);//左边框
        cellStyle.setBorderTop(BorderStyle.THIN);//上边框
        cellStyle.setBorderRight(BorderStyle.THIN);//右边框
        return cellStyle;
    }


    //发送响应流方法
    private void setResponseHeader(HttpServletResponse response, String fileName) throws Exception {
        String fileNameUrl = URLEncoder.encode(fileName, "UTF-8");
        //解决下载后文件名称空格变成+的问题
        fileNameUrl = fileNameUrl.replaceAll("\\+", "%20");
        response.setContentType("application/octet-stream;");
        response.setHeader("Content-disposition", "attachment;filename=" + fileNameUrl + ";" + "filename*=utf-8''" + fileNameUrl);
        response.addHeader("Pargam", "no-cache");
        response.addHeader("Cache-Control", "no-cache");
    }

    private void setCellParagraph123(XSLFTableCell xslfTextParagraphs, XSLFTableCell xslfTextParagraphs1, XSLFTableCell xslfTextParagraphs2, List<String> fullNames) {
        if (CollUtil.isNotEmpty(fullNames)) {
            for (int i = 0; i < fullNames.size(); i++) {
                if ((i + 1) % 3 == 1) {
                    setCellParagraphStr(xslfTextParagraphs, fullNames.get(i), i);
                }
                if ((i + 1) % 3 == 2) {
                    setCellParagraphStr(xslfTextParagraphs1, fullNames.get(i), i);
                }
                if ((i + 1) % 3 == 0) {
                    setCellParagraphStr(xslfTextParagraphs2, fullNames.get(i), i);
                }
            }
        }
    }

    private void setCellParagraphStr(XSLFTableCell tableCell, String fullName, int i) {
        XSLFTextParagraph paragraph;
        if (i == 0 || i == 1 || i == 2) {
            paragraph = tableCell.getTextParagraphs().get(0);
        } else {
            paragraph = tableCell.addNewTextParagraph();
        }
        paragraph.setTextAlign(LEFT);
        XSLFTextRun xslfTextRun = paragraph.addNewTextRun();
        xslfTextRun.setText(fullName);
        setTextStyle7(xslfTextRun);

    }

    private void setCellParagraph(XSLFTableCell tableCell, List<String> fullNames) {
        if (CollUtil.isNotEmpty(fullNames)) {
            for (int j = 0; j < fullNames.size(); j++) {
                XSLFTextParagraph paragraph;
                if (j == 0) {
                    paragraph = tableCell.getTextParagraphs().get(0);
                } else {
                    paragraph = tableCell.addNewTextParagraph();
                }
                paragraph.setTextAlign(LEFT);
                XSLFTextRun xslfTextRun = paragraph.addNewTextRun();
                xslfTextRun.setText(fullNames.get(j));
                setTextStyle7(xslfTextRun);

            }
        }
    }


    private List<String> getPotentialLevelStr(List<HrsfPmsuserBase> hrsfPmsUserBases) {
        List<String> potentialLevel = new ArrayList<>();
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            List<HrsfPmsuserBase> total = hrsfPmsUserBases.stream().filter(e -> Objects.nonNull(e.getTotalScore())).collect(Collectors.toList());
            Arrays.stream(PotentialLevelEnum.values()).forEach(o -> {
                List<HrsfPmsuserBase> collect = hrsfPmsUserBases.stream().filter(e -> Objects.equals(o.getType(), e.getPotentialLevel())
                        && Objects.nonNull(e.getTotalScore())
                ).collect(Collectors.toList());
                int size = CollUtil.isNotEmpty(collect) ? collect.size() : 0;
                BigDecimal divide;
                if (total.size() != 0) {
                    divide = BigDecimal.valueOf(size).divide(BigDecimal.valueOf(total.size()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2);
                } else {
                    divide = BigDecimal.valueOf(0);
                }
                String s = size + "(" + divide + "%)";
                if (Objects.equals(o.name(), PotentialLevelEnum.B.name())) {
                    potentialLevel.add(s);
                    potentialLevel.add(s);
                    potentialLevel.add(s);
                } else {
                    potentialLevel.add(s);
                }
            });
            return potentialLevel;
        }
        return potentialLevel;
    }

    private Map<String, List<String>> getPmsLeveNameStr(List<HrsfPmsuserBase> hrsfPmsUserBases,String assesYear) {
        Map<String, List<String>> pmsLevelStr = new HashMap<>(6);
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            log.info("getPmsLeveNameStr|assesYear = {}", assesYear);
            int year = 2025; // 默认值
            if (StringUtils.isNotBlank(assesYear)) {
                year = Integer.parseInt(assesYear);
            }
            if(year < 2025){
                Arrays.stream(PmsLevelEnum.values()).forEach(o -> {
                    List<HrsfPmsuserBase> collect = hrsfPmsUserBases.stream().filter(e -> Objects.equals(o.getType(), e.getPmsLevel())
                            && Objects.nonNull(e.getTotalScore())).collect(Collectors.toList());
                List<String> list = new ArrayList<>();
                if (CollUtil.isEmpty(collect)) {
                    list.add("");
                } else {
                    List<HrsfPmsuserBase> collect1 = collect.stream().sorted(Comparator.comparing(HrsfPmsuserBase::getTotalScore).reversed()).collect(Collectors.toList());
                    List<String> listStr = getListStr(collect1);
                    list.addAll(listStr);
                }

                if (Objects.equals(o.name(), PmsLevelEnum.Successful.name()) || Objects.equals(o.name(), PmsLevelEnum.Excellent.name())) {
                    if (list.size() > 1) {
                        List<HrsfPmsuserBase> collect1 = collect.stream().sorted(Comparator.comparing(HrsfPmsuserBase::getTotalScore).reversed()).collect(Collectors.toList());
                        int i = collect1.size() % 2 == 1 ? collect1.size() / 2 + 1 : collect1.size() / 2;
                        List<HrsfPmsuserBase> hrsfPmsuserBasesMax = collect1.subList(0, i);
                        List<HrsfPmsuserBase> hrsfPmsuserBasesMin = collect1.subList(i, collect1.size());
                        pmsLevelStr.put(o.name() + "Max", getListStr(hrsfPmsuserBasesMax));
                        pmsLevelStr.put(o.name() + "Min", getListStr(hrsfPmsuserBasesMin));
                    } else {
                        pmsLevelStr.put(o.name() + "Max", list);
                        pmsLevelStr.put(o.name() + "Min", new ArrayList<>());
                    }
                } else {
                    pmsLevelStr.put(o.name(), list);
                }
                });
            }else{
                Arrays.stream(PmsLevelEnumNew.values()).forEach(o -> {
                    List<HrsfPmsuserBase> collect = hrsfPmsUserBases.stream()
                            .filter(e -> Objects.equals(o.getType(), e.getPmsLevel())
                                    && Objects.nonNull(e.getTotalScore()))
                            .collect(Collectors.toList());
                    List<String> list = new ArrayList<>();
                    if (CollUtil.isEmpty(collect)) {
                        list.add("");
                    } else {
                        List<HrsfPmsuserBase> collect1 = collect.stream()
                                .sorted(Comparator.comparing(HrsfPmsuserBase::getTotalScore).reversed())
                                .collect(Collectors.toList());
                        List<String> listStr = getListStr(collect1);
                        list.addAll(listStr);
                    }

                    if (Objects.equals(o.name(), 
                            PmsLevelEnumNew.Successful.name())
                            || Objects.equals(o.name(), PmsLevelEnumNew.Excellent.name())) {
                        if (list.size() > 1) {
                            List<HrsfPmsuserBase> collect1 = collect.stream()
                                    .sorted(Comparator.comparing(HrsfPmsuserBase::getTotalScore).reversed())
                                    .collect(Collectors.toList());
                            int i = collect1.size() % 2 == 1 ? collect1.size() / 2 + 1 : collect1.size() / 2;
                            List<HrsfPmsuserBase> hrsfPmsuserBasesMax = collect1.subList(0, i);
                            List<HrsfPmsuserBase> hrsfPmsuserBasesMin = collect1.subList(i, collect1.size());
                            pmsLevelStr.put(o.name() + "Max", getListStr(hrsfPmsuserBasesMax));
                            pmsLevelStr.put(o.name() + "Min", getListStr(hrsfPmsuserBasesMin));
                        } else {
                            pmsLevelStr.put(o.name() + "Max", list);
                            pmsLevelStr.put(o.name() + "Min", new ArrayList<>());
                        }
                    } else {
                        pmsLevelStr.put(o.name(), list);
                    }
                });
            }
        }

        return pmsLevelStr;
    }

    private List<String> getListStr(List<HrsfPmsuserBase> collect) {
        List<String> list = new ArrayList<>();
        collect.forEach(e -> {
            String score = Objects.nonNull(e.getTotalScore()) ? e.getTotalScore() + "%" : "0%";
            String fullName = Objects.nonNull(e.getFullName()) ? e.getFullName() : "";
            String str = score + " " + fullName;
            list.add(str);
        });
        return list;
    }

    private List<String> getPmsLeveStr(List<HrsfPmsuserBase> hrsfPmsUserBases,String assesYear) {
        List<String> pmsLevelStr = new ArrayList<>();
        if (CollUtil.isNotEmpty(hrsfPmsUserBases)) {
            log.info("getPmsLeveStr|assesYear = {}", assesYear);
            int year = 2025; // 默认值
            if (StringUtils.isNotBlank(assesYear)) {
                year = Integer.parseInt(assesYear);
            }
            if(year < 2025){
                List<HrsfPmsuserBase> total = hrsfPmsUserBases.stream().filter(e -> Objects.nonNull(e.getTotalScore())).collect(Collectors.toList());
                Arrays.stream(PmsLevelEnum.values()).forEach(o -> {
                List<HrsfPmsuserBase> collect = hrsfPmsUserBases.stream().filter(e -> Objects.equals(o.getType(), e.getPmsLevel())
                        && Objects.nonNull(e.getTotalScore())).collect(Collectors.toList());
                int size = CollUtil.isNotEmpty(collect) ? collect.size() : 0;
                BigDecimal divide;
                if (total.size() != 0) {
                    divide = BigDecimal.valueOf(size).divide(BigDecimal.valueOf(hrsfPmsUserBases.size()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2);
                } else {
                    divide = BigDecimal.valueOf(0);
                }
                String s = size + "(" + divide + "%)";
                if (Objects.equals(o.name(), PmsLevelEnum.Successful.name()) || Objects.equals(o.name(), PmsLevelEnum.Excellent.name())) {
                    pmsLevelStr.add(s);
                    pmsLevelStr.add(s);
                } else {
                    pmsLevelStr.add(s);
                }
                });
            }else{
                List<HrsfPmsuserBase> total = hrsfPmsUserBases.stream().filter(e -> Objects.nonNull(e.getTotalScore()))
                        .collect(Collectors.toList());
                Arrays.stream(PmsLevelEnumNew.values()).forEach(o -> {
                    List<HrsfPmsuserBase> collect = hrsfPmsUserBases.stream()
                            .filter(e -> Objects.equals(o.getType(), e.getPmsLevel())
                                    && Objects.nonNull(e.getTotalScore()))
                            .collect(Collectors.toList());
                    int size = CollUtil.isNotEmpty(collect) ? collect.size() : 0;
                    BigDecimal divide;
                    if (total.size() != 0) {
                        divide = BigDecimal.valueOf(size)
                                .divide(BigDecimal.valueOf(hrsfPmsUserBases.size()), 4, RoundingMode.HALF_UP)
                                .multiply(BigDecimal.valueOf(100)).setScale(2);
                    } else {
                        divide = BigDecimal.valueOf(0);
                    }
                    String s = size + "(" + divide + "%)";
                    if (Objects.equals(o.name(), PmsLevelEnumNew.Successful.name())
                            || Objects.equals(o.name(), PmsLevelEnumNew.Excellent.name())) {
                        pmsLevelStr.add(s);
                        pmsLevelStr.add(s);
                    } else {
                        pmsLevelStr.add(s);
                    }
                });
            }
            return pmsLevelStr;
        }
        return pmsLevelStr;
    }


    private void setRowOne(XSLFTable xslfTable, int cellNum, List<String> pmsLeveStr) {
        XSLFTableRow xslfTableCells = xslfTable.getRows().get(1);
        List<XSLFTableCell> cells = xslfTableCells.getCells();
        for (int i = 0; i < pmsLeveStr.size(); i++) {
            XSLFTableCell tableCell = cells.get(i + cellNum);
            List<XSLFTextParagraph> textParagraphs = tableCell.getTextParagraphs();
            if(CollUtil.isNotEmpty(textParagraphs)){
                XSLFTextParagraph paragraph = tableCell.getTextParagraphs().get(0);
                paragraph.setTextAlign(CENTER);
                XSLFTextRun xslfTextRun = paragraph.addNewTextRun();
                xslfTextRun.setText(pmsLeveStr.get(i));
                setTextStyle8(xslfTextRun);
            }
        }
    }

    /**
     * 设置字体样式
     */
    private static void setTextStyle(XSLFTextRun xslfTextRun) {
        xslfTextRun.setFontSize(9.0);
        xslfTextRun.setBold(false);
        xslfTextRun.setFontColor(Color.BLACK);
    }

    private static void setTextStyle14(XSLFTextRun xslfTextRun) {
        xslfTextRun.setFontSize(14.0);
        xslfTextRun.setBold(false);
        xslfTextRun.setFontColor(Color.BLACK);
    }

    private static void setTextStyle8(XSLFTextRun xslfTextRun) {
        xslfTextRun.setFontSize(8.0);
        xslfTextRun.setBold(false);
        xslfTextRun.setFontColor(Color.BLACK);
    }

    private static void setTextStyle7(XSLFTextRun xslfTextRun) {
        xslfTextRun.setFontSize(7.0);
        xslfTextRun.setBold(false);
        xslfTextRun.setFontColor(Color.BLACK);
    }
}
