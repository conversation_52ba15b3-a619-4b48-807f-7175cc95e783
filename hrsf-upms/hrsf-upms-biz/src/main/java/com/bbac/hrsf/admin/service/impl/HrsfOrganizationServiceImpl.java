package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfOrganization;
import com.bbac.hrsf.admin.api.entity.HrsfPmsWriteDataBackLog;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.mapper.HrsfOrganizationMapper;
import com.bbac.hrsf.admin.service.IHrsfOrganizationService;
import com.bbac.hrsf.admin.service.IHrsfPmsWriteDataBackLogService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.EmailStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.ErrorFlag;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import com.bbac.hrsf.performance.api.feign.RemoteEmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HrsfOrganizationServiceImpl extends ServiceImpl<HrsfOrganizationMapper, HrsfOrganization> implements IHrsfOrganizationService {

    @Value("${sf.sync.username}")
    private String userName;
    @Value("${sf.sync.pwd}")
    private String pwd;
    @Value("${sf.sync.operation.email}")
    private String operationEmail;

    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;
    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;

    @Value("${sf.sync.processInactiveEmployees}")
    private String processInactiveEmployees;

    private static final String UPDATE_PMS_FORMATTER = "User(userId='{}')";
    private final String CONTENT_TRANS = "最终总分Final Rating: {}%; 业绩等级Performance Rank: {}";

    private final HrsfOrganizationMapper hrsfOrganizationMapper;

    private final IHrsfPmsWriteDataBackLogService hrsfPmsWriteDataBackLogService;

    private final RemoteEmailService remoteEmailService;

    private final IHrsfPmsuserBaseService hrsfPmsuserBaseService;

    private static final String CALIBRATION_NAME_TEMPLATE = "{}年{}考核 {} {} Review";

    @Override
    @Deprecated
    public HrsfOrganizationVO selectOneData() {
        /**
         * 注释掉
         * Set<String> systemList = hrsfOrganizationMapper.selectSystem();
         Set<String> departmentList = hrsfOrganizationMapper.selectDepartment();
         Set<String> sectionList = hrsfOrganizationMapper.selectSection();
         Set<String> userGroupList = hrsfOrganizationMapper.selectUserGroup();
         Set<String> userLevelList = hrsfOrganizationMapper.selectUserLevel();

         return new HrsfOrganizationVO(systemList, departmentList, sectionList, userGroupList, userLevelList);*/

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public void syncOrganizationHandler() {

        /**
         * 1、首先从员工表身上获取组织架构信息
         * 2、删除HRSF_ORGANIZATION的数据
         * 3、新增数据
         */
        /*List<String> systemList = hrsfOrganizationMapper.selectSystem();
        List<String> departmentList = hrsfOrganizationMapper.selectDepartment();
        List<String> sectionList = hrsfOrganizationMapper.selectSection();
        List<String> userGroupList = hrsfOrganizationMapper.selectUserGroup();
        List<String> userLevelList = hrsfOrganizationMapper.selectUserLevel();
        hrsfOrganizationMapper.delTable();
        save(new HrsfOrganization().setSystem(StrUtil.join("##", systemList)).setDepartment(StrUtil.join("##", departmentList))
                .setSection(StrUtil.join("##", sectionList))
                .setUserGroup(StrUtil.join("##", userGroupList))
                .setUserLevel(StrUtil.join("##", userLevelList)));*/

    }

    @Override
    public void syncFinalWriteDataBackErrorHandler() {
        List<HrsfPmsWriteDataBackLog> hrsfPmsWriteDataBackLogs = hrsfPmsWriteDataBackLogService.selectByEmailStatus();
        List<HrsfPmsWriteDataBackLog> twoList = hrsfPmsWriteDataBackLogs.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.TWO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(twoList)) {
            List<WriteDataBackDTO> writeDataBackDTOS3 = new ArrayList<>();
            twoList.forEach(o -> {
                String errorInfo = o.getErrorInfo();
                WriteDataBackDTO writeDataBackDTO = JSONObject.parseObject(errorInfo, WriteDataBackDTO.class);
                writeDataBackDTO.setEmailStatus(EmailStatusEnum.THREE.getType());
                writeDataBackDTO.setFlag(o.getFlag());
                writeDataBackDTO.setId(o.getId());
                writeDataBackDTOS3.add(writeDataBackDTO);
            });
            retryWriteBack(writeDataBackDTOS3);
        }

        List<HrsfPmsWriteDataBackLog> oneList = hrsfPmsWriteDataBackLogs.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ONE.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(oneList)) {
            List<WriteDataBackDTO> writeDataBackDTOS2 = new ArrayList<>();
            oneList.forEach(o -> {
                String errorInfo = o.getErrorInfo();
                WriteDataBackDTO writeDataBackDTO = JSONObject.parseObject(errorInfo, WriteDataBackDTO.class);
                writeDataBackDTO.setEmailStatus(EmailStatusEnum.TWO.getType());
                writeDataBackDTO.setId(o.getId());
                writeDataBackDTO.setFlag(o.getFlag());
                writeDataBackDTOS2.add(writeDataBackDTO);
            });
            retryWriteBack(writeDataBackDTOS2);
        }

        List<HrsfPmsWriteDataBackLog> zeroList = hrsfPmsWriteDataBackLogs.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ZERO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(zeroList)) {
            List<WriteDataBackDTO> writeDataBackDTOS = new ArrayList<>();
            zeroList.forEach(o -> {
                String errorInfo = o.getErrorInfo();
                WriteDataBackDTO writeDataBackDTO = JSONObject.parseObject(errorInfo, WriteDataBackDTO.class);
                writeDataBackDTO.setEmailStatus(EmailStatusEnum.ONE.getType());
                writeDataBackDTO.setFlag(o.getFlag());
                writeDataBackDTO.setId(o.getId());
                writeDataBackDTOS.add(writeDataBackDTO);

            });
            retryWriteBack(writeDataBackDTOS);
        }
    }

    @Override
    public void syncPmsJobHandlerAgain() {
        List<String> type = new ArrayList<>();
        type.add(EmailStatusEnum.ZERO.getType());
        type.add(EmailStatusEnum.ONE.getType());
        type.add(EmailStatusEnum.TWO.getType());
        List<HrsfPmsWriteDataBackLog> list = hrsfPmsWriteDataBackLogService.list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery().in(HrsfPmsWriteDataBackLog::getEmailStatus, type).eq(HrsfPmsWriteDataBackLog::getFlag, ErrorFlag.syncPmsJobHandler.getType()));
        List<HrsfPmsWriteDataBackLog> collect = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.TWO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            collect.forEach(o -> {
                String errorInfo = o.getErrorInfo();
                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                String key = jsonObject.getStr("key");
                List<String> value = jsonObject.getJSONArray("value").toList(String.class);
                putAgain(key, value, EmailStatusEnum.THREE.getType(), o.getId());
            });
        }


        List<HrsfPmsWriteDataBackLog> collect3 = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ONE.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect3)) {
            collect3.forEach(o -> {
                String errorInfo = o.getErrorInfo();
                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                String key = jsonObject.getStr("key");
                List<String> value = jsonObject.getJSONArray("value").toList(String.class);
                putAgain(key, value, EmailStatusEnum.THREE.getType(), o.getId());
            });
        }

        List<HrsfPmsWriteDataBackLog> collect4 = list.stream().filter(e -> Objects.equals(e.getEmailStatus(), EmailStatusEnum.ZERO.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect4)) {
            collect4.forEach(o -> {
                String errorInfo = o.getErrorInfo();
                cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(errorInfo);
                String key = jsonObject.getStr("key");
                List<String> value = jsonObject.getJSONArray("value").toList(String.class);
                putAgain(key, value, EmailStatusEnum.ONE.getType(), o.getId());
            });
        }

    }

    /**
     * 这里发送邮件的逻辑统一迁移到另一个job任务跑
     */
    @Override
    public void retryErrorSendEmail() {
        List<HrsfPmsWriteDataBackLog> list = hrsfPmsWriteDataBackLogService.list(Wrappers.<HrsfPmsWriteDataBackLog>lambdaQuery()
                .eq(HrsfPmsWriteDataBackLog::getEmailStatus, EmailStatusEnum.THREE.getType()));
        List<HrsfPmsWriteDataBackLog> syncErrorList = list.stream().filter(e -> Objects.equals(e.getFlag(), ErrorFlag.SYNC.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(syncErrorList)) {
            log.info("主数据传输运行失败，发送邮件");
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setSendTo(operationEmail);
            emailInfoDTO.setSubject("主数据接口程序运行失败");
            emailInfoDTO.setTemplate("sync_user_template.html");
            List<Long> idList = syncErrorList.stream().map(HrsfPmsWriteDataBackLog::getId).collect(Collectors.toList());
            Map<String, Object> model = new HashMap<>(16);
            model.put("errorListSize", syncErrorList.size());
            emailInfoDTO.setModel(model);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
            hrsfPmsWriteDataBackLogService.updateEmailStatusById(EmailStatusEnum.SEND.getType(), idList);
        }
        List<HrsfPmsWriteDataBackLog> syncPmsErrorList = list.stream().filter(e -> Objects.equals(e.getFlag(), ErrorFlag.syncPmsJobHandler.getType())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(syncPmsErrorList)) {
            log.info("劳资员数据回传失败，发送邮件");
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setSendTo(operationEmail);
            emailInfoDTO.setSubject("劳资员数据回写程序运行失败");
            emailInfoDTO.setTemplate("sync_pms_template.html");
            HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = syncPmsErrorList.get(0);
            String calibrationName = "";
            if (hrsfPmsWriteDataBackLog != null) {
                String descByType = AssessTypeEnum.getDescByType(hrsfPmsWriteDataBackLog.getAssessType());
                calibrationName = StrUtil.format(CALIBRATION_NAME_TEMPLATE, hrsfPmsWriteDataBackLog.getAssessYear()
                        , descByType);
            }
            List<Long> idList = syncPmsErrorList.stream().map(HrsfPmsWriteDataBackLog::getId).collect(Collectors.toList());
            Map<String, Object> model = new HashMap<>(16);
            model.put("errorListSize", syncPmsErrorList.size());
            model.put("calibrationName", calibrationName);
            emailInfoDTO.setModel(model);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
            hrsfPmsWriteDataBackLogService.updateEmailStatusById(EmailStatusEnum.SEND.getType(), idList);
        }
        List<HrsfPmsWriteDataBackLog> writeDataErrorList = list.stream().filter(e ->
                !(Objects.equals(e.getFlag(), ErrorFlag.syncPmsJobHandler.getType()) || Objects.equals(e.getFlag(), ErrorFlag.SYNC.getType())))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(writeDataErrorList)) {
            log.info("存在回写数据失败，发送邮件");
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setSendTo(operationEmail);
            emailInfoDTO.setSubject("绩效考核结果回写程序运行失败");
            emailInfoDTO.setTemplate("data_back_template.html");
            HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = writeDataErrorList.get(0);
            String calibrationName = "";
            if (hrsfPmsWriteDataBackLog != null) {
                String descByType = AssessTypeEnum.getDescByType(hrsfPmsWriteDataBackLog.getAssessType());
                calibrationName = StrUtil.format(CALIBRATION_NAME_TEMPLATE, hrsfPmsWriteDataBackLog.getAssessYear()
                        , descByType);
            }
            if (!syncPmsErrorList.isEmpty()) {
                List<Long> idList = writeDataErrorList.stream().map(HrsfPmsWriteDataBackLog::getId).collect(Collectors.toList());
                Map<String, Object> model = new HashMap<>(16);
                model.put("errorListSize", syncPmsErrorList.size());
                model.put("calibrationName", calibrationName);
                emailInfoDTO.setModel(model);
                remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
                hrsfPmsWriteDataBackLogService.updateEmailStatusById(EmailStatusEnum.SEND.getType(), idList);
            }
        }
    }

    private void putAgain(String key, List<String> values, String type, Long id) {
        String format = StrUtil.format(processInactiveEmployees);

        Map<String, Object> paramMap = new ConcurrentHashMap<>();
        Map<String, Object> subParamMap = new ConcurrentHashMap<>();
        paramMap.put("__metadata", subParamMap);
        subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORMATTER, key));
        ArrayList<Object> subTParamMapList = new ArrayList<>();
        values.stream().forEach(value -> {
            Map<String, Object> subTTParamMap = new ConcurrentHashMap<>(16);
            Map<String, Object> subTParamMap = new ConcurrentHashMap<>(16);
            subTTParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORMATTER, value));
            subTParamMap.put("__metadata", subTTParamMap);
            subTParamMapList.add(subTParamMap);
        });
        paramMap.put("customManager", subTParamMapList);
        String body = null;
        try {
            body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(userName, pwd).execute().body();
            cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(body);
            cn.hutool.json.JSONObject returnJson = (cn.hutool.json.JSONObject) jsonObject.getJSONArray("d").get(0);
            String status = returnJson.getStr("status");
            /**
             * 如果失败记录日志,继续执行其他的数据
             */
            if (!"OK".equals(status)) {
                log.error("同步绩效协调员到SF系统失败:{},入参值:{}", body, JSONUtil.toJsonStr(values));
                saveErrorLog(key, values, body, id, type);
            } else {
                saveErrorLog(key, values, body, id, EmailStatusEnum.SUCCESS.getType());
                log.info("同步绩效协调员到SF系统成功,入参值:{}", JSONUtil.toJsonStr(values));
            }
        } catch (Exception e) {
            saveErrorLog(key, values, body, id, type);
        }
    }

    private void saveErrorLog(String key, List<String> values, String body, Long id, String type) {
        HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
        Map<String, Object> map = new HashMap<>();
        map.put("key", key);
        map.put("value", values);
        hrsfPmsWriteDataBackLog.setId(id);
        hrsfPmsWriteDataBackLog.setEmailStatus(type);
        hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.syncPmsJobHandler.getType());
        hrsfPmsWriteDataBackLog.setResponseBody(body);
        hrsfPmsWriteDataBackLog.setErrorInfo(JSONUtil.toJsonStr(map));
        hrsfPmsWriteDataBackLog.setName(key);
        hrsfPmsWriteDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
    }

    private void retryWriteBack(List<WriteDataBackDTO> writeDataBackDTOS3) {
        hrsfPmsuserBaseService.writeDataBack(writeDataBackDTOS3.stream().filter(e -> Objects.equals("1", e.getFlag())).collect(Collectors.toList()));
        hrsfPmsuserBaseService.whiteYearWriteDataBack(writeDataBackDTOS3.stream().filter(e -> Objects.equals("2", e.getFlag())).collect(Collectors.toList()));
        hrsfPmsuserBaseService.whiteYearFinalWriteDataBack(writeDataBackDTOS3.stream().filter(e -> Objects.equals("3", e.getFlag())).collect(Collectors.toList()));
        hrsfPmsuserBaseService.finalWriteDataBack(writeDataBackDTOS3.stream().filter(e -> Objects.equals("4", e.getFlag())).collect(Collectors.toList()));
    }
}
