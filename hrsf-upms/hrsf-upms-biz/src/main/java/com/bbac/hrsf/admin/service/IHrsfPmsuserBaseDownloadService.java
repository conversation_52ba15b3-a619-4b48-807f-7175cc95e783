package com.bbac.hrsf.admin.service;

import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;

import javax.servlet.http.HttpServletResponse;

/**
 * @author: liu, jie
 * @create: 2022-06-20
 **/
public interface IHrsfPmsuserBaseDownloadService {

    void reportCalibrationExcel(AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception;

    void reportOverviewExcel(AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception;

    void reportDownloadPpt(AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response);


}
