package com.bbac.hrsf.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCard;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface HrsfPmsTalentCardMapper extends BaseMapper<HrsfPmsTalentCard> {
    @Select("select * from (select * from HRSF_PMS_USER_TALENT_CARD where STAFF_ID = #{staffId} order by creation_date desc) WHERE　ROWNUM = 1")
    HrsfPmsTalentCard selectTalentCardByStaffId(@Param("staffId") String staffId);
}
