package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserDTO;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserUpdateDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUser;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserVO;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
public interface IHrsfPmsUserService extends IService<HrsfPmsUser> {
    IPage<HrsfPmsUser> getUserPageInfo(Page page, HrsfPmsUserDTO hrsfPmsUserDTO);

    Boolean updatePmsUser(HrsfPmsUserUpdateDTO updateDTO);

    HrsfPmsUserVO getPmsUserInfo(Long pmsUserId);
}
