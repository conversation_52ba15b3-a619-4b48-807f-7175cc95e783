package com.bbac.hrsf.admin.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserScoreTaskQueryDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserScoreTask;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskCountingVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTemplateVo;
import com.bbac.hrsf.admin.handle.SFApiManager;
import com.bbac.hrsf.admin.mapper.HrsfPmsUserScoreTaskMapper;
import com.bbac.hrsf.admin.service.IHrsfUserScoreTaskService;
import com.bbac.hrsf.common.core.constant.CacheConstants;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.exception.ServiceException;
import com.bbac.hrsf.common.mybatis.config.MybatisPlusMetaObjectHandler;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class HrsfUserScoreTaskServiceImpl  extends ServiceImpl<HrsfPmsUserScoreTaskMapper, HrsfPmsUserScoreTask> implements IHrsfUserScoreTaskService {

    @Value("${sf.sync.usernameSDAPI:SDAPI@beijingbenT1}")
    private String sfUserName;
    /** 年度小分指标 itemId1：业绩-工作任务完成 Performance-Completion of work */
    @Value("${sf.sync.itemId1}")
    private String sfYearScoreItemId1;
    /** 年度小分指标 itemId2：业绩-质量目标 Performance-Work Quality */
    @Value("${sf.sync.itemId2}")
    private String sfYearScoreItemId2;
    /** 年度小分指标 itemId3： 业绩-安全生产 Performance-Production Safety*/
    @Value("${sf.sync.itemId3}")
    private String sfYearScoreItemId3;
    /** 年度小分指标 itemId4：业绩-出勤 Performance-Attendance */
    @Value("${sf.sync.itemId4}")
    private String sfYearScoreItemId4;
    /** 年度小分指标 itemId5：业绩-成本控制 Performance-Cost Control */
    @Value("${sf.sync.itemId5}")
    private String sfYearScoreItemId5;
    /** 年度小分指标 itemId6：能力-人际沟通 Ability-Communication */
    @Value("${sf.sync.itemId6}")
    private String sfYearScoreItemId6;
    /** 年度小分指标 itemId7：能力-团队协作 Ability-Team Work */
    @Value("${sf.sync.itemId7}")
    private String sfYearScoreItemId7;
    /** 年度小分指标 itemId8：能力-专业技能 Ability-Professional */
    @Value("${sf.sync.itemId8}")
    private String sfYearScoreItemId8;
    /** 年度小分指标 itemId9：态度-敬业负责 Attitude-Responsibility */
    @Value("${sf.sync.itemId9}")
    private String sfYearScoreItemId9;
    /** 年度小分指标 itemId10：态度-严谨规范 Attitude-Discipline */
    @Value("${sf.sync.itemId10}")

    private String sfYearScoreItemId10;
    /** 蓝领季度小分指标 itemId11：质量目标 Work Quality */
    @Value("${sf.sync.itemId11}")
    private String sfBlueQuarterScoreItemId1;
    /** 蓝领季度小分指标 itemId12：成本控制 Cost Control */
    @Value("${sf.sync.itemId12}")
    private String sfBlueQuarterScoreItemId2;
    /** 蓝领季度小分指标 itemId13：工作任务完成 Completion of Work */
    @Value("${sf.sync.itemId13}")
    private String sfBlueQuarterScoreItemId3;
    /** 蓝领季度小分指标 itemId14：安全生产 Production Safety */
    @Value("${sf.sync.itemId14}")
    private String sfBlueQuarterScoreItemId4;
    /** 蓝领季度小分指标 itemId15：出勤 Attendance */
    @Value("${sf.sync.itemId15}")
    private String sfBlueQuarterScoreItemId5;

    /** 白领季度小分指标 itemId16：质量目标 Work Quality */
    @Value("${sf.sync.itemId16}")
    private String sfWhiteQuarterScoreItemId1;
    /** 白领季度小分指标 itemId17：成本控制 Cost Control */
    @Value("${sf.sync.itemId17}")
    private String sfWhiteQuarterScoreItemId2;
    /** 白领季度小分指标 itemId18：工作任务完成 Completion of Work */
    @Value("${sf.sync.itemId18}")
    private String sfWhiteQuarterScoreItemId3;
    /** 白领季度小分指标 itemId19：安全生产 Production Safety */
    @Value("${sf.sync.itemId19}")
    private String sfWhiteQuarterScoreItemId4;
    /** 白领季度小分指标 itemId20：出勤 Attendance */
    @Value("${sf.sync.itemId20}")
    private String sfWhiteQuarterScoreItemId5;

    private final String assessDescFmt = "%s %s\n%s %s";
    private final String sfUploadFileNameFmt = "【%s】%s-%s- Historical Data of %s Performance Evaluation Results%s员工历史绩效评估结果汇总表.xlsx";


    private final HrsfPmsUserScoreTaskMapper hrsfPmsUserScoreTaskMapper;
    private final MybatisPlusMetaObjectHandler mybatisPlusMetaObjectHandler;
    private final SFApiManager sfApiManager;
    private final RedisTemplate redisTemplate;
    // 历史绩效分数任务线程池
    private final ThreadPoolTaskExecutor userScoreTaskExecutor;

    @Override
    public String startUserScoreTask(Long currTaskId) {
        // 从缓存判断任务是否在执行
        Object cacheId = redisTemplate.opsForValue().get(CacheConstants.CURRENT_USER_SCORE_TASK_ID_HIS);
        if(cacheId != null){
            log.info("startUserScoreTask processing, cacheId:{}", cacheId);
            // 任务处理中
            return CommonConstants.PROCESSING_MESSAGE;
        }
        // 获取当前任务状态
        HrsfPmsUserScoreTask currentScoreTask = getCurrentTaskStatus();
        Long taskId = currentScoreTask != null ? currentScoreTask.getId() : null;
        // 当前操作人及时间
        String userName = mybatisPlusMetaObjectHandler.getUserName();
        LocalDateTime operateTime = LocalDateTime.now();
        HrsfPmsUserScoreTask scoreTask = null;
        if(currTaskId != null){
            // 重新推送失败任务
            if(!currTaskId.equals(taskId) || currentScoreTask.getStatus() != 3) {
                log.info("UserScoreTask invalid operation, taskId: {}", taskId);
                return String.format("当前任务状态或任务ID不正确，不可以操作 Invalid operation due to invalid task id(%d)", taskId);
            }
            scoreTask = currentScoreTask;
            // 初始化任务明细
            hrsfPmsUserScoreTaskMapper.batchUpdateFailedTaskDetail(taskId,0, userName, operateTime);
            // 回写明细数量及最大任务Id
            HrsfPmsUserScoreTaskCountingVo taskCountingVo = hrsfPmsUserScoreTaskMapper.obtainTaskDetailCounting(taskId);
            scoreTask.setMaxDetailId(taskCountingVo.getTaskDetailMaxId());
            scoreTask.setStatus(1);
            scoreTask.setUpdateBy(userName);
            scoreTask.setUpdateTime(operateTime);
            hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
            log.info("UserScoreTask restarted, taskId: {}", taskId);
        } else {
            // 新绩效汇总任务
            if(currentScoreTask != null && currentScoreTask.getStatus() == 3){
                // 终止失败的任务
                hrsfPmsUserScoreTaskMapper.batchUpdateFailedTaskDetail(taskId,4, userName, operateTime);
                currentScoreTask.setStatus(4);
                currentScoreTask.setUpdateBy(userName);
                currentScoreTask.setUpdateTime(operateTime);
                hrsfPmsUserScoreTaskMapper.updateById(currentScoreTask);
            }
            scoreTask = new HrsfPmsUserScoreTask();
            scoreTask.setTaskType(1);
            scoreTask.setStatus(1);
            scoreTask.setStartTime(operateTime);
            if(currentScoreTask == null){
                scoreTask.setAssesYear("All");
                scoreTask.setAssesType("All");
                scoreTask.setFormDataPullFlag(1);
            } else {
                // 当前绩效考核
                HrsfPmstart lastHrsfPmstart = hrsfPmsUserScoreTaskMapper.getLastHrsfPmstart();
                // 判断是否需要从SF拉去表单数据
                if(lastHrsfPmstart.getAssesYear().equals(currentScoreTask.getAssesYear())
                        && lastHrsfPmstart.getAssesType().equals(currentScoreTask.getAssesYear())){
                    scoreTask.setFormDataPullFlag(2);
                } else {
                    scoreTask.setFormDataPullFlag(1);
                }
                scoreTask.setAssesYear("5Years");
                scoreTask.setAssesType(lastHrsfPmstart.getAssesType());
            }
            // 创建任务
            hrsfPmsUserScoreTaskMapper.insert(scoreTask);
            taskId = scoreTask.getId();
            log.debug("===debug insert_scoreTask, taskId:{}", taskId);
            // 初始化任务明细
            hrsfPmsUserScoreTaskMapper.initTaskDetail(taskId, userName, operateTime);
            log.info("startUserScoreTask initTaskDetail taskId:{}, userName:{}, operateTime:{}", taskId, userName, operateTime);
            // 回写明细数量及最大任务Id
            HrsfPmsUserScoreTaskCountingVo taskCountingVo = hrsfPmsUserScoreTaskMapper.obtainTaskDetailCounting(taskId);
            scoreTask.setProcessNumber(taskCountingVo.getTaskDetailCount());
            scoreTask.setMaxDetailId(taskCountingVo.getTaskDetailMaxId());
            hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
            log.info("UserScoreTask started, taskId: {}", taskId);
        }
        // 缓存任务状态
        redisTemplate.opsForValue().set(CacheConstants.CURRENT_USER_SCORE_TASK_ID_HIS, taskId,
                CacheConstants.CURRENT_USER_SCORE_TASK_ID_CACHE_TIME, TimeUnit.MINUTES);
        HrsfPmsUserScoreTask finalScoreTask = scoreTask;
        CompletableFuture.runAsync(() -> handleHrsfPmsUserScoreTask(finalScoreTask));
        return CommonConstants.SUCCESS_FLAG;
    }

    /**
     * 检查历史绩效任务
     */
    public void checkUserScoreTask(){
        // 从缓存判断任务是否在执行
        Object cacheId = redisTemplate.opsForValue().get(CacheConstants.CURRENT_USER_SCORE_TASK_ID_HIS);
        if(cacheId != null){
            return;
        }
        HrsfPmsUserScoreTask currentTask = getCurrentTaskStatus();
        if(currentTask == null || currentTask.getStatus() != 1){
            return;
        }
        Long taskId = currentTask.getId();
        log.info("checkScoreHistoryTaskStatus restart, taskId:{}", currentTask.getId());
        // 当前操作人及时间
        String userName = mybatisPlusMetaObjectHandler.getUserName();
        LocalDateTime operateTime = LocalDateTime.now();
        // 更新阻塞任务明细状态
        hrsfPmsUserScoreTaskMapper.batchUpdatePendingTaskDetail(taskId, userName, operateTime);
        // 回写明细数量及最大任务Id
        HrsfPmsUserScoreTaskCountingVo taskCountingVo = hrsfPmsUserScoreTaskMapper.obtainTaskDetailCounting(taskId);
        currentTask.setMaxDetailId(taskCountingVo.getTaskDetailMaxId());
        currentTask.setStatus(1);
        currentTask.setUpdateBy(userName);
        currentTask.setUpdateTime(operateTime);
        hrsfPmsUserScoreTaskMapper.updateById(currentTask);
        // 缓存任务状态
        redisTemplate.opsForValue().set(CacheConstants.CURRENT_USER_SCORE_TASK_ID_HIS, taskId,
                CacheConstants.CURRENT_USER_SCORE_TASK_ID_CACHE_TIME, TimeUnit.MINUTES);
        CompletableFuture.runAsync(() -> handleHrsfPmsUserScoreTask(currentTask));
    }

    @Override
    public HrsfPmsUserScoreTask getCurrentTaskStatus() {
        return hrsfPmsUserScoreTaskMapper.selectOne(Wrappers.<HrsfPmsUserScoreTask>lambdaQuery()
                .eq(HrsfPmsUserScoreTask::getTaskType, 1)
                .orderByDesc(HrsfPmsUserScoreTask::getCreateTime)
                .last("FETCH FIRST 1 ROWS ONLY"));
    }

    @Override
    public List<HrsfPmsUserScoreTaskDetailVo> queryFailedTaskDetailList(String taskIdStr) {
        long taskId = 0;
//        if(StringUtils.isNotEmpty(taskIdStr)){
//            try{
//                taskId = Long.parseLong(taskIdStr);
//            }catch (NumberFormatException e){
//                log.warn("queryFailedTaskDetailList invalid taskId: {}", taskIdStr);
//                return Collections.emptyList();
//            }
//        }else{
            HrsfPmsUserScoreTask currentTaskStatus = getCurrentTaskStatus();
            taskId = currentTaskStatus.getId();
//        }
        return hrsfPmsUserScoreTaskMapper.queryProcessingDetailList(taskId, new Integer[]{3});
    }

    @Override
    public IPage<HrsfPmsUserScoreTaskDetailVo> queryTaskDetailList(HrsfPmsUserScoreTaskQueryDTO queryDTO) {
        QueryWrapper<HrsfPmsUserScoreTaskDetailVo> wrapper = new QueryWrapper<>();
        if (queryDTO.getTaskId() == null) {
            return new Page<>();
        }
        wrapper.eq("td.TASK_ID", queryDTO.getTaskId());
        wrapper.eq("td.STATUS", queryDTO.getStatus());
        return hrsfPmsUserScoreTaskMapper.queryScoreTaskDetailListPage(queryDTO, wrapper);
    }

    /**
     * 启动历史绩效分数任务
     * @param scoreTask 任务
     */
    private void handleHrsfPmsUserScoreTask(HrsfPmsUserScoreTask scoreTask){
        Long taskId = scoreTask.getId();
        Integer[] statuses = new Integer[]{0};
        boolean errorFlag = false;
        while (true){
            List<HrsfPmsUserScoreTaskDetailVo> detailVos = hrsfPmsUserScoreTaskMapper
                    .queryProcessingDetailList(taskId, statuses);
            detailVos.forEach(vo -> vo.setTaskStatus(1));
            hrsfPmsUserScoreTaskMapper.batchUpdateStatus(detailVos, sfUserName, LocalDateTime.now());
            log.info("handeHrsfPmsUserScoreTask processing. taskId:{}, detailCount:{}", taskId, detailVos.size());
            List<String> userIdList = detailVos.stream().map(HrsfPmsUserScoreTaskDetailVo::getStaffId)
                    .collect(Collectors.toList());
            // 获取模板所需 年度分数
            List<HrsfPmsUserScoreTemplateVo> scoreTemplateVos = hrsfPmsUserScoreTaskMapper.queryUserScoreYearList(scoreTask.getAssesYear(), userIdList);
            // 获取模板所需 季度分数
            List<HrsfPmsUserScoreTemplateVo> scoreQuarterVos = hrsfPmsUserScoreTaskMapper.queryUserScoreQuarterList(scoreTask.getAssesYear(), null, userIdList);
            scoreTemplateVos.addAll(scoreQuarterVos);
            // 聚合单个用户的评分
            Map<String, List<HrsfPmsUserScoreTemplateVo>> templateVoMap = new HashMap<>(detailVos.size());
            for (HrsfPmsUserScoreTemplateVo templateVo : scoreTemplateVos) {
                String staffId = templateVo.getStaffId();
                List<HrsfPmsUserScoreTemplateVo> templateVos = templateVoMap.computeIfAbsent(staffId, k -> new ArrayList<>());
                templateVos.add(templateVo);
            }
            // 多线程处理模板及上传
            int i = detailVos.size() - 1;
            String operator = mybatisPlusMetaObjectHandler.getUserName();
            while (i >= 0) {
                try {
                    HrsfPmsUserScoreTaskDetailVo detailVo = detailVos.get(i);
                    List<HrsfPmsUserScoreTemplateVo> templateVos = templateVoMap.get(detailVo.getStaffId());
                    if(templateVos == null){
                        log.info("handeHrsfPmsUserScoreTask templateVos is empty, staffId:{}", detailVo.getStaffId());
                        detailVo.setTaskStatus(3);
                        detailVo.setTaskErrorMessage("无模板数据 Not found the template data");
                        errorFlag = true;
                        // 更新任务状态
                        handleTaskStatus(scoreTask, detailVo, operator, errorFlag);
                        i--;
                        continue;
                    }
                    // 获取员工当前类型的考核指标
                    List<HrsfPmsUserScoreTemplateVo> finalTemplateVos = templateVos.stream()
                            .filter(data -> data.getStaffType().equals(detailVo.getStaffType()))
                            .collect(Collectors.toList());
                    // 排序
                    finalTemplateVos.sort(Comparator.comparing(HrsfPmsUserScoreTemplateVo::getAssessYear)
                            .thenComparing(HrsfPmsUserScoreTemplateVo::getAssessType).reversed());

                    // 提交任务
                    boolean finalErrorFlag = errorFlag;
                    userScoreTaskExecutor.execute(() -> {
                        // 处理并上传考核汇总报表
                        handleHrsfPmsUserScoreTemplate(scoreTask, detailVo, finalTemplateVos);
                        // 更新任务状态
                        handleTaskStatus(scoreTask, detailVo, operator, finalErrorFlag);
                    });
                }catch (RejectedExecutionException e){
                    try {
                        log.info("handeHrsfPmsUserScoreTask sleeping, taskId:{}", taskId);
                        Thread.sleep(2000L);
                        continue;
                    } catch (InterruptedException ex) {
                        log.warn("handeHrsfPmsUserScoreTask sleep failed, unhandled size: {}", scoreTemplateVos.size());
                    }
                } catch (RuntimeException e) {
                    log.warn("handeHrsfPmsUserScoreTask error, taskId:{}", taskId, e);
                }
                i--;
            }
        }
    }

    /**
     *  任务状态处理
     * @param scoreTask 主任务
     * @param detailVo 任务明细
     * @param operator 操作人
     * @param errorFlag 是否有错误
     */
    private void handleTaskStatus(HrsfPmsUserScoreTask scoreTask, HrsfPmsUserScoreTaskDetailVo detailVo, String operator, boolean
            errorFlag) {
        LocalDateTime now = LocalDateTime.now();
        // 更新task detail状态
        hrsfPmsUserScoreTaskMapper.updateStatus(detailVo, operator, now);
        // 结束任务处理
        if(detailVo.getId() >= scoreTask.getMaxDetailId()){
            int errorCount = hrsfPmsUserScoreTaskMapper.getScoreTaskDetailErrorCount(scoreTask.getId());
            if(errorCount > 0){
                scoreTask.setStatus(3);
            }else {
                scoreTask.setStatus(2);
            }
            scoreTask.setEndTime(now);
            scoreTask.setUpdateBy(operator);
            scoreTask.setUpdateTime(now);
            // 更新数据库状态并清空缓存
            hrsfPmsUserScoreTaskMapper.updateById(scoreTask);
            redisTemplate.opsForValue().getAndDelete(CacheConstants.CURRENT_USER_SCORE_TASK_ID_HIS);
            log.info("handeHrsfPmsUserScoreTask completed. taskId:{}", scoreTask.getId());
        } else {
            // 延长任务过期时间
            redisTemplate.opsForValue().getAndExpire(CacheConstants.CURRENT_USER_SCORE_TASK_ID_HIS,
                    CacheConstants.CURRENT_USER_SCORE_TASK_ID_CACHE_TIME, TimeUnit.MINUTES);
        }
    }

    /**
     * 处理Excel模板数据
     * @param scoreTask 任务
     * @param detailVo 任务明细
     * @param templateVos 考核数据
     */
    private void handleHrsfPmsUserScoreTemplate(HrsfPmsUserScoreTask scoreTask, HrsfPmsUserScoreTaskDetailVo detailVo, List<HrsfPmsUserScoreTemplateVo> templateVos){
        log.debug("handleHrsfPmsUserScoreTemplate debug, templateVos.size: {}, staffId: {}", templateVos.size(), detailVo.getStaffId());
        templateVos.parallelStream().forEach(templateVo -> {
            try{
                String assessTypeDesc = null;
                String assessYear = templateVo.getAssessYear();
                String assessType = templateVo.getAssessType();
                switch (assessType){
                    case "Q1":
                        assessTypeDesc = "第一季度";break;
                    case "Q2":
                        assessTypeDesc = "第二季度";break;
                    case "Q3":
                        assessTypeDesc = "第三季度";break;
                    case "YEAR":
                        assessTypeDesc = "年度";break;
                    default:
                        assessTypeDesc = "";
                }
                String assessDesc = String.format(assessDescFmt, assessYear, assessTypeDesc, assessYear, assessType);
                templateVo.setAssessDesc(assessDesc);
//                if(scoreTask.getFormDataPullFlag() == 1 || scoreTask.getFormDataPullFlag() == 3){
                    // 补全SF部分数据
                    fillDataFromSF(templateVo, detailVo);
//                }
            } catch (ServiceException e) {
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage(e.getMessage());
            } catch (RuntimeException e) {
                detailVo.setTaskStatus(3);
                detailVo.setTaskErrorMessage(e.getMessage());
                log.warn("fillDataFromSF failed, staffId:{}, templateVoId:{}", templateVo.getStaffId(), templateVo.getId(), e);
            }
        });

        try{
            String templateFileName;
            if(detailVo.getStaffType() == 1 || detailVo.getStaffType() == 3){
                templateFileName = "/file/wc_user_scoring_report.xlsx";
            }else{
                templateFileName = "/file/bc_user_scoring_report.xlsx";
            }
            // 转为base64并上传到SF
            String base64Str = handleTemplateAsBase64(templateFileName, templateVos, detailVo);
            uploadExcelToSF(detailVo, base64Str);
            if(StringUtils.isEmpty(detailVo.getTaskErrorMessage())){
                detailVo.setTaskStatus(2);
            }
            detailVo.setSfUploadTime(LocalDateTime.now());
            log.info("handleHrsfPmsUserScoreTemplate completed, detailId: {}, staffId: {}", detailVo.getId(), detailVo.getStaffId());
        } catch (RuntimeException e) {
            detailVo.setTaskStatus(3);
            detailVo.setTaskErrorMessage(e.getMessage());
            log.info("handleHrsfPmsUserScoreTemplate failed, detailId: {}, staffId: {}", detailVo.getId(), detailVo.getStaffId());
        }
    }

    private void uploadExcelToSF(HrsfPmsUserScoreTaskDetailVo detailVo, String base64Str){
        HashMap<String, Object> metaData = new HashMap<>();
        metaData.put("uri", "Attachment");
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("__metadata", metaData);
        paramMap.put("module", "EMPLOYEE_PROFILE");
        paramMap.put("userId", "SDAPI");
        paramMap.put("fileContent", base64Str);
        String fileName;
        String staffTypeDescCn;
        String staffTypeDescEn;
        if(detailVo.getStaffType() == 1 || detailVo.getStaffType() == 3){
            staffTypeDescCn = "白领";
            staffTypeDescEn = "White-collar";
        } else{
            staffTypeDescCn = "蓝领";
            staffTypeDescEn = "Blue-collar";
        }
        fileName = String.format(sfUploadFileNameFmt, staffTypeDescCn, detailVo.getStaffId(), detailVo.getStaffName(), staffTypeDescEn, staffTypeDescCn);
        paramMap.put("fileName", fileName);

        // 上传附件
        String attachmentId = sfApiManager.uploadAttachment(detailVo.getStaffId(), paramMap);
        // 步骤2：将附件上传并与员工号关联
        sfApiManager.associateAttachment(detailVo.getStaffId(), attachmentId);
    }

    /**
     * 填充模板数据，然后转换为base64
     * @param templateFileName 模板文件名
     * @param data 数据
     */
    private String handleTemplateAsBase64(String templateFileName, List<?> data, Object extData){
        String base64Str = null;
        InputStream templateFileInputStream = null;
        ByteArrayOutputStream outputStream = null;
        try{
            templateFileInputStream = new ClassPathResource(templateFileName).getInputStream();
            outputStream = new ByteArrayOutputStream();
            // 输出为文件流
            ExcelWriter excelWriter = EasyExcel.write(outputStream).withTemplate(templateFileInputStream).inMemory(true).build();
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
            excelWriter.fill(data, fillConfig, writeSheet);
            excelWriter.fill(extData, writeSheet);
            excelWriter.finish();

            base64Str = java.util.Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (RuntimeException | IOException e){
            log.warn("handleTemplateAsBase64 transfer base64 failed", e);
        } finally {
            try {
                if(templateFileInputStream != null){
                    templateFileInputStream.close();
                }
                if(outputStream != null){
                    outputStream.close();
                }
            } catch (IOException e) {
                log.warn("handleTemplateAsBase64 close output failed", e);
            }
        }
        return base64Str;
    }

    /**
     * 调用SF接口不全数据
     *
     * @param vo        数据模板
     * @param detailVo 任务明细
     */
    private void fillDataFromSF(HrsfPmsUserScoreTemplateVo vo, HrsfPmsUserScoreTaskDetailVo detailVo){
        log.debug("fillDataFromSF processing, staffId:{}, templateVoId:{}", vo.getStaffId(), vo.getId());
        Integer staffType = detailVo.getStaffType();
        // 获取formContentId
        String formId = vo.getFormId();
        if(StringUtils.isEmpty(formId) || formId.equalsIgnoreCase("null")){
            log.info("SF formId is empty, staffId:{}, hrsf_pmsuser_base.id:{}", vo.getStaffId(), vo.getId());
            return;
        }
        String formContentId = sfApiManager.getFormContentId(vo.getStaffId(), formId);

        // 获取轻微违规违纪扣减说明
        if(!"YEAR".equals(vo.getAssessType())){
            try{
                String regulationDeductedRemark = sfApiManager.getRegulationDeductedRemark(vo.getStaffId(), formId, formContentId);
                vo.setRegulationDeductedRemark(sfApiManager.unescapeHtml(regulationDeductedRemark));
            } catch (ServiceException e) {
                detailVo.setTaskErrorMessage(e.getMessage());
            }
        }

        // 获取(白领)主要成就
        if("YEAR".equals(vo.getAssessType()) && (staffType == 1 || staffType == 3) ){
            try {
                String annualMajorAccomplishments = sfApiManager.getMajorAccomplishments(vo.getStaffId(), formId, formContentId);
                vo.setAnnualMajorAccomplishments(sfApiManager.unescapeHtml(annualMajorAccomplishments));
            } catch (ServiceException e) {
                detailVo.setTaskErrorMessage(e.getMessage());
            }
        }
        // 检查并同步绩效评分
        checkAndSyncCompetenciesScore(formContentId, formId, detailVo.getStaffType(), vo);
    }

    /**
     * 检查并同步绩效评分
     * @param contentId SF contentId
     * @param formId SF formId
     * @param staffType DB staffType 员工类型：1-白领，2-蓝领
     * @param templateVo DB templateVo
     */
    private void checkAndSyncCompetenciesScore(String contentId, String formId, Integer staffType, HrsfPmsUserScoreTemplateVo templateVo){
//        if(templateVo.getWorkQualityScore() != null || templateVo.getCompletionOfWorkScore() != null){
//            // 如果有小分则不用同步SF小分
//            return;
//        }
        String operator = mybatisPlusMetaObjectHandler.getUserName();
        LocalDateTime now = LocalDateTime.now();

        if("YEAR".equals(templateVo.getAssessType()) && (staffType == 2 || staffType == 4)){
            // 蓝领年度小分
            if(templateVo.getCompletionOfWorkScore() == null || templateVo.getCompletionOfWorkScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setCompletionOfWorkScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId1));
            }
            if(templateVo.getWorkQualityScore() == null || templateVo.getWorkQualityScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setWorkQualityScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId2));
            }
            if (templateVo.getProductionSafetyScore() == null || templateVo.getProductionSafetyScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setProductionSafetyScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId3));
            }
            if (templateVo.getAttendanceScore() == null || templateVo.getAttendanceScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setAttendanceScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId4));
            }
            if(templateVo.getCostControlScore() == null || templateVo.getCostControlScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setCostControlScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId5));
            }
            if(templateVo.getCommunicationScore() == null || templateVo.getCommunicationScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setCommunicationScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId6));
            }
            if(templateVo.getTeamWorkScore() == null || templateVo.getTeamWorkScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setTeamWorkScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId7));
            }
            if(templateVo.getProfessionalScore() == null || templateVo.getProfessionalScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setProfessionalScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId8));
            }
            if(templateVo.getResponsibilityScore() == null || templateVo.getResponsibilityScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setResponsibilityScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId9));
            }
            if(templateVo.getDisciplineScore() == null || templateVo.getDisciplineScore().compareTo(BigDecimal.ZERO) == 0){
                templateVo.setDisciplineScore(sfApiManager.getCompetenciesScore(contentId, formId, sfYearScoreItemId10));
            }
            hrsfPmsUserScoreTaskMapper.updateCompetenciesYearScore(templateVo, operator, now);
        } else if(!"YEAR".equals(templateVo.getAssessType()) && (staffType == 2 || staffType == 4)) {
            // 蓝领季度小分
            if (templateVo.getWorkQualityScore() == null || templateVo.getWorkQualityScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setWorkQualityScore(sfApiManager.getCompetenciesScore(contentId, formId, sfBlueQuarterScoreItemId1));
            }
            if (templateVo.getCostControlScore() == null || templateVo.getCostControlScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setCostControlScore(sfApiManager.getCompetenciesScore(contentId, formId, sfBlueQuarterScoreItemId2));
            }
            if (templateVo.getCompletionOfWorkScore() == null || templateVo.getCompletionOfWorkScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setCompletionOfWorkScore(sfApiManager.getCompetenciesScore(contentId, formId, sfBlueQuarterScoreItemId3));
            }
            if (templateVo.getProductionSafetyScore() == null || templateVo.getProductionSafetyScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setProductionSafetyScore(sfApiManager.getCompetenciesScore(contentId, formId, sfBlueQuarterScoreItemId4));
            }
            if (templateVo.getAttendanceScore() == null || templateVo.getAttendanceScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setAttendanceScore(sfApiManager.getCompetenciesScore(contentId, formId, sfBlueQuarterScoreItemId5));
            }
            hrsfPmsUserScoreTaskMapper.updateCompetenciesQuarterScore(templateVo, operator, now);
        } else if(!"YEAR".equals(templateVo.getAssessType()) && (staffType == 1 || staffType == 3)){
            // 白领季度小分
            if (templateVo.getWorkQualityScore() == null || templateVo.getWorkQualityScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setWorkQualityScore(sfApiManager.getCompetenciesScore(contentId, formId, sfWhiteQuarterScoreItemId1));
            }
            if (templateVo.getCostControlScore() == null || templateVo.getCostControlScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setCostControlScore(sfApiManager.getCompetenciesScore(contentId, formId, sfWhiteQuarterScoreItemId2));
            }
            if (templateVo.getCompletionOfWorkScore() == null || templateVo.getCompletionOfWorkScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setCompletionOfWorkScore(sfApiManager.getCompetenciesScore(contentId, formId, sfWhiteQuarterScoreItemId3));
            }
            if (templateVo.getProductionSafetyScore() == null || templateVo.getProductionSafetyScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setProductionSafetyScore(sfApiManager.getCompetenciesScore(contentId, formId, sfWhiteQuarterScoreItemId4));
            }
            if (templateVo.getAttendanceScore() == null || templateVo.getAttendanceScore().compareTo(BigDecimal.ZERO) == 0) {
                templateVo.setAttendanceScore(sfApiManager.getCompetenciesScore(contentId, formId, sfWhiteQuarterScoreItemId5));
            }
            log.debug("===debug updateCompetenciesQuarterScore, templateVo:{}", JSONUtil.toJsonStr(templateVo));
            hrsfPmsUserScoreTaskMapper.updateCompetenciesQuarterScore(templateVo, operator, now);
        }
    }

}
