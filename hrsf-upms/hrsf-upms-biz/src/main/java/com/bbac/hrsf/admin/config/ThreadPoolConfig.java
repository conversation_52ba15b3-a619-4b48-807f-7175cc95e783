package com.bbac.hrsf.admin.config;

import com.google.common.base.Throwables;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 * <AUTHOR>
 * @since 18/08/2021
 */
@Configuration
@Slf4j
public class ThreadPoolConfig {

    /**
     * 批处理任务专用线程池
     */
    @Bean("batchTaskExecutor")
    public ThreadPoolTaskExecutor batchTaskExecutor() {
        int queueCapacity = 1000;
        int corePoolSize = 10;
        int maxPoolSize = 20;
        int awaitTerminationSeconds = 3;
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        ThreadFactory factory = new ThreadFactoryBuilder().setUncaughtExceptionHandler(
            (t, ex) -> {
            log.error("thread: {}, error:{} ", t.getName(), Throwables.getRootCause(ex).toString());
            }
        ).setNameFormat("batch-task-pool-%d").build();
        executor.setThreadFactory(factory);
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("batch-task-pool-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        return executor;
    }

    /**
     * 历史绩效分数任务线程池
     */
    @Bean("userScoreTaskExecutor")
    public ThreadPoolTaskExecutor userScoreTaskExecutor() {
        int queueCapacity = 1000;
        int corePoolSize = 10;
        int maxPoolSize = 20;
        int awaitTerminationSeconds = 3;
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        ThreadFactory factory = new ThreadFactoryBuilder().setUncaughtExceptionHandler(
                (t, ex) -> {
                    log.error("UserScoreTask thread: {}, error:{} ", t.getName(), Throwables.getRootCause(ex).toString(), ex);
                }
        ).setNameFormat("UserScoreTaskPool-%d").build();
        executor.setThreadFactory(factory);
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("UserScoreTaskPool-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        return executor;
    }

    /**
     * 绩效复制任务线程池
     */
    @Bean("userScoreDuplicateTaskExecutor")
    public ThreadPoolTaskExecutor userScoreDuplicateTaskExecutor() {
        int queueCapacity = 1000;
        int corePoolSize = 10;
        int maxPoolSize = 20;
        int awaitTerminationSeconds = 3;
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        ThreadFactory factory = new ThreadFactoryBuilder().setUncaughtExceptionHandler(
                (t, ex) -> {
                    log.error("userScoreDuplicateTaskExecutor thread: {}, error:{} ", t.getName(), Throwables.getRootCause(ex).toString(), ex);
                }
        ).setNameFormat("userScoreDuplicateTaskExecutor-%d").build();
        executor.setThreadFactory(factory);
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("userScoreDuplicateTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(awaitTerminationSeconds);
        executor.initialize();
        return executor;
    }
}
