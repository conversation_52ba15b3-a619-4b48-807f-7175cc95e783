package com.bbac.hrsf.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelationInfo;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Mapper
public interface HrsfPmsUserRelationInfoMapper extends BaseMapper<HrsfPmsUserRelationInfo> {

    @Delete("DELETE FROM HRSF_PMS_USER_RELATION_INFO WHERE PMS_USER_ID = #{pmsUserId}")
    boolean deleteByPmsUserId(Long pmsUserId);

    @Delete("DELETE FROM HRSF_PMS_USER_RELATION_INFO")
    boolean deleteTabelData();
}
