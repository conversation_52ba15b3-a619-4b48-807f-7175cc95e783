package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardPDF;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 实现功能：人才卡数据下载
 */
public interface IHrsfPmsTalentCardPDFService extends IService<HrsfPmsTalentCardPDF> {
    /**
     * 人才卡数据下载
     * @param list 工号集合
     */
    void download(List<String> list, HttpServletResponse response);

    /**
     * 生成pdf文件
     */
    void createPdf();

    /**
     * 根据指定工号生成pdf文件
     */
    void createPdf(String staffId) throws Exception;
}
