/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.SysRoleDTO;
import com.bbac.hrsf.admin.api.entity.*;
import com.bbac.hrsf.admin.mapper.SysRoleMapper;
import com.bbac.hrsf.admin.mapper.SysRoleMenuMapper;
import com.bbac.hrsf.admin.service.SysRoleService;
import com.bbac.hrsf.common.core.constant.CacheConstants;
import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {

    private final SysRoleMenuMapper sysRoleMenuMapper;

    /**
     * 通过角色ID，删除角色,并清空角色菜单缓存
     *
     * @param id
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = CacheConstants.MENU_DETAILS, allEntries = true)
    public Boolean removeRoleById(Long id) {

        if (Objects.equals(id, RoleTypeEnum.HR.getType())
                || Objects.equals(id, RoleTypeEnum.HRL3.getType())
                || Objects.equals(id, RoleTypeEnum.HRL4.getType())) {
            throw new CheckedException("该角色作为校准会议的固定审批结点，不支持删除。您可以修改角色人员；");
        }

        sysRoleMenuMapper.delete(Wrappers.<SysRoleMenu>update().lambda().eq(SysRoleMenu::getRoleId, id));
        return this.removeById(id);
    }

    @Override
    public IPage<SysRole> getUserPageInfo(Page page, SysRoleDTO sysRoleDTO) {
        return baseMapper.selectPage(page, buildQueryWrapper(sysRoleDTO));
    }

    private LambdaQueryWrapper buildQueryWrapper(SysRoleDTO sysRoleDTO) {
        LambdaQueryWrapper<SysRole> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotBlank(sysRoleDTO.getRoleName())) {
            wrapper.like(SysRole::getRoleName, sysRoleDTO.getRoleName());
        }
        if (StrUtil.isNotBlank(sysRoleDTO.getEnableFlag())) {
            wrapper.eq(SysRole::getEnableFlag, sysRoleDTO.getEnableFlag());
        }
        return wrapper;
    }

}
