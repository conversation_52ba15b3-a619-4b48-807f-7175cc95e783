package com.bbac.hrsf.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelation;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Mapper
public interface HrsfPmsUserRelationMapper extends BaseMapper<HrsfPmsUserRelation> {

    @Delete("DELETE FROM HRSF_PMS_USER_RELATION WHERE PMS_USER_ID = #{pmsUserId}")
    boolean deleteByPmsUserId(Long pmsUserId);

    @Delete("DELETE FROM HRSF_PMS_USER_RELATION WHERE PMS_STAFF_ID = #{pmsStaffId}")
    boolean deleteByPmsStaffId(String pmsStaffId);

    @Delete("DELETE FROM HRSF_PMS_USER_RELATION_INFO WHERE PMS_STAFF_ID = #{pmsStaffId}")
    boolean deleteByPmsStaffIdFormInfo(String pmsStaff);
}
