package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.dto.CompetencyRatingDTO;
import com.bbac.hrsf.admin.api.dto.CompetencyRatingFormHeaderDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCard;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardCourseRecord;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardKeyProject;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserCompetency;
import com.bbac.hrsf.admin.api.vo.HrsfUserSimpleVO;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.mapper.HrsfPmsUserCompetencyMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardCourseRecordService;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardKeyProjectService;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardPDFService;
import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardService;
import com.bbac.hrsf.admin.service.IHrsfPmsUserCompetencyService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import com.bbac.hrsf.performance.api.feign.RemoteEmailService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.*;

/**
 * 对应需求文档参：
 * 能力分数回写功能说明书.docx
 * 人才卡PRD_v3.docx
 */
@Slf4j
@Service
public class HrsfPmsUserCompetencyServiceImpl extends ServiceImpl<HrsfPmsUserCompetencyMapper, HrsfPmsUserCompetency> implements IHrsfPmsUserCompetencyService {

    public static final String DELIMITER = "###";

    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;

    @Value("${sf.sync.usernameSDAPI:SDAPI@beijingbenT1}")
    private String userName;
    @Value("${sf.sync.pwdSDAPI:q~gOkDiCE!BU}")
    private String pwd;

    @Value("${sf.sync.usernameSDHRAPI:SDHR@beijingbenT1}")
    private String sdhrUser;

    @Value("${sf.sync.pwdSDHRAPI:^W7#A-wlfK8n}")
    private String sdhrPwd;

    //能力定义查询url: https://api15preview.sapsf.cn/odata/v2/RoleCompetencyMappingEntity?&$expand=competencyNav&$filter=RoleEntity_externalCode eq '职务分类'&$format=Json
    @Value("${sf.sync.competencyDefApi:https://api15preview.sapsf.cn/odata/v2/RoleCompetencyMappingEntity?&$expand=competencyNav&$filter=RoleEntity_externalCode eq '{}'&$format=Json}")
    private String competencyDefApi;

    //第三步获取能力表单ID值 https://api15preview.sapsf.cn/odatav4/ecosystem/jobs/JpbAdoptionOdataService.svc/v1/JdmJpbMappingEntity?$filter=objectType eq 'COMPETENCY' and guid eq '能力ID'
    @Value("${sf.sync.jdmIdApi:https://api15preview.sapsf.cn/odatav4/ecosystem/jobs/JpbAdoptionOdataService.svc/v1/JdmJpbMappingEntity?$filter=objectType eq 'COMPETENCY' and guid eq '{}'}")
    private String jdmIdApi;

    //获取员工能力评分 https://api15preview.sapsf.cn/odata/v2/CompetencyRating?&$filter=guid eq '能力ID' and userId eq '员工工号' and status eq 3&$format=Json&$orderby=lastModifiedDateTime desc
    @Value("${sf.sync.competencyRatingApi:https://api15preview.sapsf.cn/odata/v2/CompetencyRating?&$filter=guid eq '{}' and userId eq '{}' and status eq 3&$format=Json&$orderby=lastModifiedDateTime desc}")
    private String competencyRatingApi;

    //2.1 步骤1：读取集成账号待办信息 SDAPI：员工工号
    //https://api15preview.sapsf.cn/odata/v2/TodoEntryV2?$filter=userId eq 'SDAPI'&$format=json&$expand=formContentNav/pmReviewContentDetail/competencySections/competencies/officialRating&$format=Json&$orderby=createdDate asc&$select=formContentNav/pmReviewContentDetail/competencySections/competencies,todoEntryId,subjectId,formContentNav/pmReviewContentDetail/competencySections/sectionIndex,formContentNav/pmReviewContentDetail/competencySections/sectionWeightKey
    @Value("${sf.sync.todoListApi:https://api15preview.sapsf.cn/odata/v2/TodoEntryV2?$filter=userId eq 'SDAPI' and status eq '2'&$format=json&$expand=formContentNav/pmReviewContentDetail/competencySections/competencies/officialRating&$format=Json&$orderby=createdDate asc&$select=formContentNav/pmReviewContentDetail/competencySections/competencies,todoEntryId,subjectId,formContentNav/pmReviewContentDetail/competencySections/sectionIndex,formContentNav/pmReviewContentDetail/competencySections/sectionWeightKey,status}")
    private String todoListApi;

    //2.1 步骤2：表单能力分数回写
    //https://api15preview.sapsf.cn/odata/v2/upsert
    @Value("${sf.sync.competencyUpdateApi:https://api15preview.sapsf.cn/odata/v2/upsert}")
    private String competencyUpdateApi;

    //发送表单到下一步 https://api15preview.sapsf.cn/odata/v2/sendToNextStep?formDataId=<formDataId>
    @Value("${sf.sync.sendFormIdToNextStepApi:https://api15preview.sapsf.cn/odata/v2/sendToNextStep?formDataId={}L}")
    private String sendFormIdToNextStepApi;

    //人才卡 4.6.3 能力分数信息
    @Value("${sf.sync.formHeaderApi:https://api15preview.sapsf.cn/odata/v2/FormHeader?&$format=Json&$filter=formSubjectId eq '{}' and formDataStatus eq '3' and formTemplateId eq '805'&$orderby=creationDate desc&$select=formSubjectId,formDataStatus,formTemplateId,rating,creationDate}")
    private String formHeaderApi;
    //人才卡 4.7 过往人才盘点结果
    @Value("${sf.sync.previousTalentReviewResultApi:https://api15preview.sapsf.cn/odata/v2/User?&$filter=userId eq '{}'&$format=Json&$select=userId,matrix1Label}")
    private String previousTalentReviewResultApi;
    //人才卡 4.8 评价中心测评结果
    @Value("${sf.sync.assessmentCenterEvaluationResultApi:https://api15preview.sapsf.cn/odata/v2/cust_ACAssessment?&$filter=externalCode eq '{}'&$format=Json&$select=externalCode,cust_TypeNav/label_zh_CN,cust_ACResultNav/label_zh_CN,cust_AssessmentDate&$expand=cust_ACResultNav,cust_TypeNav}")
    private String assessmentCenterEvaluationResultApi;
    //人才卡 4.9 参与的重点项目
    @Value("${sf.sync.keyProjectsListLeftApi:https://api15preview.sapsf.cn/odata/v2/Background_Program?$filter=userId eq '{}'&$format=Json&$select=userId,Program,startDate,bgOrderPos&$orderby=bgOrderPos asc}")
    private String keyProjectsListLeftApi;
    @Value("${sf.sync.keyProjectsListRightApi:https://api15preview.sapsf.cn/odata/v2/Background_Hprogram?$filter=userId eq '{}'&$format=Json&$select=userId,startDate}")
    private String keyProjectsListRightApi;
    //人才卡 4.10 本岗位本年度培训记录
    @Value("${sf.sync.courseRecordListApi:https://api15preview.sapsf.cn/odata/v2/DevGoal_2001?$format=json&$filter=userId eq '{}'&$expand=milestones}")
    private String courseRecordListApi;

    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;
    @Value("${sf.sync.operation.email}")
    private String operationEmail;
    @Autowired
    private IHrsfUserBaseService hrsfUserBaseService;
    @Autowired
    private IHrsfPmsTalentCardService hrsfPmsTalentCardService;
    @Autowired
    private IHrsfPmsTalentCardCourseRecordService hrsfPmsTalentCardCourseRecordService;
    @Autowired
    private IHrsfPmsTalentCardKeyProjectService hrsfPmsTalentCardKeyProjectService;
    @Lazy
    @Autowired
    private IHrsfPmsTalentCardPDFService hrsfPmsTalentCardPDFService;

    @Autowired
    private RemoteEmailService remoteEmailService;

    private final long size = 100;


    private static HashMap<String, String> CompetencyJdmIdMap = new HashMap(100);

    //映射Competency和JdmId的关系表
    static {
        CompetencyJdmIdMap.put("1", "1168");
        CompetencyJdmIdMap.put("2", "1184");
        CompetencyJdmIdMap.put("3", "1206");
        CompetencyJdmIdMap.put("4", "1228");
        CompetencyJdmIdMap.put("5", "1240");
        CompetencyJdmIdMap.put("6", "1242");
        CompetencyJdmIdMap.put("7", "1244");
        CompetencyJdmIdMap.put("12", "1170");
        CompetencyJdmIdMap.put("14", "1172");
        CompetencyJdmIdMap.put("15", "1174");
        CompetencyJdmIdMap.put("16", "1176");
        CompetencyJdmIdMap.put("17", "1178");
        CompetencyJdmIdMap.put("18", "1180");
        CompetencyJdmIdMap.put("19", "1182");
        CompetencyJdmIdMap.put("20", "1186");
        CompetencyJdmIdMap.put("21", "1188");
        CompetencyJdmIdMap.put("22", "1190");
        CompetencyJdmIdMap.put("23", "1192");
        CompetencyJdmIdMap.put("24", "1194");
        CompetencyJdmIdMap.put("25", "1196");
        CompetencyJdmIdMap.put("26", "1198");
        CompetencyJdmIdMap.put("27", "1200");
        CompetencyJdmIdMap.put("28", "1202");
        CompetencyJdmIdMap.put("29", "1204");
        CompetencyJdmIdMap.put("30", "1208");
        CompetencyJdmIdMap.put("31", "1210");
        CompetencyJdmIdMap.put("32", "1212");
        CompetencyJdmIdMap.put("33", "1214");
        CompetencyJdmIdMap.put("34", "1216");
        CompetencyJdmIdMap.put("35", "1218");
        CompetencyJdmIdMap.put("36", "1220");
        CompetencyJdmIdMap.put("37", "1222");
        CompetencyJdmIdMap.put("38", "1224");
        CompetencyJdmIdMap.put("39", "1226");
        CompetencyJdmIdMap.put("40", "1230");
        CompetencyJdmIdMap.put("41", "1232");
        CompetencyJdmIdMap.put("42", "1234");
        CompetencyJdmIdMap.put("43", "1236");
        CompetencyJdmIdMap.put("44", "1238");
    }

//    暂时不需要 只用basicAuth就能获取到数据了 TODO 后续如果确实不需要可以把此部分删掉
//    //第一步获取assertion值 https://api15preview.sapsf.cn/oauth/idp
//    @Value("${sf.sync.assertionApi}")
//    private String assertionApi;
//    //第一步获取assertion值 的入参
//    @Value("${sf.sync.assertionApiParam}")
//    private String assertionApiParam;
//    //第二步获取Token值 https://api15preview.sapsf.cn/oauth/token
//    @Value("${sf.sync.accessTokenApi}")
//    private String accessTokenApi;
//    //第二步获取Token值 company_id=<CompanyID>&client_id=<ClientID>&grant_type=urn:ietf:params:oauth:grant-type:saml2-bearer&user_id=<userid>
//    @Value("${sf.sync.accessTokenApiParam}")
//    private String accessTokenApiParam;

    /**
     * 接口调式失败重试次数  建议至少2次，某些接口第一次调用报401，第二次调用就好了
     */
    private static final int retryCount = 2;

    /**
     * 主方法，支持反复调用
     */
    @XxlJob("pullCompetencyDataAndWriteBack")//TODO 需要按业务要求配置
    public ReturnT<String> pullCompetencyDataAndWriteBackJob() {
        String oldName = Thread.currentThread().getName();
        try {
            Thread.currentThread().setName(UUID.randomUUID().toString());
            XxlJobHelper.log("开始执行能力分数回写任务");
            log.info("开始执行能力分数回写任务");
            pullCompetencyDataAndWriteBack();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            XxlJobHelper.log(e);
            return new ReturnT(500, e.getMessage());
        } finally {
            log.info("能力分数回写任务执行结束.");
            Thread.currentThread().setName(oldName);
        }
    }

    /**
     * 主方法，支持反复调用，依赖主数据表信息
     */
    @Override
    public void pullCompetencyDataAndWriteBack() {
        log.info("执行生成人才卡开始时间:{}", LocalDateTime.now());
        try{
            log.info("remoteEmailService.sendEmailByInfo=========>{}",operationEmail);
            //TODO 邮件通知 模板、内容、收件人等信息需要优化
            //name_zh_CN 规则： 排序序号-中文分类-中文名称\n英文分类-英文名称  约定只能有一个\n
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setSendTo(operationEmail);
            emailInfoDTO.setSubject("执行生成人才卡任务成功启动" );
            emailInfoDTO.setTemplate("launch_success_template.html");
            Map<String, Object> model = new HashMap<>(16);
            model.put("calibrationName", "test");
            emailInfoDTO.setModel(model);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);

            log.info("remoteEmailService.sendEmailByInfo=========>22");
        }catch (Exception e){
            e.printStackTrace();
        }
        //1.	能力分数存储
        //抓取员工信息
        List<HrsfUserSimpleVO> simpleInfoList = hrsfUserBaseService.getAllSimpleInfo();
        //应为能力标准名称解析失败从而要忽略的员工 供发送表单到下一步使用
        Set<String> ignoreStaffIdSet = Sets.newHashSet();
        //1.1	能力标准
        List<HrsfPmsUserCompetency> competencyDefIDList = findCompetencyDefinition(simpleInfoList, ignoreStaffIdSet);
        //1.2	能力评分
        List<HrsfPmsUserCompetency> userCompetencyList = findUserCompetency(simpleInfoList, competencyDefIDList);
        //写入本地数据库 此数据是人才卡功能的基础数据
        getBaseMapper().truncate();
        this.saveBatch(userCompetencyList);
        //步骤1：读取集成账号待办信息 一次性差所有员工的待办信息
        List<CompetencyIds> idsList = Lists.newArrayList();
        List<CompetencySections> sectionsList = Lists.newArrayList();
        Boolean sync = true;
        int i=0;
        while (sync) {
            //后面几步都按员工处理
            sync = findTodoInfo(idsList, sectionsList, userCompetencyList, i * size, size);
            i++;
        }
        //2.年底能力分数回写
        backUpdate(idsList);
        //3.区间权重写入
        weightUpdate(sectionsList);
        //4.发送表单到下一步
        sendForm2Next(sectionsList, ignoreStaffIdSet);

        //人才卡需要的信息
        //人才卡 4.6.3 能力分数信息
        List<HrsfPmsTalentCard> talentCardInfoList = getFormHeader(simpleInfoList);
        //人才卡 4.7 过往人才盘点结果
        getPreviousTalentReviewResult(talentCardInfoList);
        //人才卡 4.8 评价中心测评结果
        getAssessmentCenterEvaluationResult(talentCardInfoList);
        getBaseMapper().truncateTalentCard();
        hrsfPmsTalentCardService.saveBatch(talentCardInfoList);
        //人才卡 4.9 参与的重点项目
        List<HrsfPmsTalentCardKeyProject> talentCardKeyProjectList = getKeyProject(simpleInfoList);
        getBaseMapper().truncateKeyProject();
        hrsfPmsTalentCardKeyProjectService.saveBatch(talentCardKeyProjectList);
        //人才卡 4.10 本岗位本年度培训记录
        List<HrsfPmsTalentCardCourseRecord> talentCardCourseRecordList = getCourseRecord(simpleInfoList);
        getBaseMapper().truncateCourseRecord();
        this.hrsfPmsTalentCardCourseRecordService.saveBatch(talentCardCourseRecordList);
        log.info("执行生成人才卡结束时间:{}", LocalDateTime.now());
    }

    //----------------------测试代码开始 TODO 后续如果确实不需要可以把此部分删掉 -----------------------
    public static void main(String[] args) {


        // 使用Instant从时间戳创建一个时间点
        long timestamp =  Long.parseLong(dateConvert("/Date(1695718722000+0000)/"));
        Instant instant = Instant.ofEpochMilli(timestamp);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateStr = zonedDateTime.format(formatter);
        LocalDateTime localDateTime = LocalDateTime.parse(formattedDateStr, formatter);
        Date date = new Date(Timestamp.valueOf(localDateTime).getTime());
//        Date date = Date.from(ZonedDateTime.parse(formattedDateStr, formatter).toInstant());
//        Date date = Date.from(localDateTime.atZone(ZoneOffset.UTC).toInstant());
        System.out.println("Formatted Date: " + date);

        HrsfPmsUserCompetencyServiceImpl service = new HrsfPmsUserCompetencyServiceImpl();
        service.setProxyEnable(false);
        service.setProxyHost(null);
        service.setProxyPort(0);
        service.setUserName("pmgmadmin@beijingbenT1");
        service.setPwd("q4nc+A7)WEAryyn");//"SDAPI@beijingbenT1", "q~gOkDiCE!BU"
//        service.setSdhrUser("SDHR");
//        service.setSdhrPwd("^W7#A-wlfK8n");
        service.setCompetencyDefApi("https://api15preview.sapsf.cn/odata/v2/RoleCompetencyMappingEntity?&$expand=competencyNav&$filter=RoleEntity_externalCode eq '{}'&$format=Json");
//        service.setAssertionApi("https://api15preview.sapsf.cn/oauth/idp");
//        service.setAssertionApiParam("client_id=M2U5MWExYTI1YjE1YTIzNDU0ZWQyMTUwNDhmOA&user_id=LearningAPI&token_url=https://api15preview.sapsf.cn/oauth/token&private_key=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");
//        service.setAccessTokenApi("https://api15preview.sapsf.cn/oauth/token");
//        service.setAccessTokenApiParam("company_id=beijingbenT1&client_id=M2U5MWExYTI1YjE1YTIzNDU0ZWQyMTUwNDhmOA&grant_type=urn:ietf:params:oauth:grant-type:saml2-bearer&user_id=LearningAPI");
        service.setJdmIdApi("https://api15preview.sapsf.cn/odatav4/ecosystem/jobs/JpbAdoptionOdataService.svc/v1/JdmJpbMappingEntity?$filter=objectType eq 'COMPETENCY' and guid eq '{}'");

        service.setCompetencyRatingApi("https://api15preview.sapsf.cn/odata/v2/CompetencyRating?&$filter=userId eq '{}' and status eq 3&$format=Json&$orderby=lastModifiedDateTime desc&$select=guid,lastModifiedDateTime,rating");
        //只查询待办： https://api15preview.sapsf.cn/odata/v2/TodoEntryV2?$filter=userId eq '{}' and status eq '2'&$format=json&$expand=formContentNav/pmReviewContentDetail/competencySections/competencies/officialRating&$format=Json&$orderby=createdDate asc&$select=formContentNav/pmReviewContentDetail/competencySections/competencies,todoEntryId,subjectId,formContentNav/pmReviewContentDetail/competencySections/sectionIndex,formContentNav/pmReviewContentDetail/competencySections/sectionWeightKey,status
//        service.setTodoListApi("https://api15preview.sapsf.cn/odata/v2/TodoEntryV2?$filter=userId eq '{}' and status eq '2'&$format=json&$expand=formContentNav/pmReviewContentDetail/competencySections/competencies/officialRating&$format=Json&$orderby=createdDate asc&$select=formContentNav/pmReviewContentDetail/competencySections/competencies,todoEntryId,subjectId,formContentNav/pmReviewContentDetail/competencySections/sectionIndex,formContentNav/pmReviewContentDetail/competencySections/sectionWeightKey,status");
//        service.setTodoListApi("https://api15preview.sapsf.cn/odata/v2/TodoEntryV2?$filter=userId eq 'SDAPI' and status eq '2'&$format=json&$expand=formContentNav/pmReviewContentDetail/competencySections/competencies/officialRating&$format=Json&$orderby=createdDate asc&$select=formContentNav/pmReviewContentDetail/competencySections/competencies,todoEntryId,subjectId,formContentNav/pmReviewContentDetail/competencySections/sectionIndex,formContentNav/pmReviewContentDetail/competencySections/sectionWeightKey");
//        service.setTodoListApi("https://api15preview.sapsf.cn/odata/v2/TodoEntryV2?$filter=userId eq 'SDAPI' and status eq '2'&$format=json&$expand=formContentNav/pmReviewContentDetail/competencySections/competencies/officialRating&$format=Json&$orderby=createdDate asc&$select=formContentNav/pmReviewContentDetail/competencySections/competencies,todoEntryId,subjectId,formContentNav/pmReviewContentDetail/competencySections/sectionIndex,formContentNav/pmReviewContentDetail/competencySections/sectionWeightKey,status");
//        service.setSendFormIdToNextStepApi("https://api15preview.sapsf.cn/odata/v2/sendToNextStep?formDataId={}L");
//        service.setCompetencyUpdateApi("https://api15preview.sapsf.cn/odata/v2/upsert");

        service.setFormHeaderApi("https://api15preview.sapsf.cn/odata/v2/FormHeader?&$format=Json&$filter=formDataStatus eq '3' and formTemplateId eq '805'&$orderby=creationDate desc&$select=formSubjectId,formDataStatus,formTemplateId,rating,creationDate,formDataId");
//        service.setPreviousTalentReviewResultApi("https://api15preview.sapsf.cn/odata/v2/User?&$filter=userId eq '{}'&$format=Json&$select=userId,matrix1Label");
//        service.setAssessmentCenterEvaluationResultApi("https://api15preview.sapsf.cn/odata/v2/cust_ACAssessment?&$filter=externalCode eq '{}'&$format=Json&$select=externalCode,cust_TypeNav/label_zh_CN,cust_ACResultNav/label_zh_CN,cust_AssessmentDate&$expand=cust_ACResultNav,cust_TypeNav");
//        service.setKeyProjectsListLeftApi("https://api15preview.sapsf.cn/odata/v2/Background_Program?$filter=userId eq '{}'&$format=Json&$select=userId,Program,startDate,bgOrderPos&$orderby=bgOrderPos asc");
//        service.setKeyProjectsListRightApi("https://api15preview.sapsf.cn/odata/v2/Background_Hprogram?$filter=userId eq '{}'&$format=Json&$select=userId,startDate");
//        service.setCourseRecordListApi("https://api15preview.sapsf.cn/odata/v2/DevGoal_2001?$format=json&$filter=userId eq '{}'&$expand=milestones");

//        //人才卡 4.6.3 能力分数信息
//        @Value("${sf.sync.formHeaderApi:https://api15preview.sapsf.cn/odata/v2/FormHeader?&$format=Json&$filter=formSubjectId eq '{}' and formDataStatus eq '3' and formTemplateId eq '805'&$orderby=creationDate desc&$select=formSubjectId,formDataStatus,formTemplateId,rating,creationDate}")
//        private String formHeaderApi;
//        //人才卡 4.7 过往人才盘点结果
//        @Value("${sf.sync.previousTalentReviewResultApi:https://api15preview.sapsf.cn/odata/v2/User?&$filter=userId eq '{}'&$format=Json&$select=userId,matrix1Label}")
//        private String previousTalentReviewResultApi;
//        //人才卡 4.8 评价中心测评结果
//        @Value("${sf.sync.assessmentCenterEvaluationResultApi:}")
//        private String assessmentCenterEvaluationResultApi;
//        //人才卡 4.9 参与的重点项目
//        @Value("${sf.sync.keyProjectsListApi:}")
//        private String keyProjectsListApi ;
//        //人才卡 4.10 本岗位本年度培训记录
//        @Value("${sf.sync.courseRecordListApi:https://api15preview.sapsf.cn/odata/v2/DevGoal_2001?$format=json&$filter=userId eq '{}'&$expand=milestones}")
//        private String courseRecordListApi;

        //1.	能力分数存储
        //抓取员工信息 测试是从1000 ~ 1061
        //select concat('simpleInfoList.add(new HrsfUserSimpleVO("',staff_id,'","',position_level,'"));') from hrsf_user_base
        // where staff_id is not null and  position_level is not null order by 1;
        List<HrsfUserSimpleVO> simpleInfoList = Lists.newArrayList();
        simpleInfoList.add(new HrsfUserSimpleVO("00218","21"));
//        simpleInfoList.add(new HrsfUserSimpleVO("00173","101759"));
//        simpleInfoList.add(new HrsfUserSimpleVO("00218","101625"));
//        simpleInfoList.add(new HrsfUserSimpleVO("00365","101334"));

        List<HrsfPmsTalentCard> talentCardInfoList = service.getFormHeader(simpleInfoList);

        //应为能力标准名称解析失败从而要忽略的员工 供发送表单到下一步使用
        Set<String> ignoreStaffIdSet = Sets.newHashSet();
        //1.1	能力标准 测试通过
        List<HrsfPmsUserCompetency> competencyDefIDList = service.findCompetencyDefinition(simpleInfoList, ignoreStaffIdSet);
        //1.2	能力评分 测试通过
        List<HrsfPmsUserCompetency> userCompetencyList = service.findUserCompetency(simpleInfoList, competencyDefIDList);
        //写入本地数据库 此数据是人才卡功能的基础数据  未测试
        //getBaseMapper().truncate(); //  未测试
        //this.saveBatch(userCompetencyList);//  未测试

        //后面几步都按员工处理
        List<CompetencyIds> idsList = Lists.newArrayList();
        List<CompetencySections> sectionsList = Lists.newArrayList();
        //步骤1：读取集成账号待办信息 一次性查询所有员工的待办信息 测试通过
//        service.findTodoInfo(idsList, sectionsList, userCompetencyList);
//        //2.年底能力分数回写 回写接口响应错误 需要调试
//        service.backUpdate(idsList);
//        //3.区间权重写入 回写接口响应错误 需要调试
//        service.weightUpdate(sectionsList);
//        //4.发送表单到下一步 回写接口响应错误 需要调试
//        service.sendForm2Next(sectionsList, ignoreStaffIdSet);

//        Boolean sync = true;
//        int i=0;
//        while (sync) {
//            //后面几步都按员工处理
//            sync = findTodoInfo(idsList, sectionsList, userCompetencyList, i * size, size);
//            i++;
//        }
//        //2.年底能力分数回写
//        backUpdate(idsList);
//        //3.区间权重写入
//        weightUpdate(sectionsList);
//        //4.发送表单到下一步
//        sendForm2Next(sectionsList, ignoreStaffIdSet);
//
//        //人才卡需要的信息
//        //人才卡 4.6.3 能力分数信息
//        List<HrsfPmsTalentCard> talentCardInfoList = getFormHeader(simpleInfoList);
//        //人才卡 4.7 过往人才盘点结果
//        getPreviousTalentReviewResult(talentCardInfoList);
//        //人才卡 4.8 评价中心测评结果
//        getAssessmentCenterEvaluationResult(talentCardInfoList);
//        getBaseMapper().truncateTalentCard();
//        hrsfPmsTalentCardService.saveBatch(talentCardInfoList);
//        //人才卡 4.9 参与的重点项目
//        List<HrsfPmsTalentCardKeyProject> talentCardKeyProjectList = getKeyProject(simpleInfoList);
//        getBaseMapper().truncateKeyProject();
//        hrsfPmsTalentCardKeyProjectService.saveBatch(talentCardKeyProjectList);
//        //人才卡 4.10 本岗位本年度培训记录
//        List<HrsfPmsTalentCardCourseRecord> talentCardCourseRecordList = getCourseRecord(simpleInfoList);
//        getBaseMapper().truncateCourseRecord();
//        this.hrsfPmsTalentCardCourseRecordService.saveBatch(talentCardCourseRecordList);
//        log.info("执行生成人才卡结束时间:{}", LocalDateTime.now());

        //人才卡需要的信息
        //人才卡 4.6.3 能力分数信息
//        List<HrsfPmsTalentCard> talentCardInfoList1 = service.getFormHeader(simpleInfoList);
        if (talentCardInfoList.isEmpty()) {
            for (HrsfUserSimpleVO hrsfUserSimpleVO : simpleInfoList) {
                HrsfPmsTalentCard talentCard = new HrsfPmsTalentCard();
                talentCard.setStaffId(hrsfUserSimpleVO.getStaffId());
                talentCard.setCompetencyScore("4.3");
                talentCardInfoList.add(talentCard);
            }
        }
        //人才卡 4.7 过往人才盘点结果
        service.getPreviousTalentReviewResult(talentCardInfoList);
        //人才卡 4.8 评价中心测评结果
        service.getAssessmentCenterEvaluationResult(talentCardInfoList);
        //人才卡 4.9 参与的重点项目
        List<HrsfPmsTalentCardKeyProject> talentCardKeyProjectList = service.getKeyProject(simpleInfoList);
        //人才卡 4.10 本岗位本年度培训记录
        List<HrsfPmsTalentCardCourseRecord> talentCardCourseRecordList = service.getCourseRecord(simpleInfoList);
    }

    //以下set方法均为测试试用
    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public void setSdhrUser(String sdhrUser) {
        this.sdhrUser = sdhrUser;
    }

    public void setSdhrPwd(String sdhrPwd) {
        this.sdhrPwd = sdhrPwd;
    }

    public void setCompetencyDefApi(String competencyDefApi) {
        this.competencyDefApi = competencyDefApi;
    }

//    public void setAssertionApi(String assertionApi) {
//        this.assertionApi = assertionApi;
//    }
//
//    public void setAssertionApiParam(String assertionApiParam) {
//        this.assertionApiParam = assertionApiParam;
//    }
//
//    public void setAccessTokenApiParam(String accessTokenApiParam) {
//        this.accessTokenApiParam = accessTokenApiParam;
//    }
//
//    public void setAccessTokenApi(String accessTokenApi) {
//        this.accessTokenApi = accessTokenApi;
//    }

    public void setJdmIdApi(String jdmIdApi) {
        this.jdmIdApi = jdmIdApi;
    }

    public void setCompetencyRatingApi(String competencyRatingApi) {
        this.competencyRatingApi = competencyRatingApi;
    }

    public void setTodoListApi(String todoListApi) {
        this.todoListApi = todoListApi;
    }

    public void setSendFormIdToNextStepApi(String sendFormIdToNextStepApi) {
        this.sendFormIdToNextStepApi = sendFormIdToNextStepApi;
    }

    public void setCompetencyUpdateApi(String competencyUpdateApi) {
        this.competencyUpdateApi = competencyUpdateApi;
    }

    public void setFormHeaderApi(String formHeaderApi) {
        this.formHeaderApi = formHeaderApi;
    }

    public void setPreviousTalentReviewResultApi(String previousTalentReviewResultApi) {
        this.previousTalentReviewResultApi = previousTalentReviewResultApi;
    }

    public void setAssessmentCenterEvaluationResultApi(String assessmentCenterEvaluationResultApi) {
        this.assessmentCenterEvaluationResultApi = assessmentCenterEvaluationResultApi;
    }

    public void setKeyProjectsListLeftApi(String keyProjectsListLeftApi) {
        this.keyProjectsListLeftApi = keyProjectsListLeftApi;
    }

    public void setKeyProjectsListRightApi(String keyProjectsListRightApi) {
        this.keyProjectsListRightApi = keyProjectsListRightApi;
    }

    public void setCourseRecordListApi(String courseRecordListApi) {
        this.courseRecordListApi = courseRecordListApi;
    }

    public void setProxyHost(String proxyHost) {
        this.proxyHost = proxyHost;
    }

    public void setProxyPort(int proxyPort) {
        this.proxyPort = proxyPort;
    }

    public void setProxyEnable(boolean proxyEnable) {
        this.proxyEnable = proxyEnable;
    }

//----------------------测试代码结束-----------------------

    /**
     * 返回与员工无关的能力Id、表单能力ID、能力分类、能力中文名称、能力英文名称、能力排序、能力标准分数
     */
    //测试通过
    private List<HrsfPmsUserCompetency> findCompetencyDefinition(List<HrsfUserSimpleVO> simpleInfoList, Set<String> ignoreStaffIdSet) {
        Map<String, List<String>> positionLevelMap = simpleInfoList.stream().filter(data -> StrUtil.isNotBlank(data.getJobCode())).collect(Collectors.groupingBy(HrsfUserSimpleVO::getJobCode, Collectors.mapping(HrsfUserSimpleVO::getStaffId, Collectors.toList())));
        //能力定义全集
        List<HrsfPmsUserCompetency> result = Lists.newArrayList();
        Set<String> competencySet = Sets.newHashSet();
        for (Map.Entry<String, List<String>> entry : positionLevelMap.entrySet()) {
            String s = entry.getKey();
            for (int i = 0; i < retryCount; i++) {//第一次查询401,重复查询又好了 这里至少要查2次
                String format = StrUtil.format(competencyDefApi, s);
                try {
                    log.info("获取competencyDefinition 的url:{}", format);
                    HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                    if (response.getStatus() != 200) {
                        log.warn("\n获取competencyDefinition,fail httpStatus:{}", response.getStatus());
                        continue;
                    }
                    String body = response.body();
                    log.info("获取competencyDefinition 职位分类:{},body:{}", s, body);
                    //取能力第一个结果，第一个是按时间倒序排列的 是最新的
                    JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                    if (jsonArray == null || jsonArray.size() == 0) {
                        log.warn("\nquery : {}\n d.resut[0] is null", format);
                        break;
                    }
                    //循环获取所有能力类型
                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(j);
                        String competency = jsonObject.getStr("competency", "");
                        if (competencySet.contains(s + "-" + competency) || StringUtils.isBlank(competency)) {
                            continue;
                        }
                        //同
                        competencySet.add(s + "-" + competency);
                        HrsfPmsUserCompetency hrsfPmsUserCompetency = new HrsfPmsUserCompetency();
                        //标记当前能力对应的jobCode
                        hrsfPmsUserCompetency.setJobCode(s);
                        //1、获取能力ID
                        hrsfPmsUserCompetency.setCompetency(competency);
                        //2、	获取能力对应的标准分数，接口获取的分数是100分制，人才卡最终展示5分制；（在存储过程中）
                        //对应关系：
                        //API分数	0	25	50	75	100
                        //人才卡雷达图分数	1	2	3	4	5
                        setStandScore(jsonObject, hrsfPmsUserCompetency);
                        //4、获取能力名称
                        setCompetencyNameInfo(jsonObject, hrsfPmsUserCompetency, entry.getValue(), ignoreStaffIdSet);
                        result.add(hrsfPmsUserCompetency);
                    }
                    break;
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(s, e.getMessage(), e);
                        continue;
                    }
                }
            }
        }
        //5、获取表单能力ID
        setJdmId(result);
        log.info("setJdmId(result)={}",JSONUtil.toJsonStr(result));
        return result;
    }

    //测试通过
    private void setJdmId(List<HrsfPmsUserCompetency> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        //TODO 根据下面的调用来确定是否需要第一第二步 文档说需要，实际测试不需要
        //第一步获取assertion值
        //String assertionValue = getAssertion();
        //第二步获取accessToken值
        //String accessToken = getAccessToken(assertionValue);
        //第三步获取能力表单ID值
        List<String> competencyList = result.stream().map(HrsfPmsUserCompetency::getCompetency).distinct().collect(Collectors.toList());
        Map<String, String> map = Maps.newHashMap();
        //TODO 后续优化可修改成并行流
        for (String competency : competencyList) {
            for (int i = 0; i < retryCount; i++) {
                try {
                    //设置能力ID
                    String format = StrUtil.format(jdmIdApi, competency);
                    log.info("获取能力表单ID值 req url:{}", format);
                    //文档要求调用方式 调用结果： Credentials are required for authentication. Please provide the username, password, and company ID for authentication.
                    //String jdmIdBody = HttpRequest.post(format).auth(accessToken).execute().body();
                    //经测试这个可以调用
                    String jdmIdBody = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                    log.info("获取能力表单ID值,res.body:{}", jdmIdBody);
                    JSONArray jsonArray = JSONUtil.parseObj(jdmIdBody).getJSONArray("value");
                    if (!CollectionUtils.isEmpty(jsonArray)) {
                        //获取表单能力ID
                        String jdmId = jsonArray.getJSONObject(0).getStr("jdmId");
                        if (StringUtils.isNotBlank(jdmId)) {
                            map.put(competency, jdmId);
                        }
                    } else {
                        map.put(competency, CompetencyJdmIdMap.get(competency));
                    }
                    break;
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
        //);
        for (HrsfPmsUserCompetency hrsfPmsUserCompetency : result) {
            hrsfPmsUserCompetency.setJdmId(map.get(hrsfPmsUserCompetency.getCompetency()));
        }
    }
//TODO 后续如果确实不需要可以把此部分删掉
//
//    //测试通过
//    private String getAccessToken(String assertionValue) {
//        for (int i = 0; i < retryCount; i++) {
//            try {
//                log.debug("获取accessToken req url:{}", accessTokenApi);
//                String tokenBody = HttpRequest.post(accessTokenApi)
//                        .form(stringParams2Map(accessTokenApiParam))
//                        .form("assertion",assertionValue)
//                        .basicAuth(userName, pwd).execute().body();
//                log.debug("获取accessToken,res.body:{}", tokenBody);
//                //token3小时有效
//                return JSONUtil.parseObj(tokenBody).getStr("access_token");
//            }catch (Exception e){
//                if( i == retryCount -1 ) {
//                    log.error(e.getMessage(), e);
//                    throw e;
//                }
//            }
//        }
//        throw new RuntimeException("getAccessToken fail");
//    }
//
//    //测试通过
//    private String getAssertion() {
//        for (int i = 0; i < retryCount; i++) {
//            try {
//                log.debug("获取assertion req url:{}", assertionApi);
//                String assertionBody = HttpRequest.post(assertionApi).form(stringParams2Map(assertionApiParam)).execute().body();
//                log.debug("获取assertion,res.body:{}", assertionBody);
//                return assertionBody;
//            }catch (Exception e){
//                if( i == retryCount -1 ) {
//                    log.error(e.getMessage(), e);
//                    throw e;
//                }
//            }
//        }
//        throw new RuntimeException("getAccessToken fail");
//    }

    //设置名称信息
    private void setCompetencyNameInfo(JSONObject jsonObject, HrsfPmsUserCompetency hrsfPmsUserCompetency, List<String> staffIdList, Set<String> ignoreStaffIdSet) {
        JSONObject competencyNav = jsonObject.getJSONObject("competencyNav");
        String name = competencyNav.getStr("name_zh_CN");
        log.info("获取到的name格式:{}", name);
        hrsfPmsUserCompetency.setName(name);//后续获根待办数据关联需要
        try {
            //name_zh_CN 规则： 排序序号-中文分类-中文名称\n英文分类-英文名称  约定只能有一个\n
            int index = name.indexOf("\n");
            //如果有则截取英文名称信息
            //剔除可能的尾部\n
            String[] enNames = name.substring(index + 1).replace("\\n", "").split("-");
            hrsfPmsUserCompetency.setCompetencyCategoryEn(enNames[1].trim());//英文分类
            if(enNames.length==4){
                hrsfPmsUserCompetency.setCompetencyNameEN(enNames[1].trim() + "-" + enNames[2].trim()+"-"+enNames[3].trim());
            }else{
                hrsfPmsUserCompetency.setCompetencyNameEN(enNames[1].trim() + "-" + enNames[2].trim());//英文名称
            }

            //截取中文名称信息
            name = index == -1 ? name : name.substring(0, index);
            String[] names = name.split("-");
            //中文名称
            hrsfPmsUserCompetency.setCompetencyWeight(Integer.parseInt(names[0]));//排序
            hrsfPmsUserCompetency.setCompetencyCategory(names[1]);//分类
            hrsfPmsUserCompetency.setCompetencyNameCN(names[1] + "-" + names[2]);//中文名称
        } catch (Exception e) {
            hrsfPmsUserCompetency.setCompetencyCategory(name);
            hrsfPmsUserCompetency.setCompetencyCategoryEn(name);
            hrsfPmsUserCompetency.setCompetencyNameCN(name);
            hrsfPmsUserCompetency.setCompetencyNameEN(name);
            log.info("能力名称解析失败: {} error:{} 名称命名规则: 排序序号-中文分类-中文名称\\n英文分类-英文名称", name, e.getMessage());
            log.debug("能力名称解析失败: {} error:{},忽略的员工: {}", name, e.getMessage(), String.join(",", staffIdList), e);
            ignoreStaffIdSet.addAll(staffIdList);
        }
    }

    //设置标准分数
    private void setStandScore(JSONObject jsonObject, HrsfPmsUserCompetency hrsfPmsUserCompetency) {
        //对应关系：
        //API分数	0	25	50	75	100
        //人才卡雷达图分数	1	2	3	4	5
        switch (jsonObject.getStr("rating_localized", "null")) {
            case "0":
                hrsfPmsUserCompetency.setScoreStandard("1");
                break;
            case "25":
                hrsfPmsUserCompetency.setScoreStandard("2");
                break;
            case "50":
                hrsfPmsUserCompetency.setScoreStandard("3");
                break;
            case "75":
                hrsfPmsUserCompetency.setScoreStandard("4");
                break;
            case "100":
                hrsfPmsUserCompetency.setScoreStandard("5");
                break;
            default: {
                log.error("标准分数没有找到 competency:{}", hrsfPmsUserCompetency.getCompetency());
            }
        }
    }

    //测试通过
    private List<HrsfPmsUserCompetency> findUserCompetency(List<HrsfUserSimpleVO> simpleInfoList, List<HrsfPmsUserCompetency> competencyDefIDList) {
        List<HrsfPmsUserCompetency> result = Lists.newArrayList();
        //TODO 后续优化可修改成并行流
        //循环员工号
        simpleInfoList.forEach(user -> {
            //取出对应jobcode的能力项
            List<HrsfPmsUserCompetency> sameJobCodeCompetencyList =
                    competencyDefIDList.stream().filter(obj -> user.getJobCode().equals(obj.getJobCode())).collect(Collectors.toList());
            log.info("取出对应jobcode的能力项:{}", JSONUtil.toJsonStr(sameJobCodeCompetencyList));
            //获取员工能力分数
            List<HrsfPmsUserCompetency> pmsUserCompetencyList = findUserCompetency(user, sameJobCodeCompetencyList);
            result.addAll(pmsUserCompetencyList);
        });
        return result;
    }

    //测试通过
    private List<HrsfPmsUserCompetency> findUserCompetency(HrsfUserSimpleVO user, List<HrsfPmsUserCompetency> competencyList) {
        List<HrsfPmsUserCompetency> returnList=new ArrayList<>();
        Map<String, String> guidRatingMap = new HashMap<>();
        Map<String, String> guidModifiedDateMap = new HashMap<>();
        try {
            String format = StrUtil.format(competencyRatingApi, user.getStaffId());
            log.info("获取员工能力分数 req url:{}", format);
            String competencyRatingBody;
            try {
                competencyRatingBody = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable)
                        .setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
            } catch (HttpException e) {
                competencyRatingBody = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable)
                        .setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
            }
            log.info("获取员工能力分数,res.body:{}", competencyRatingBody);
            JSONArray jsonArray = JSONUtil.parseObj(competencyRatingBody).getJSONObject("d").getJSONArray("results");
            if (!CollectionUtils.isEmpty(jsonArray)) {
                List<CompetencyRatingDTO> ratingDTOList = JSONUtil.toList(jsonArray, CompetencyRatingDTO.class);
                ratingDTOList.sort(Comparator.comparing(CompetencyRatingDTO::getLastModifiedDateTime));
                log.info("ratingDTOList={}",JSONUtil.toJsonStr(ratingDTOList));
                guidRatingMap = ratingDTOList.stream().collect(Collectors.toMap(CompetencyRatingDTO::getGuid, CompetencyRatingDTO::getRating, (old, cur) -> cur));
                log.info("guidRatingMap={}",JSONUtil.toJsonStr(guidRatingMap));
                guidModifiedDateMap = ratingDTOList.stream().collect(Collectors.toMap(CompetencyRatingDTO::getGuid, CompetencyRatingDTO::getLastModifiedDateTime, (old, cur) -> cur));
                log.info("guidModifiedDateMap={}",JSONUtil.toJsonStr(guidModifiedDateMap));
            } else {
                log.debug("员工能力分数没有找到 staffId: {} ", user.getStaffId());
            }
        } catch (Exception e) {
            log.error("获取员工能力分数失败:{}", e);
        }
        Map<String, String> finalGuidRatingMap = guidRatingMap;
        Map<String, String> finalModifiedDateMap = guidModifiedDateMap;
        log.info("finalModifiedDateMap={}",finalModifiedDateMap);
        try {
            competencyList.stream().forEach(competency -> {
                HrsfPmsUserCompetency pmsUserCompetency = HrsfUpmsConvert.INSTANCE.toHrsfPmsUserCompetency(competency);
                pmsUserCompetency.setStaffId(user.getStaffId());
                pmsUserCompetency.setId(user.getStaffId() + "-" + competency.getCompetency());
                if (CompetencyJdmIdMap.containsKey(competency.getCompetency())) {
                    pmsUserCompetency.setScore(finalGuidRatingMap.get(competency.getJdmId()));
                    pmsUserCompetency.setLastModifiedDate(dateConvert(finalModifiedDateMap.get(competency.getJdmId())));
                    log.info("员工能力分数已有能力ID进行评分={}",JSONUtil.toJsonStr(pmsUserCompetency));
                } else {
                    pmsUserCompetency.setScore(finalGuidRatingMap.get(competency.getCompetency()));
                    pmsUserCompetency.setLastModifiedDate(dateConvert(finalModifiedDateMap.get(competency.getCompetency())));
                    log.info("员工能力分数未新能力ID进行评分={}",JSONUtil.toJsonStr(pmsUserCompetency));
                }
                returnList.add(pmsUserCompetency);
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("发现错误{}",e.getMessage());
        }
         log.info("returnList.size={}",returnList.size());
        return returnList;
    }

    //测试通过
    //一次性查询所有员工的待办信息
    private boolean findTodoInfo(List<CompetencyIds> idsList, List<CompetencySections> sectionsList, List<HrsfPmsUserCompetency> userCompetencyList,long current,long size) {
        Map<String, List<HrsfPmsUserCompetency>> userCompetencyMap = userCompetencyList.stream().collect(groupingBy(item -> item.getStaffId() + DELIMITER + item.getJdmId()));
        for (int i = 0; i < retryCount; i++) {
            try {
                //这里要求层层循环获取
                String todoListApiNew = StrUtil.format(todoListApi, current, size);
                log.info("读取集成账号待办信息,req url:{}", todoListApiNew);
                String todoListBody = new HttpRequestCustom(todoListApiNew).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                log.debug("读取集成账号待办信息,res.body:{}", todoListBody);
                JSONArray jsonArray = JSONUtil.parseObj(todoListBody).getJSONObject("d").getJSONArray("results");
                for (int j = 0; j < jsonArray.size(); j++) {
                    String subjectId = jsonArray.getJSONObject(j).getStr("subjectId");//员工工号
                    JSONArray formContentNav = jsonArray.getJSONObject(j).getJSONObject("formContentNav").getJSONArray("results");
                    for (int k = 0; k < formContentNav.size(); k++) {
                        JSONArray pmReviewContentDetail = formContentNav.getJSONObject(k).getJSONObject("pmReviewContentDetail").getJSONArray("results");
                        for (int l = 0; l < pmReviewContentDetail.size(); l++) {
                            JSONArray competencySections = pmReviewContentDetail.getJSONObject(l).getJSONObject("competencySections").getJSONArray("results");
                            for (int m = 0; m < competencySections.size(); m++) {
                                JSONObject jsonObject = competencySections.getJSONObject(m);
                                JSONArray competencies = jsonObject.getJSONObject("competencies").getJSONArray("results");
                                if (CollectionUtils.isEmpty(competencies)) {
                                    continue;
                                }
                                int sectionIndex = jsonObject.getInt("sectionIndex", 0);
                                String sectionWeightKey = jsonObject.getStr("sectionWeightKey", "");
                                for (int n = 0; n < competencies.size(); n++) {
                                    //如表单区间4存在对应能力，则进行表单能力分数回写，若不存在则进行下一步
                                    if (sectionIndex == 3 || sectionIndex == 4) {
                                        JSONObject item = competencies.getJSONObject(n);
                                        CompetencySections sections = new CompetencySections();
                                        sections.setSubjectId(subjectId);
                                        sections.setFormDataId(item.getStr("formDataId"));
                                        sections.setFormContentId(item.getStr("formContentId"));
                                        sections.setSectionIndex(sectionIndex + "");
                                        sections.setSectionWeightKey(sectionWeightKey);
                                        sections.setSectionWeight(item.getInt("weight", 0));
                                        sectionsList.add(sections);
                                    }
                                    if (sectionIndex == 4) {
                                        JSONObject item = competencies.getJSONObject(n);
                                        String key = subjectId + DELIMITER + item.getStr("itemId", "");
                                        List<HrsfPmsUserCompetency> userCompetencies = userCompetencyMap.get(key);
                                        if (!CollectionUtils.isEmpty(userCompetencies)) {
                                            CompetencyIds ids = new CompetencyIds();
                                            ids.setSubjectId(subjectId);
                                            ids.setItemId(item.getStr("itemId"));
                                            ids.setFormDataId(item.getStr("formDataId"));
                                            ids.setFormContentId(item.getStr("formContentId"));
                                            JSONObject officialRating = item.getJSONObject("officialRating");
                                            ids.setRating(userCompetencies.get(0).getScore());
                                            ids.setRatingKey(officialRating.getStr("ratingKey"));
                                            idsList.add(ids);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                return jsonArray.size()>0;
            } catch (Exception e) {
                //第二次请求超时，在输出错误日志
                if (i == 1) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return true;
    }

    //2.年底能力分数回写
    private void backUpdate(List<CompetencyIds> idsList) {
        if (CollectionUtils.isEmpty(idsList)) {
            return;
        }
        //按员工按分组处理 根据请求json需要分组来发送
        Map<String, List<CompetencyIds>> groupMap = idsList.stream().collect(groupingBy(item -> item.getSubjectId() + DELIMITER + item.getFormContentId() + DELIMITER + item.getFormDataId()));
        //TODO 后续优化可修改成并行流
        for (Map.Entry<String, List<CompetencyIds>> entry : groupMap.entrySet()) {
            String formContentId = "";
            String formDataId = "";
            List<String> competenciesList = Lists.newArrayList();
            for (CompetencyIds item : entry.getValue()) {
                formContentId = item.getFormContentId();
                formDataId = item.getFormDataId();
                String itemId = item.getItemId();
                String ratingKey = item.getRatingKey();
                String rating = item.getRating();
                //json参见需求文档 表单能力分数回写
                String competenciesArrayJSONString = "{\"__metadata\":{\"uri\":\"FormCompetency(formContentId=" + formContentId + ",formDataId=" + formDataId + ",itemId=" + itemId + ",sectionIndex=4)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId=" + formContentId + ",formDataId=" + formDataId + ",itemId=" + itemId + ",ratingType='official',sectionIndex=4)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"" + ratingKey + "\",\"rating\":\"" + rating + "\"}}";
                competenciesList.add(competenciesArrayJSONString);
            }
            String json = "{\"__metadata\":{\"uri\":\"FormCompetencySection(formContentId=" + formContentId + ",formDataId=" + formDataId + ",sectionIndex=4)\",\"type\":\"SFOData.FormCompetencySection\"},\"competencies\":[" + String.join(",", competenciesList) + "]}";
            for (int i = 0; i < retryCount; i++) {
                try {
                    log.info("年底能力分数回写 req url:{},body:{}", competencyUpdateApi, json);
                    HttpResponse response = new HttpRequestCustom(competencyUpdateApi).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(json).basicAuth(userName, pwd).execute();
                    String body = response.body();
                    log.error("macros|年底能力分数回写body:{}",body);
                    if (response.getStatus() == 200) {
                        JSONArray jsonArray = JSONUtil.parseObj(body).getJSONArray("d");
                        if (jsonArray != null) {
                            String status = jsonArray.getJSONObject(0).getStr("status");
                            String message = jsonArray.getJSONObject(0).getStr("message");
                            if ("OK".equals(status)) {
                                log.info("年底能力分数回写,success subjectId: {}", entry.getKey());
                                break;
                            } else {
                                log.info("年底能力分数回写,fail subjectId: {},status: {},message: {}", entry.getKey(), status, message);
                            }
                        } else {
                            log.info("年底能力分数回写,fail subjectId: {},responseBody: {}", entry.getKey(), body);
                        }
                    } else {
                        log.info("年底能力分数回写,fail subjectId: {},responseStatus: {},responseBody:{}", entry.getKey(), response.getStatus(), body);
                    }
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    //3.区间权重写入
    private void weightUpdate(List<CompetencySections> sectionsList) {
        if (CollectionUtils.isEmpty(sectionsList)) {
            return;
        }
        Map<String, List<CompetencySections>> groupMap = sectionsList.stream().collect(groupingBy(item -> item.getSubjectId() + DELIMITER + item.getFormContentId() + DELIMITER + item.getFormDataId()));
        for (Map.Entry<String, List<CompetencySections>> entry1 : groupMap.entrySet()) {
            String formContentId = entry1.getValue().get(0).getFormContentId();
            String formDataId = entry1.getValue().get(0).getFormDataId();
            List<String> competenciesList = Lists.newArrayList();
            Map<String, Integer> weightKeyMap = entry1.getValue().stream().collect(groupingBy(item -> item.getSectionIndex() + DELIMITER + item.getSectionWeightKey(), Collectors.summingInt(CompetencySections::getSectionWeight)));
            for (Map.Entry<String, Integer> entry : weightKeyMap.entrySet()) {
                String[] split = entry.getKey().split(DELIMITER);
                String sectionIndex = split[0];
                String sectionWeightKey = split[1];
                int sectionWeight = entry.getValue();
                String competenciesArrayJSONString = "{\"__metadata\":{\"uri\":\"FormCompetencySection(formContentId=" + formContentId + "L,formDataId=" + formDataId + "L,sectionIndex=" + sectionIndex + ")\"},\"sectionWeightKey\":\"" + sectionWeightKey + "\",\"sectionWeight\":\"" + sectionWeight + "\"}";
                competenciesList.add(competenciesArrayJSONString);
            }
            String json = "{\"__metadata\":{\"uri\":\"https://api15preview.sapsf.cn:443/odata/v2/FormPMReviewContentDetail(formContentId=" + formContentId + "L,formDataId=" + formDataId + "L)\",\"type\":\"SFOData.FormPMReviewContentDetail\"},\"competencySections\":[" + String.join(",", competenciesList) + "]}";
            for (int i = 0; i < retryCount; i++) {
                try {
                    log.info("区间权重写入 req url:{},body:{}", competencyUpdateApi, json);
                    HttpResponse response = new HttpRequestCustom(competencyUpdateApi).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(json).basicAuth(userName, pwd).execute();
                    String body = response.body();
                    log.error("macros|区间权重写入body:{}",body);
                    if (response.getStatus() == 200) {
                        JSONArray jsonArray = JSONUtil.parseObj(body).getJSONArray("d");
                        if (jsonArray != null) {
                            String status = jsonArray.getJSONObject(0).getStr("status");
                            String message = jsonArray.getJSONObject(0).getStr("message");
                            if ("OK".equals(status)) {
                                log.info("区间权重写入,success subjectId: {}", entry1.getKey());
                                break;
                            } else {
                                log.info("区间权重写入,fail subjectId: {},status: {},message: {}", entry1.getKey(), status, message);
                            }
                        } else {
                            log.info("区间权重写入,fail subjectId: {},responseBody: {}", entry1.getKey(), body);
                        }
                    } else {
                        log.info("区间权重写入,fail subjectId: {},responseStatus: {},responseBody:{}", entry1.getKey(), response.getStatus(), body);
                    }
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    private void sendForm2Next(List<CompetencySections> idsList, Set<String> ignoreStaffIdSet) {
        //TODO 后续优化可修改成并行流
        Map<String, Set<String>> groupMap = idsList.stream()
                .filter(item -> !ignoreStaffIdSet.contains(item.getSubjectId()))//忽略掉不需要发送到下一步的员工
                .collect(Collectors.groupingBy(CompetencySections::getSubjectId, Collectors.mapping(CompetencySections::getFormDataId, Collectors.toSet())));
        log.info("发送");
        for (Map.Entry<String, Set<String>> entry : groupMap.entrySet()) {
            for (String item : entry.getValue()) {
                for (int i = 0; i < retryCount; i++) {
                    try {
                        String format = StrUtil.format(sendFormIdToNextStepApi, item);
                        log.info("发送表单到下一步 req url:{}", format);
                        HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                        String body = response.body();
                        log.error("macros|发送表单到下一步body:{}",body);
                        if (response.getStatus() == 200 && body != null && body.contains("<d:status>Success</d:status>")) {
                            log.info("发送表单到下一步 success: {} - {}", entry.getKey(), item);
                            break;
                        } else {
                            log.info("发送表单到下一步,fail subjectId: {},responseStatus: {},responseBody:{}", entry.getKey(), response.getStatus(), body);
                        }
                        if (response.getStatus() == 200) {
                            String status = JSONUtil.parseObj(body).getJSONObject("d").getJSONObject("CORouteFormStatusBean").getStr("status");
                            if ("success".equalsIgnoreCase(status)) {
                                log.info("发送表单到下一步 success: {} - {}", entry.getKey(), item);
                                break;
                            } else {
                                log.info("发送表单到下一步,fail subjectId: {},responseStatus: 200,responseBody:{}", entry.getKey(), body);
                            }
                        } else {
                            log.info("发送表单到下一步,fail subjectId: {},responseStatus: {},responseBody:{}", entry.getKey(), response.getStatus(), body);
                        }
                    } catch (Exception e) {
                        //第二次请求超时，在输出错误日志
                        if (i == 1) {
                            log.error(e.getMessage(), e);
                        }
                    }
                }
            }
        }
    }

    //已测试
    //人才卡 4.6.3 能力分数信息
    private List<HrsfPmsTalentCard> getFormHeader(List<HrsfUserSimpleVO> simpleInfoList) {
        List<HrsfPmsTalentCard> result = Lists.newArrayList();

        //TODO simpleInfoList.parallelStream().forEach(hrsfUserSimpleVO ->{});
        for (int i = 0; i < retryCount; i++) {
            try {
                String format = StrUtil.format(formHeaderApi);
                log.info("人才卡-能力分数信息获取 req url:{}", format);
                HttpResponse response = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                String body = response.body();
                log.error("macros|人才卡-能力分数信息获取body:{}",body);
                if (response.getStatus() == 200) {
                    log.info("人才卡-能力分数信息获取,res.body:{}", body);
                    JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                    if (!CollectionUtils.isEmpty(jsonArray)) {
                        List<CompetencyRatingFormHeaderDTO> ratingDTOList = JSONUtil.toList(jsonArray, CompetencyRatingFormHeaderDTO.class);
                        ratingDTOList.stream().forEach(obj->{
//                            log.info("ratingDTOList.obj={}",obj);
                            HrsfPmsTalentCard talentCard = new HrsfPmsTalentCard();
                            talentCard.setStaffId(obj.getFormSubjectId());
                            talentCard.setCompetencyScore(obj.getRating());
                            try{
                                talentCard.setFormDataId(obj.getFormDataId());
                                // 使用Instant从时间戳创建一个时间点
                                long timestamp =  Long.parseLong(dateConvert(obj.getCreationDate()));
                                Instant instant = Instant.ofEpochMilli(timestamp);
                                ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                                String formattedDateStr = zonedDateTime.format(formatter);
                                LocalDateTime localDateTime = LocalDateTime.parse(formattedDateStr, formatter);
                                Date date = new Date(Timestamp.valueOf(localDateTime).getTime());
                                talentCard.setCreationDate(date);
                            }catch(Exception e){
                                e.printStackTrace();
                            }
                            result.add(talentCard);
                        });
                    } else {
                        log.info("人才卡-能力分数信息获取,res.status: {},res.body: {}", response.getStatus(), body);
                    }
                    break;
                }
            } catch (Exception e) {
                //如果是第二次超时，则在打印超时日志
                if (i == 1) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        log.info("talentCard|result={}",JSONUtil.toJsonStr(result));
        return result;
    }

    private static String dateConvert(String dateStr){
        String dateTimePattern = "^/Date\\((\\d+)\\+\\d+\\)/$";
        String datePattern = "^/Date\\((\\d+)\\)/$";
        String timestampStr = null;
        try {
            if (Pattern.matches(dateTimePattern,dateStr)) {
                timestampStr  = dateStr.replaceAll("^/Date\\((\\d+)\\+\\d+\\)/$", "$1");
            }else if (Pattern.matches(datePattern,dateStr)){
                timestampStr  = dateStr.replaceAll("^/Date\\((\\d+)\\)/$", "$1");
            }else {
                log.info("未匹配相应的时间字符串={}",dateStr);
            }

            // 将时间戳字符串转换为长整数（毫秒）
            long timestamp = Long.parseLong(timestampStr);
//            log.info("时间戳（毫秒）: " + timestamp);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("无法解析日期字符串");
        }
        return timestampStr;
    }

    //已确认
    //人才卡 4.7 过往人才盘点结果
    private void getPreviousTalentReviewResult(List<HrsfPmsTalentCard> talentCardList) {
        for (HrsfPmsTalentCard talentCard : talentCardList) {
            for (int i = 0; i < retryCount; i++) {
                try {
                    String format = StrUtil.format(previousTalentReviewResultApi, talentCard.getStaffId());
                    log.info("人才卡-过往人才盘点结果 req url:{}", format);
                    HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                    String body = response.body();
                    log.error("macros|人才卡-过往人才盘点结果body:{}",body);
                    if (response.getStatus() == 200) {
                        log.info("人才卡-过往人才盘点结果,res.body:{}", body);
                        JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                        if (!CollectionUtils.isEmpty(jsonArray)) {
                            //Needs Coaching/需要辅导	    Emerging Star/新星	S           tar/明星
                            //Marginal Performer/潜力达标者	Solid Contributor/干将	        Rising Star/后起之秀
                            //Under Performer/绩效较差者	    Acceptable Performer/绩效较好者	Experienced Professional/经验丰富的专业人员
                            talentCard.setReviewResult(jsonArray.getJSONObject(0).getStr("matrix1Label", ""));
                            break;
                        }
                    } else {
                        log.info("人才卡-过往人才盘点结果,res.status: {},res.body: {}", response.getStatus(), body);
                    }
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    //人才卡 4.8 评价中心测评结果
    private void getAssessmentCenterEvaluationResult(List<HrsfPmsTalentCard> talentCardList) {
        for (HrsfPmsTalentCard talentCard : talentCardList) {
            for (int i = 0; i < retryCount; i++) {
                try {
                    String format = StrUtil.format(assessmentCenterEvaluationResultApi, talentCard.getStaffId());
                    log.info("人才卡-评价中心测评结果 req url:{}", format);
                    HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                    String body = response.body();
                    log.error("macros|人才卡-评价中心测评结果body:{}",body);
                    if (response.getStatus() == 200) {
                        log.info("人才卡-评价中心测评结果,res.body:{}", body);
                        JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                        if (!CollectionUtils.isEmpty(jsonArray)) {
                            JSONObject jsonObject = jsonArray.getJSONObject(0);
                            JSONArray jsonArray2 = jsonObject.getJSONObject("cust_TypeNav").getJSONArray("results");
                            JSONArray jsonArray3 = jsonObject.getJSONObject("cust_ACResultNav").getJSONArray("results");
                            if (!CollectionUtils.isEmpty(jsonArray2) && !CollectionUtils.isEmpty(jsonArray3)) {
                                talentCard.setPromoteType(jsonArray2.getJSONObject(0).getStr("label_zh_CN"));
                                talentCard.setEvaluateResult(jsonArray3.getJSONObject(0).getStr("label_zh_CN"));
                                String dateStr = jsonObject.getStr("cust_AssessmentDate");
                                talentCard.setEvaluateDate(DateUtil.format(getDate(dateStr), DatePattern.NORM_DATE_PATTERN));
                                break;
                            }
                        }
                    } else {
                        log.info("人才卡-评价中心测评结果,res.status: {},res.body: {}", response.getStatus(), body);
                    }
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(e.getMessage(), e);
                    }
                }
            }
        }
    }

    //人才卡 4.9 参与的重点项目
    private List<HrsfPmsTalentCardKeyProject> getKeyProject(List<HrsfUserSimpleVO> simpleInfoList) {
        List<HrsfPmsTalentCardKeyProject> result = Lists.newArrayList();
        for (HrsfUserSimpleVO hrsfUserSimpleVO : simpleInfoList) {
            getKeyProjectLeft(hrsfUserSimpleVO.getStaffId(), result);
            getKeyProjectRight(hrsfUserSimpleVO.getStaffId(), result);
        }
        return result;
    }

    private void getKeyProjectLeft(String staffId, List<HrsfPmsTalentCardKeyProject> result) {
        for (int i = 0; i < retryCount; i++) {
            try {
                String format = StrUtil.format(keyProjectsListLeftApi, staffId);
                log.info("人才卡-参与的重点项目L req url:{}", format);
                HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                String body = response.body();
                log.error("macros|人才卡-参与的重点项目Body:{}",body);
                if (response.getStatus() == 200) {
                    log.info("人才卡-参与的重点项目L,res.body:{}", body);
                    JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                    if (!CollectionUtils.isEmpty(jsonArray)) {
                        for (int j = 0; j < jsonArray.size(); j++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            HrsfPmsTalentCardKeyProject keyProject = new HrsfPmsTalentCardKeyProject();
                            keyProject.setStaffId(staffId);
                            keyProject.setLeftFlag(true);
                            keyProject.setProject(jsonObject.getStr("Program"));
                            if (StringUtils.isNotBlank(keyProject.getProject())) {//忽略项目名称为空的
                                keyProject.setProjectEN("");
                                String startDate = jsonObject.getStr("startDate");
                                keyProject.setParticipateDate(DateUtil.format(getDate(startDate), DatePattern.NORM_DATE_PATTERN));
                                result.add(keyProject);
                            }
                        }
                        break;
                    }
                } else {
                    log.info("人才卡-参与的重点项目L,res.status: {},res.body: {}", response.getStatus(), body);
                }
            } catch (Exception e) {
                //第二次请求超时，在输出错误日志
                if (i == 1) {
                    log.error(staffId,e.getMessage(), e);
                }
            }
        }
    }

    private void getKeyProjectRight(String staffId, List<HrsfPmsTalentCardKeyProject> result) {
        for (int i = 0; i < retryCount; i++) {
            try {
                String format = StrUtil.format(keyProjectsListRightApi, staffId);
                log.info("人才卡-参与的重点项目R req url:{}", format);
                HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute();
                String body = response.body();
                log.error("macros|人才卡-参与的重点项目R Body:{}",body);
                if (response.getStatus() == 200) {
                    log.info("人才卡-参与的重点项目R,res.body:{}", body);
                    JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                    if (!CollectionUtils.isEmpty(jsonArray)) {
                        for (int j = 0; j < jsonArray.size(); j++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            HrsfPmsTalentCardKeyProject keyProject = new HrsfPmsTalentCardKeyProject();
                            keyProject.setStaffId(staffId);
                            keyProject.setLeftFlag(false);
                            keyProject.setProject(jsonObject.getStr("Program"));
                            if (StringUtils.isNotBlank(keyProject.getProject())) {
                                keyProject.setProjectEN("");
                                String startDate = jsonObject.getStr("startDate");
                                keyProject.setParticipateDate(DateUtil.format(getDate(startDate), DatePattern.NORM_DATE_PATTERN));
                                result.add(keyProject);
                            }
                        }
                        break;
                    }
                } else {
                    log.info("人才卡-参与的重点项目R,res.status: {},res.body: {}", response.getStatus(), body);
                }
            } catch (Exception e) {
                //第二次请求超时，在输出错误日志
                if (i == 1) {
                    log.error(staffId, e.getMessage(), e);
                }
            }
        }
    }

    //人才卡 4.10 本岗位本年度培训记录
    private List<HrsfPmsTalentCardCourseRecord> getCourseRecord(List<HrsfUserSimpleVO> simpleInfoList) {
        List<HrsfPmsTalentCardCourseRecord> result = Lists.newArrayList();
        for (HrsfUserSimpleVO hrsfUserSimpleVO : simpleInfoList) {
            for (int i = 0; i < retryCount; i++) {
                try {
                    String format = StrUtil.format(courseRecordListApi, hrsfUserSimpleVO.getStaffId());
                    log.info("人才卡-本岗位本年度培训记录 req url:{}", format);
                    HttpResponse response = new HttpRequestCustom(format).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sdhrUser, sdhrPwd).execute();
                    String body = response.body();
                    log.error("macros|人才卡-本岗位本年度培训记录 Body:{}",body);
                    if (response.getStatus() == 200) {
                        log.info("人才卡-本岗位本年度培训记录,res.body:{}", body);
                        JSONArray jsonArray = JSONUtil.parseObj(body).getJSONObject("d").getJSONArray("results");
                        if (!CollectionUtils.isEmpty(jsonArray)) {
                            for (int j = 0; j < jsonArray.size(); j++) {
                                JSONObject jsonObject0 = jsonArray.getJSONObject(j).getJSONObject("milestones");
                                if (jsonObject0 != null) {
                                    JSONArray jsonArray2 = jsonObject0.getJSONArray("results");
                                    if (!CollectionUtils.isEmpty(jsonArray)) {
                                        for (int k = 0; k < jsonArray2.size(); k++) {
                                            JSONObject jsonObject = jsonArray2.getJSONObject(k);
                                            HrsfPmsTalentCardCourseRecord courseRecord = new HrsfPmsTalentCardCourseRecord();
                                            courseRecord.setStaffId(hrsfUserSimpleVO.getStaffId());
                                            courseRecord.setCourse(jsonObject.getStr("target"));
                                            courseRecord.setCourseEN("");
                                            String startDateStr = jsonObject.getStr("start", null);
                                            String edDateStr = jsonObject.getStr("due", null);
                                            if (StrUtil.isNotBlank(startDateStr) && !"null".equals(startDateStr)) {
                                                courseRecord.setStartDate(DateUtil.format(getDate(startDateStr), DatePattern.NORM_DATE_PATTERN));
                                            }
                                            if (StrUtil.isNotBlank(edDateStr) && !"null".equals(edDateStr)) {
                                                courseRecord.setEndDate(DateUtil.format(getDate(edDateStr), DatePattern.NORM_DATE_PATTERN));
                                            }
                                            courseRecord.setStatus(jsonObject.getStr("description"));
                                            result.add(courseRecord);
                                        }
                                    }
                                }
                            }
                            break;
                        }
                    } else {
                        log.info("人才卡-本岗位本年度培训记录,res.status: {},res.body: {}", response.getStatus(), body);
                    }
                } catch (Exception e) {
                    //第二次请求超时，在输出错误日志
                    if (i == 1) {
                        log.error(hrsfUserSimpleVO.getStaffId(),e.getMessage(), e);
                    }
                }
            }
        }
        return result;
    }

    private Map<String, Object> stringParams2Map(String paramString) {
        Map<String, Object> params = Maps.newHashMap();
        for (String s : paramString.split("&")) {
            int index = s.indexOf("=");
            String key = s.substring(0, index);
            String value = s.substring(index + 1);
            params.put(key, value);
        }
        return params;
    }

    /**
     * @param s Date(1656979200000)
     * @return java.util.Date
     */
    private Date getDate(String s) {
        s = s.substring(s.indexOf("(") + 1);
        s = s.substring(0, s.indexOf(")"));
        return new Date(Long.valueOf(s));
    }

    //能力分数回写数据对象
    @Data
    static class CompetencyIds {
        private String subjectId;
        private String itemId;
        private String formDataId;
        private String formContentId;
        private String ratingKey;
        private String rating;
    }

    //区间权重写入用户级数据对象
    @Data
    static class CompetencySections {
        private String subjectId;
        private String formDataId;
        private String formContentId;
        private String sectionIndex;
        private String sectionWeightKey;
        private int sectionWeight;
    }
}
