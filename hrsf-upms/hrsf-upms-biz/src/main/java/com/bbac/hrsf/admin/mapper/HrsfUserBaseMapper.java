package com.bbac.hrsf.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfUserSimpleVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.ResultType;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Mapper
public interface HrsfUserBaseMapper extends BaseMapper<HrsfUserBase> {

    @Select("SELECT STAFF_ID,STATUS,USER_NAME,USER_ID,FULL_NAME,SYSTEM,DEPARTMENT,SECTION,USER_GROUP,POSITION,JOB_TITLE,USER_LEVEL,POSITION_LEVEL from HRSF_USER_BASE  where  (regexp_like(FULL_NAME ,#{fullName},'i') or regexp_like(STAFF_ID ,#{fullName},'i')) AND ROWNUM<=100")
    List<HrsfUserBase> selectByFullName(@Param("fullName") String fullName);

    @ResultType(HrsfUserSimpleVO.class)
    @Select("select distinct STAFF_ID as staffId,JOB_CODE as jobCode from HRSF_USER_BASE where POSITION_LEVEL IN ('L2','L3','L4','L5','L6') and JOB_CODE IS NOT NULL and status='t'")
    List<HrsfUserSimpleVO> selectSimpleInfo();

    void updatePhotoBatchById(@Param("userBaseList") List<HrsfUserBase> userBaseList);

    @Update("TRUNCATE TABLE HRSF_USER_BASE")
    void truncateUserRecord();

    @Select("SELECT STAFF_ID,STATUS,USER_NAME,USER_ID,FULL_NAME,SYSTEM,DEPARTMENT,SECTION,USER_GROUP,POSITION,JOB_TITLE,USER_LEVEL,POSITION_LEVEL from HRSF_USER_BASE  where  STAFF_ID=#{staffId}")
    List<HrsfUserBase> selectByStaffId(String staffId);
}
