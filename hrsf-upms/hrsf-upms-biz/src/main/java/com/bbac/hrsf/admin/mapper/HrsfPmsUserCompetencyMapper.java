package com.bbac.hrsf.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserCompetency;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface HrsfPmsUserCompetencyMapper extends BaseMapper<HrsfPmsUserCompetency> {

    @Update("TRUNCATE TABLE HRSF_PMS_USER_COMPETENCY")
    int truncate();

    @Update("TRUNCATE TABLE HRSF_PMS_USER_TALENT_CARD")
    int truncateTalentCard();

    @Update("TRUNCATE TABLE HRSF_PMS_USER_TALENT_CARD_COURSE_RECORD")
    int truncateCourseRecord();

    @Update("TRUNCATE TABLE HRSF_PMS_USER_TALENT_CARD_KEY_PROJECT")
    int truncateKeyProject();

}
