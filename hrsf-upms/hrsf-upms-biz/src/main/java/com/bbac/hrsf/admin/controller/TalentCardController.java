package com.bbac.hrsf.admin.controller;

import com.bbac.hrsf.admin.service.IHrsfPmsTalentCardPDFService;
import com.bbac.hrsf.common.security.annotation.Inner;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/talentCard")
@Api(value = "talentCard", tags = "人才卡PDF文件下载")
public class TalentCardController {

    @Autowired
    private IHrsfPmsTalentCardPDFService hrsfPmsTalentCardPDFService;
    //用户搜索下拉
    //复用 UserController的 @GetMapping("/getUserBaseSelectName") 方法

    //打包下载人才卡
    @ApiOperation("人才卡pdf文件下载")
    @PostMapping("/download")
    @PreAuthorize("@pmsRole.hasPermissionTalentCard()")
    public void reportDownloadPpt(@ApiParam(name = "staffIds",value = "工号集合(不可为空)") @RequestBody List<String> staffIds, HttpServletResponse response) {
        hrsfPmsTalentCardPDFService.download(staffIds, response);
    }
}
