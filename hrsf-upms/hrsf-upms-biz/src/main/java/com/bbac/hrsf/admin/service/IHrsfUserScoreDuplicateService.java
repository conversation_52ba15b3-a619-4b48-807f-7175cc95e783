package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserScoreTask;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;

import java.util.List;

/**
 * <p>
 * 复制上周期绩效
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface IHrsfUserScoreDuplicateService extends IService<HrsfPmsUserScoreTask> {

    /**
     * 启动历史绩效分数任务
     * @param currTaskId 当前任务ID
     * @return message
     */
    String startUserScoreTask(Long currTaskId);

    /**
     * 检查绩效复制任务
     */
    public void checkUserScoreTask();

    /**
     * 获取任务状态
     * @return 获取任务的状态和时间
     */
    HrsfPmsUserScoreTask getCurrentTaskStatus();

}
