package com.bbac.hrsf.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseFilterDTO;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.service.IEmployeePerformanceService;
import com.bbac.hrsf.admin.service.IHrsfOrganizationService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: liu, jie
 * @create: 2022-06-24
 **/
@RestController
@RequiredArgsConstructor
@RequestMapping("/employeePerformance")
@Api(value = "user", tags = "员工绩效考核变更模块")
public class EmployeePerformanceController {

    private final IHrsfUserBaseService hrsfUserBaseService;

    private final IHrsfPmsuserBaseService hrsfPmsuserBaseService;

    private final IEmployeePerformanceService employeePerformanceService;

    /**
     * 员工绩效考核变更 列表
     *
     * @param hrsfPmsUserBaseDTO
     * @return
     */
    @PostMapping("/page")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<HrsfUserVO>> getUserPage(@RequestBody HrsfPmsUserBaseFilterDTO hrsfPmsUserBaseDTO) {
        return R.ok(hrsfUserBaseService.getUserPage(hrsfPmsUserBaseDTO));
    }

    /**
     * 删除表单ID
     * @param hrsfPmsUserBaseDTO
     * @return
     */
    @PostMapping("/removeFormId")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Object> removeFormId(@RequestBody HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO) {
        return R.ok(hrsfPmsuserBaseService.removeFormId(hrsfPmsUserBaseDTO));
    }

    /**
     * 添加人 进校准会议
     * @param hrsfPmsUserBaseDTO
     * @return
     */
    @PostMapping("/addUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<ErrorMessageVo> addUser(@RequestBody HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO) {
        return R.ok(employeePerformanceService.addUser(hrsfPmsUserBaseDTO));
    }

    /**
     * 更新校准会议人员
     * @param hrsfPmsUserBaseDTO
     * @return
     */
    @PostMapping("/updateUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<ErrorMessageVo> updateUser(@RequestBody HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO) {
        return R.ok(employeePerformanceService.updateUser(hrsfPmsUserBaseDTO));
    }

    /**
     * 获取校准会议收件箱详情-筛选器接口
     *
     * @return R
     */
    @GetMapping("/getCalibrationPmsSelect")
    public R<HrsfOrganizationVO> getCalibrationPmsSelect() {
        return R.ok(hrsfUserBaseService.getCalibrationPmsSelect());
    }



}
