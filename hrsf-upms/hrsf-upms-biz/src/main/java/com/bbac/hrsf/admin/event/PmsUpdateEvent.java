/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.event;

import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 启动绩效基础表更新部分字段内容事件
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PmsUpdateEvent {


    private String url;
    private String startDate;
    private String endDate;
    private String assesType;
    private String assesYear;
    private LocalDate startLocalDate;
    private LocalDate endLocalDate;
    /**
     * 事件类别
     */
    private PmsUpdateEventEnum eventEnum;
    private Map<String, Long> baseMap;
    private Map<String, Long> empIdMap;
    private List<String> staffIds;
    /**
     * 是否是新增用户操作
     */
    private Boolean addUserFlag;
    private HrsfPmstart hrsfPmstart;

    public PmsUpdateEvent(String url, String startDate, String assesType, String endDate, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Map<String, Long> empIdMap,HrsfPmstart hrsfPmstart) {
        this.url = url;
        this.startDate = startDate;
        this.endDate = endDate;
        this.eventEnum = eventEnum;
        this.baseMap = baseMap;
        this.assesType = assesType;
        this.empIdMap= empIdMap;
        this.hrsfPmstart= hrsfPmstart;

    }

    public PmsUpdateEvent(String url, String startDate, String assesType, String endDate, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Map<String, Long> empIdMap) {
        this.url = url;
        this.startDate = startDate;
        this.endDate = endDate;
        this.eventEnum = eventEnum;
        this.baseMap = baseMap;
        this.assesType = assesType;
        this.empIdMap= empIdMap;

    }

    public PmsUpdateEvent(String url, LocalDate startDate, LocalDate endDate, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Map<String, Long> empIdMap) {
        this.url = url;
        this.startLocalDate = startDate;
        this.endLocalDate = endDate;
        this.eventEnum = eventEnum;
        this.baseMap = baseMap;
        this.empIdMap=empIdMap;

    }

    public PmsUpdateEvent(String url, String assesYear, String assesType, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Boolean addUserFlag,Map<String, Long> empIdMap) {
        this.url = url;
        this.assesYear = assesYear;
        this.assesType = assesType;
        this.eventEnum = eventEnum;
        this.baseMap = baseMap;
        this.addUserFlag = addUserFlag;
        this.empIdMap = empIdMap;

    }

    public PmsUpdateEvent(String url, PmsUpdateEventEnum eventEnum) {
        this.url = url;
        this.eventEnum = eventEnum;

    }

    public PmsUpdateEvent(PmsUpdateEventEnum eventEnum) {
        this.eventEnum = eventEnum;

    }

    public PmsUpdateEvent(String url, List<String> staffIds, PmsUpdateEventEnum eventEnum) {
        this.url = url;
        this.eventEnum = eventEnum;
        this.staffIds = staffIds;

    }

    public static PmsUpdateEvent of(String url, String startDate, String assesType, String endDate, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Map<String, Long> empIdMap,HrsfPmstart hrsfPmstart) {
        return new PmsUpdateEvent(url, startDate, assesType, endDate, eventEnum, baseMap,empIdMap,hrsfPmstart);
    }

    public static PmsUpdateEvent of(String url, String startDate, String assesType, String endDate, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Map<String, Long> empIdMap) {
        return new PmsUpdateEvent(url, startDate, assesType, endDate, eventEnum, baseMap,empIdMap);
    }

    public static PmsUpdateEvent of(String url, String assesYear, String assesType, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Boolean addUserFlag,Map<String, Long> empIdMap) {
        return new PmsUpdateEvent(url, assesYear, assesType, eventEnum, baseMap,addUserFlag,empIdMap);
    }


    public static PmsUpdateEvent of(String url, PmsUpdateEventEnum eventEnum) {
        return new PmsUpdateEvent(url, eventEnum);
    }

    public static PmsUpdateEvent of(String url, LocalDate startDate, LocalDate endDate, PmsUpdateEventEnum eventEnum, Map<String, Long> baseMap,Map<String, Long> empIdMap) {
        return new PmsUpdateEvent(url, startDate, endDate, eventEnum, baseMap,empIdMap);
    }

    public static PmsUpdateEvent of(String url, List<String> staffIds, PmsUpdateEventEnum eventEnum) {
        return new PmsUpdateEvent(url, staffIds, eventEnum);
    }

    public static PmsUpdateEvent of(PmsUpdateEventEnum eventEnum) {
        return new PmsUpdateEvent(eventEnum);

    }
}
