package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.vo.*;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import com.bbac.hrsf.performance.api.dto.CalibrationListQueryDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
public interface IHrsfPmsuserBaseService extends IService<HrsfPmsuserBase> {

    /**
     * 开启绩效员工基础表
     *
     * @param assesType
     * @return
     */
    Boolean startPmsUser(HrsfPmstart assesType,List<String> staffIdList);

    List<HrsfPmsuserBase> getPmsUserList(String assesYear, String assesType);

    Boolean updateCalibrationBaseId(Long id, String assesType, String assesYear, Set<String> staffIds);

    List<HrsfPmsuserBase> getCalibrationListById(CalibrationListQueryDTO queryDTO, Boolean isVerify);

    Map<String, Object> getCalibrationPmsById(CalibrationListQueryDTO queryDTO, Boolean isVerify);

    HrsfOrganizationVO getCalibrationPmsSelect(Long id);

    List<HrsfUserVO> getCalibrationPmsSelectName(Long id, String fullName);

    ScoreCollectVO getCalibrationScoreCollect(CalibrationListQueryDTO queryDTO, Boolean isVerify);

    ErrorMessageVo syncScoreBySF(List<Long> userBaseIdList, HrsfCalibrationBase calibrationBase);

    R<ErrorMessageVo> returnCalibration(List<Long> userBaseIdList);

    void export(Long calibrationId, String assessType, String calibrationName, HttpServletResponse response);

    UploadMessageVo upload(Long calibrationId, String assessType, MultipartFile file);

    void syncPmsJobHandler();

    void syncPmsJobHandlerNoPushToSF();

    boolean writeDataBack(List<WriteDataBackDTO> writeDataBackDTOS);

    boolean finalWriteDataBack(List<WriteDataBackDTO> writeDataBackDTOS);

    List<HrsfPmsuserBase> selectByBaseId(Long baseId);

    Boolean updateHrsfPmsUserInfo(Map<String, List<HrsfPmsuserBase>> dataMap, Long calibrationBaseId);

    boolean whiteYearWriteDataBack(List<WriteDataBackDTO> writeDataBackDTOS);

    boolean whiteYearFinalWriteDataBack(List<WriteDataBackDTO> writeDataBackDTOS);

    List<HrsfPmsuserBase> selectByStaffIdAndASSESS(List<String> staffIds, String assesYear, String assesType);

    Boolean saveCompanyLevelCalibration(List<HrsfPmsuserBase> pmsUserList, ArrayList<String> failNameList);

    Object removeCalibrationBaseId(Long id);

    Object batchUpdateByCalibrationBaseId(Long baseId);

    Object removeCalibrationUser(AddCalibrationUserDTO addCalibrationUserDTO);

    HrsfOrganizationVO getCalibrationPmsSelectByYearAndType(String assesYear, String assesType);

    List<HrsfUserVO> getCalibrationPmsSelectNameByYearAndType(String assesYear, String assesType, String fullName);

    IPage<HrsfPmsuserBase> getPmsUserListByOverView(AddCalibrationUserDTO addCalibrationUserDTO);

    IPage<HrsfPmsuserBase> getOverviewPage(AddCalibrationUserDTO addCalibrationUserDTO);

    List<HrsfPmsuserBase> selectPmsUserBaseByStaffIdList(List<String> staffIds,String assesYear, String assesType);

    Map<String, Object> getCalibrationPotencyById(CalibrationListQueryDTO queryDTO, Boolean isVerify);

    List<HrsfPmsuserBase> selectByCalibrationUserDTO(AddCalibrationUserDTO addCalibrationUserDTO);

    Boolean compensator(String assessYear, String assessType,Long calibrationBaseId);

    Object removeFormId(HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO);

    void updateTotalScoreOriginal(Long baseId);

    Boolean syncUserBaseInfo(long current, long size);

    void updatePmsLevelById(List<Long> cachedDataListTwo);
}
