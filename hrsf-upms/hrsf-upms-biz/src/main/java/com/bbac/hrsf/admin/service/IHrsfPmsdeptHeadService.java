package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.entity.HrsfPmsdeptHead;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
public interface IHrsfPmsdeptHeadService extends IService<HrsfPmsdeptHead> {

    /**
     * 每天同步先清空机构负责人表
     */
    void delTable();

    boolean syncPrincipalInfo(List<HrsfPmsdeptHead> principalList);

    Set<String> selectDeptHeadList(String organId);
}
