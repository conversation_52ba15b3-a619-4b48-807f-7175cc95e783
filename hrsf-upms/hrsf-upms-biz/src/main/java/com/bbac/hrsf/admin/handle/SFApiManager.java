package com.bbac.hrsf.admin.handle;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * SF API 工具
 */
@Slf4j
@Component
public class SFApiManager {
    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;
    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;
    @Value("${sf.sync.usernameSDAPI:SDAPI@beijingbenT1}")
    private String sfUserName;
    @Value("${sf.sync.pwdSDAPI:q~gOkDiCE!BU}")
    private String sfPwd;
    @Value("${sf.sync.formcontentId}")
    private String sfUrl4FormcontentId;
    @Value("${sf.sync.sfUrl4formCustomSection}")
    private String sfUrl4formCustomSection;
    @Value("${sf.sync.upsert}")
    private String sfUrl4Upsert;
    /** 获取SF小分API */
    @Value("${sf.sync.sfCompetencyScoreApi}")
    private String sfCompetencyScoreApi;

    /** 业绩得分 itemId：评分 Rating（%） */
    @Value("${sf.sync.itemId}")
    private String sfYearScoreItemId;
    @Value("${sf.sync.suffixName}")
    private String suffixName;
    @Value("${sf.sync.TodoEntryV2}")
    private String sfSyncTodoEntryV2;
    @Value("${sf.sync.sendToNextStepKey}")
    private String sfSyncSendToNextStepKey;
    @Value("${sf.sync.fetchAssessmentStaff}")
    private String sfUrl4fetchAssessmentStaff;
    @Value("${sf.sync.upsert}")
    private String sfSyncUpsert;
    /** 上传附件 body模板 */
    public static final String ATTACHMENT_BODY = "{\"__metadata\": {\"uri\": \"cust_performanceattchment\"},\"externalCode\": \"%s\",\"effectiveStartDate\": \"/Date(%s)/\", \"cust_attachmentNav\": {\"__metadata\": {\"uri\": \"Attachment(attachmentId=%s)\"}}}";
    /** 写入 年度小分 body模板 */
    public static final String UPSERT_YEAR_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormCompetencySection(formContentId={{formContentId}}L,formDataId={{formId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetencySection\"},\"competencies\":[{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{workQualityScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{workQualityScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{workQualityScoreItemId}}_r\",\"rating\":\"{{workQualityScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{costControlScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{costControlScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{costControlScoreItemId}}_r\",\"rating\":\"{{costControlScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{completionOfWorkScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{completionOfWorkScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{completionOfWorkScoreItemId}}_r\",\"rating\":\"{{completionOfWorkScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{productionSafetyScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{productionSafetyScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{productionSafetyScoreItemId}}_r\",\"rating\":\"{{productionSafetyScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{attendanceScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{attendanceScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{attendanceScoreItemId}}_r\",\"rating\":\"{{attendanceScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{communicationScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{communicationScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{communicationScoreItemId}}_r\",\"rating\":\"{{communicationScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{teamWorkScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{teamWorkScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{teamWorkScoreItemId}}_r\",\"rating\":\"{{teamWorkScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{professionalScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{professionalScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{professionalScoreItemId}}_r\",\"rating\":\"{{professionalScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{responsibilityScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{responsibilityScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{responsibilityScoreItemId}}_r\",\"rating\":\"{{responsibilityScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{disciplineScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{disciplineScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{disciplineScoreItemId}}_r\",\"rating\":\"{{disciplineScore}}\"}}]}";
    /** 写入 季度小分 body模板 */
    public static final String UPSERT_QUARTER_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormCompetencySection(formContentId={{formContentId}}L,formDataId={{formId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetencySection\"},\"competencies\":[{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{productionSafetyScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{productionSafetyScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{productionSafetyScoreItemId}}_r\",\"rating\":\"{{productionSafetyScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{workQualityScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{workQualityScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{workQualityScoreItemId}}_r\",\"rating\":\"{{workQualityScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{completionOfWorkScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{completionOfWorkScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{completionOfWorkScoreItemId}}_r\",\"rating\":\"{{completionOfWorkScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{attendanceScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{attendanceScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{attendanceScoreItemId}}_r\",\"rating\":\"{{attendanceScore}}\"}},{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{costControlScoreItemId}}L,sectionIndex=2)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{costControlScoreItemId}}L,ratingType='na',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c{{costControlScoreItemId}}_r\",\"rating\":\"{{costControlScore}}\"}}]}";
    /** 写入 总分 body模板 */
    public static final String UPSERT_FINAL_SCORE_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=1438L,ratingType='official',sectionIndex=2)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_2__c1438_r\",\"rating\":\"{{finalResultScore}}\"}";
    /** 写入 业绩等级 body模板 */
    public static final String UPSERT_PERFORMANCE_LEVEL_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,sectionIndex=4,itemId=0L,ratingType='overall')\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_4_rating\",\"rating\":\"{{performanceCategory}}\"}";
    /** 写入 轻微违规违纪扣减说明 body模板 */
    public static final String UPSERT_DEDUCTED_REMARK_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormCustomElement(elementKey='ele_0',formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=-1L,sectionIndex=3)\",\"type\":\"SFOData.FormCustomElement\"},\"valueKey\":\"wf_sect_3_e_ele_00\",\"value\":\"{{regulationDeductedRemark}}\"}";
    /** 写入 主要成就 body模板 */
    public static final String UPSERT_MAJOR_ACCOMPLISHMENTS_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormCustomElement(elementKey='ele_0',formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=-1L,sectionIndex=2)\",\"type\":\"SFOData.FormCustomElement\"},\"valueKey\":\"wf_sect_2_e_ele_00\",\"value\":\"{{annualMajorAccomplishments}}\"}";
    /** 写入 潜能等级和可变动性 body模板 */
    public static final String UPSERT_POTENTIAL_CATEGORY_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormCustomSection(formContentId={{formContentId}}L,formDataId={{formId}}L,sectionIndex=5)\",\"type\":\"SFOData.FormCustomSection\"},\"customElement\":[{\"__metadata\":{\"uri\":\"FormCustomElement(elementKey='ele_1',formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=-1L,sectionIndex=5)\",\"type\":\"SFOData.FormCustomElement\"},\"valueKey\":\"wf_sect_5_e_ele_10\",\"value\":\"{{potentialCategory}}\"},{\"__metadata\":{\"uri\":\"FormCustomElement(elementKey='ele_2',formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=-1L,sectionIndex=5)\",\"type\":\"SFOData.FormCustomElement\"},\"valueKey\":\"wf_sect_5_e_ele_21\",\"value\":\"{{availability}}\"},{\"__metadata\":{\"uri\":\"FormCustomElement(elementKey='ele_3',formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=-1L,sectionIndex=5)\",\"type\":\"SFOData.FormCustomElement\"},\"valueKey\":\"wf_sect_5_e_ele_32\",\"value\":\"{{availableFromYear}}\"},{\"__metadata\":{\"uri\":\"FormCustomElement(elementKey='ele_4',formContentId={{formContentId}}L,formDataId={{formId}}L,itemId=-1L,sectionIndex=5)\",\"type\":\"SFOData.FormCustomElement\"},\"valueKey\":\"wf_sect_5_e_ele_43\",\"value\":\"{{availableFromMonth}}\"}]}";
    /** 业绩得分 body模板 */
    public static final String UPSERT_RATING_SCORE_BODY_TEMPLATE = "{\"__metadata\":{\"uri\":\"FormCompetencySection(formContentId={{formContentId}}L,formDataId={{formId}}L,sectionIndex=3)\",\"type\":\"SFOData.FormCompetencySection\"},\"competencies\":[{\"__metadata\":{\"uri\":\"FormCompetency(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{ratingScoreItemId}}L,sectionIndex=3)\",\"type\":\"SFOData.FormCompetency\"},\"officialRating\":{\"__metadata\":{\"uri\":\"FormUserRatingComment(formContentId={{formContentId}}L,formDataId={{formId}}L,itemId={{ratingScoreItemId}}L,ratingType='na',sectionIndex=3)\",\"type\":\"SFOData.FormUserRatingComment\"},\"ratingKey\":\"wf_sect_3__c{{ratingScoreItemId}}_r\",\"rating\":\"{{ratingScore}}\"}}]}";

    /**
     * 获取FormContentId
     * @param staffId
     * @param formId
     * @return
     */
    public String getFormContentId(String staffId, String formId) {
        String formContentId = null;
        String url = null;
        String body = null;
        try{
            url = StrUtil.format(sfUrl4FormcontentId, formId);
            body = new HttpRequestCustom(url).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            formContentId = jsonObject.getJSONObject("d").getStr("formContentId");
            if(StringUtils.isEmpty(formContentId)){
                log.warn("SF formContentId is empty, staffId:{}, url:{}, rspBody:{}", staffId, url, body);
                throw new ServiceException(400, "调用SF接口获取formContentId失败 | Get SF formContentId Failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF formContentId timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF formContentId get failed, staffId:{}, url:{}, body:{}", staffId, url, body, e);
            // throw new ServiceException(400, "调用SF接口获取'formContentId'失败 | Get SF formContentId Failed");
        }
        return formContentId;
    }

    /**
     * 获取轻微违规违纪扣减说明
     */
    public String getRegulationDeductedRemark(String staffId, String formDataId, String formContentId){
        String customSectionUrl = null;
        String customSectionBody = null;
        String regulationDeductedRemark = null;
        try {
            customSectionUrl = StrUtil.format(sfUrl4formCustomSection, formContentId, formDataId, 3);
            customSectionBody = new HttpRequestCustom(customSectionUrl).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject customSectionJson = JSONUtil.parseObj(customSectionBody);
            if(customSectionJson.get("error") != null){
                log.warn("SF regulationDeductedRemark error, staffId:{}, customSectionUrl:{}, customSectionBody:{}", staffId, customSectionUrl, customSectionBody);
            }else{
                /* 该方式valueKey可能为空
                Map<String, String> potentialMap = customSectionJson.getJSONObject("d").getJSONArray("results").stream()
                        .collect(Collectors.toMap(data -> ((JSONObject) data).getStr("valueKey"), data -> ((JSONObject) data).getStr("value")));
                regulationDeductedRemark = potentialMap.get("wf_sect_3_e_ele_00");*/
                Optional<Object> optional = customSectionJson.getJSONObject("d").getJSONArray("results").stream().findFirst();
                JSONObject userJson = (JSONObject) optional.get();
                regulationDeductedRemark = userJson.getStr("value");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF regulationDeductedRemark timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF regulationDeductedRemark get SF regulationDeductedRemark failed, staffId:{}, customSectionUrl:{}, customSectionBody:{}", staffId, customSectionUrl, customSectionBody, e);
            throw new ServiceException(400, "调用SF接口获取'轻微违规违纪扣减说明'失败 | Get SF regulationDeductedRemark Failed");
        }
        return regulationDeductedRemark;
    }

    /**
     * 获取(白领)主要成就
     * @param staffId
     * @param formDataId
     * @param formContentId
     * @return
     */
    public String getMajorAccomplishments(String staffId, String formDataId, String formContentId){
        String annualMajorAccomplishments = null;
        String customSectionUrl = null;
        String customSectionBody = null;
        try {
            customSectionUrl = StrUtil.format(sfUrl4formCustomSection, formContentId, formDataId, 2);
            customSectionBody = new HttpRequestCustom(customSectionUrl).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject customSectionJson = JSONUtil.parseObj(customSectionBody);
            if(customSectionJson.get("error") != null){
                log.warn("SF annualMajorAccomplishments error, staffId:{}, customSectionUrl:{}, customSectionBody:{}", staffId, customSectionUrl, customSectionBody);
            }else{
                Optional<Object> optional = customSectionJson.getJSONObject("d").getJSONArray("results").stream().findFirst();
                JSONObject userJson = (JSONObject) optional.get();
                annualMajorAccomplishments = userJson.getStr("value");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF annualMajorAccomplishments timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF annualMajorAccomplishments get failed, staffId:{}, customSectionUrl:{}, customSectionBody:{}", staffId, customSectionUrl, customSectionBody, e);
            throw new ServiceException(400, "调用SF接口获取'年度评价-主要成就'失败 | Get SF annualMajorAccomplishments Failed");
        }
        return annualMajorAccomplishments;
    }
    /**
     * 获取SF小分
     * @param contentId from SF
     * @param formId from SF
     * @param itemId from SF
     * @return score
     */
    public BigDecimal getCompetenciesScore(String contentId, String formId, String itemId){
        JSONObject competencyResp = null;
        try{
            String sfCompetenciesApiUrl = StrUtil.format(sfCompetencyScoreApi, contentId, formId, itemId, 2);
            String competencyRespStr = new HttpRequestCustom(sfCompetenciesApiUrl).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            competencyResp = JSONUtil.parseObj(competencyRespStr);
            JSONObject respData = competencyResp.getJSONObject("d");
            log.debug("===debug getCompetenciesScore, formId:{}, contentId:{}, itemId:{}, respData:{}", formId, contentId, itemId, respData);
            if(respData == null){
                log.info("getCompetenciesScore no data, contentId:{}, formId:{}, itemId:{}", contentId, formId, itemId);
                return null;
            }
            String scoreStr = respData.getStr("textRating");
            return new BigDecimal(scoreStr);
        } catch (NumberFormatException e) {
            log.info("getCompetenciesScore invalid score, contentId:{}, formId:{}, itemId:{}", contentId, formId, itemId);
        } catch (HttpException | IORuntimeException e) {
            log.info("getCompetenciesScore timeout, contentId:{}, formId:{}, itemId:{}", contentId, formId, itemId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e) {
            log.warn("getCompetenciesScore error, contentId:{}, formId:{}, itemId:{}, resp：{}", contentId, formId, itemId, competencyResp, e);
            throw new ServiceException(400, "调用SF接口获取'小分'失败 | Get SF CompetenciesScore Failed");
        }
        return null;
    }

    /**
     * 获取业绩得分
     * @param contentId
     * @param formId
     * @return
     */
    public BigDecimal getRatingScore(String contentId, String formId){
        JSONObject competencyResp = null;
        String sfCompetenciesApiUrl = null;
        try{
            sfCompetenciesApiUrl = StrUtil.format(sfCompetencyScoreApi, contentId, formId, sfYearScoreItemId, 3);
            String competencyRespStr = new HttpRequestCustom(sfCompetenciesApiUrl).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable)
                    .setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            competencyResp = JSONUtil.parseObj(competencyRespStr);
            JSONObject respData = competencyResp.getJSONObject("d");
            log.debug("===debug getRatingScore, formId:{}, contentId:{}, respData:{}", formId, contentId, respData);
            if(respData == null){
                log.warn("getRatingScore no data, contentId:{}, formId:{}", contentId, formId);
                return null;
            }
            String scoreStr = respData.getStr("rating");
            return new BigDecimal(scoreStr);
        } catch (NumberFormatException e) {
            log.info("getRatingScore invalid score, contentId:{}, itemId:{}", contentId, formId);
        } catch (HttpException | IORuntimeException e) {
            log.info("getRatingScore timeout, contentId:{}, itemId:{}", contentId, formId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e) {
            log.warn("getRatingScore error, contentId:{}, formId:{}, resp：{}", contentId, formId, competencyResp, e);
            throw new ServiceException(400, "调用SF接口获取'获取业绩得分异常'失败 | Get SF RatingScore Failed");
        }
        return null;
    }

    /**
     * 获取潜能等级和可变动性
     * @param staffId
     * @param formDataId
     * @param formContentId
     * @return Map<String, String> potentialMap
     */
    public Map<String, String> getPotentialMap(String staffId, String formDataId, String formContentId){
        String url = null;
        String body = null;
        Map<String, String> potentialMap = null;
        try {
            url = StrUtil.format(sfUrl4formCustomSection, formContentId, formDataId, 5);
            body = new HttpRequestCustom(url).setHttpProxyShortTime(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject dataObject = JSONUtil.parseObj(body).getJSONObject("d");
            if(dataObject != null){
                potentialMap = dataObject.getJSONArray("results").stream()
                        .collect(Collectors.toMap(data -> ((JSONObject) data).getStr("elementKey"), data -> ((JSONObject) data).getStr("value")));
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF getPotentialMap timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF getPotentialMap failed, staffId:{}, customSectionUrl:{}, customSectionBody:{}",
                    staffId, url, body, e);
            throw new ServiceException(400, "调用SF接口获取'潜能等级和可变动性'失败 | Get SF potentialCategory&availability Failed");
        }
        if(potentialMap == null){
            log.warn("get getPotentialMap is empty, staffId:{}, url:{}, body:\n{}",
                    staffId, url, body);
            throw new ServiceException(400, "调用SF接口获取'潜能等级和可变动性'为空 | Get SF potentialCategory&availability is empty");
        }
        return potentialMap;
    }

    /**
     * 上传附件
     * @param staffId staffId
     * @param paramMap paramMap
     * @return attachmentId
     */
    public String uploadAttachment(String staffId, Map<String, Object> paramMap){
        try {
            String body = new HttpRequestCustom(sfUrl4Upsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paramMap)).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            JSONObject returnJson = (JSONObject) jsonObject.getJSONArray("d").get(0);
            String status = returnJson.getStr("status");
            if (!"OK".equals(status)) {
                log.warn("SF uploadExcelToSF failed, staffId:{}, url:{}, rspBody:{}", staffId, sfUrl4Upsert, body);
                throw new ServiceException(400, "调用SF接口上传Excel失败 | Upload Excel File SF Failed");
            }
            String key = returnJson.getStr("key");
            return key.split("=")[1];
        } catch (HttpException | IORuntimeException e) {
            log.info("SF uploadAttachment timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF uploadAttachment failed, staffId:{}", staffId, e);
            throw new ServiceException(400, "调用SF接口上传附件失败 | SF UploadAttachment Failed");
        }
    }

    /**
     * 将附件与员工号关联
     * @return
     */
    public void associateAttachment(String staffId, String attachmentId){
        try{
            // 步骤2：将附件上传并与员工号关联
            long epochMilliToday = LocalDate.now().toEpochDay() * 1000 * 60 * 60 * 24;
            String reqBody = String.format(ATTACHMENT_BODY, staffId, epochMilliToday, attachmentId);
            String body = new HttpRequestCustom(sfUrl4Upsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(reqBody).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body);
            JSONObject returnJson = (JSONObject) jsonObject.getJSONArray("d").get(0);
            String status = returnJson.getStr("status");
            if (!"OK".equals(status)) {
                log.warn("SF associateAttachment failed, staffId:{}, url:{}, rspBody:{}", staffId, sfUrl4Upsert, body);
                throw new ServiceException(400, "调用SF接口关联员工号失败 | Associated Excel File Failed");
            }
            log.info("uploadExcelToSF staff score report uploaded. staffId:{}", staffId);
        } catch (HttpException | IORuntimeException e) {
            log.info("SF associateAttachment timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF associateAttachment failed, staffId:{}", staffId, e);
            throw new ServiceException(400, "调用SF将附件与员工号关联失败 | SF AssociateAttachment Failed");
        }
    }

    /**
     * 获取主管评估阶段员工
     * @param current
     * @param size
     * @return
     */
    public JSONArray fetchAssessmentStaff(int current, int size){
        try{
            String sfUrl = StrUtil.format(sfUrl4fetchAssessmentStaff, current, size);
            String body;
            try {
                body = new HttpRequestCustom(sfUrl).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(sfUserName, sfPwd).execute().body();
            } catch (HttpException | IORuntimeException e) {
                log.warn("获取获取当前评估员工接口超时,自动重新执行一次");
                body = new HttpRequestCustom(sfUrl).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(sfUserName, sfPwd).execute().body();
            }
            JSONObject jsonObject = JSONUtil.parseObj(body);
            JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
            return jsonArray;
        } catch (HttpException | IORuntimeException e) {
            log.info("SF fetchAssessmentStaff timeout");
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF fetchAssessmentStaff failed", e);
            throw new ServiceException(400, "调用SF获取主管评估阶段员工失败 | SF FetchAssessmentStaff Failed");
        }
    }

    /**
     * 获取经理
     * @param staffId
     * @param formDataId
     * @return directManagerId
     */
    public String getDirectManagerId(String staffId, String formDataId){
        try{
            String url = StrUtil.format(sfSyncTodoEntryV2, formDataId);
            String body = new HttpRequestCustom(url).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(sfUserName, sfPwd).execute().body();
            JSONObject jsonObject = JSONUtil.parseObj(body).getJSONObject("d");
            if (jsonObject == null || jsonObject.getJSONArray("results").isEmpty()) {
                log.warn("SF directManagerId is empty, staffId:{}, url:{}, rspBody:{}", staffId, url, body);
                throw new ServiceException(400, "未获取到直接负责人 | directManagerId is empty, formDataId:"+formDataId);
            }
            Optional<Object> optional = jsonObject.getJSONArray("results").stream().
                    filter(obj -> (StrUtil.isNotBlank(((JSONObject) obj).getStr("userId")))).findFirst();
            JSONObject userJson = (JSONObject) optional.get();
            String directManagerId = userJson.getStr("userId");
            directManagerId = directManagerId + suffixName;
            return directManagerId;
        } catch (HttpException | IORuntimeException e) {
            log.info("SF getDirectManagerId timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (RuntimeException e){
            log.warn("SF getDirectManagerId failed, staffId:{}", staffId, e);
            throw new ServiceException(400, "调用SF获取经理失败 | SF GetDirectManagerId Failed");
        }
    }

    /**
     * 写入小分
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param reqBody
     */
    public void upsertItemScore(String staffId, String previousFormDataId,  String directManagerId, String reqBody){
        String body = null;
        try {
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST)
                    .body(JSONUtil.toJsonStr(reqBody)).basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert ItemScore, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert ItemScore failed, staffId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入小分失败 | Upsert Item Score failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertItemScore timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("SF upsertItemScore error, staffId:{}, directManagerId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, directManagerId, sfSyncUpsert, body, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入小分异常 | Upsert Item Score error");
        }
    }

    /**
     * 写入小分
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param reqBody
     */
    public void upsertRatingScore(String staffId, String previousFormDataId,  String directManagerId, String reqBody){
        String body = null;
        try {
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST)
                    .body(JSONUtil.toJsonStr(reqBody)).basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert RatingScore, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert RatingScore failed, staffId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入小分失败 | Upsert Item Score failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertRatingScore timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("Upsert RatingScore error, staffId:{}, directManagerId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, directManagerId, sfSyncUpsert, body, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入小分异常 | Upsert Item Score error");
        }
    }

    /**
     * 写入总分
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param templateVo
     */
    public void upsertFinalScore(String staffId, String previousFormDataId,  String directManagerId, Object templateVo){
        String body = null;
        String reqBody = null;
        try {
            reqBody = processTemplate(UPSERT_FINAL_SCORE_BODY_TEMPLATE, templateVo);
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST)
                    .body(JSONUtil.toJsonStr(reqBody)).basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert finalResultScore, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert finalResultScore failed, staffId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入最终总分失败 | Upsert Final Result failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertFinalScore timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("Upsert finalResultScore error, staffId:{}, directManagerId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, directManagerId, sfSyncUpsert, body, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入最终总分异常 | Upsert Final Result error");
        }
    }

    /**
     * 写入绩效等级
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param templateVo
     */
    public void upsertPerformanceLevel(String staffId, String previousFormDataId,  String directManagerId, Object templateVo){
        String body = null;
        String reqBody = null;
        try {
            reqBody = processTemplate(UPSERT_PERFORMANCE_LEVEL_BODY_TEMPLATE, templateVo);
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST)
                    .body(JSONUtil.toJsonStr(reqBody)).basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert performanceCategory, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert performanceCategory failed, staffId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入业绩等级失败 | Upsert Performance Category failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertPerformanceLevel timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("Upsert upsertPerformanceLevel error, staffId:{}, url:{}, body:\n{}, templateVo:{}, reqBody:\n{}",
                    staffId, sfSyncUpsert, body, templateVo, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入业绩等级异常 | Upsert Performance Category error");
        }
    }

    /**
     * 写入轻微违规违纪扣减说明
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param templateVo
     */
    public void upsertDeductedRemark(String staffId, String previousFormDataId, String directManagerId, Object templateVo){
        String body = null;
        String reqBody = null;
        try {
            reqBody = processTemplate(UPSERT_DEDUCTED_REMARK_BODY_TEMPLATE, templateVo, true);
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(reqBody))
                    .basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert regulationDeductedRemark, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert regulationDeductedRemark failed, staffId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入轻微违规违纪扣减说明失败 | Upsert Regulation Deducted Remark failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertDeductedRemark timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("Upsert upsertDeductedRemark error, staffId:{}, url:{}, body:\n{}, templateVo:{}, reqBody:\n{}",
                    staffId, sfSyncUpsert, body, templateVo, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入轻微违规违纪扣减说明异常 | Upsert Regulation Deducted Remark error");
        }
    }

    /**
     * 写入主要成就
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param templateVo
     */
    public void upsertMajorAccomplishments(String staffId, String previousFormDataId, String directManagerId, Object templateVo){
        String body = null;
        String reqBody = null;
        try {
            reqBody = processTemplate(UPSERT_MAJOR_ACCOMPLISHMENTS_BODY_TEMPLATE, templateVo, true);
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(reqBody)).basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert annualMajorAccomplishments, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert annualMajorAccomplishments failed, staffId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入主要成就失败 | Upsert Annual-Major Accomplishments failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertMajorAccomplishments timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("Upsert upsertMajorAccomplishments error, staffId:{}, url:{}, body:\n{}, templateVo:{}, reqBody:\n{}",
                    staffId, sfSyncUpsert, body, templateVo, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入主要成就异常 | Upsert Annual-Major Accomplishments error");
        }
    }

    /**
     * 写入潜能等级和可变动性
     * @param staffId
     * @param previousFormDataId
     * @param directManagerId
     * @param templateVo
     */
    public void upsertPotentialLevel(String staffId, String previousFormDataId, String directManagerId, Object templateVo){
        String body = null;
        String reqBody = null;
        try {
            reqBody = processTemplate(UPSERT_POTENTIAL_CATEGORY_BODY_TEMPLATE, templateVo, true);
            body = new HttpRequestCustom(sfSyncUpsert).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(reqBody)).basicAuth(directManagerId, sfSyncSendToNextStepKey).execute().body();
            JSONObject jsonObject = (JSONObject)JSONUtil.parseObj(body).getJSONArray("d").get(0);
            String status = jsonObject.getStr("status");
            log.debug("===debug Upsert upsertPotentialLevel, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                    staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
            if (!"OK".equals(status)) {
                log.warn("Upsert upsertPotentialLevel failed, staffId:{}, previousFormDataId:{}, url:{}, body:\n{}, reqBody:\n{}",
                        staffId, previousFormDataId, sfSyncUpsert, body, reqBody);
                throw new ServiceException(400, "调用SF接口写入潜能等级和可变动性 | Upsert potentialCategory&availability failed");
            }
        } catch (HttpException | IORuntimeException e) {
            log.info("SF upsertPotentialLevel timeout, staffId:{}", staffId);
            throw new ServiceException(408, e.getMessage());
        } catch (Exception e) {
            log.warn("Upsert upsertPotentialLevel error, staffId:{}, url:{}, body:\n{}, templateVo:{}, reqBody:\n{}",
                    staffId, sfSyncUpsert, body, templateVo, reqBody, e);
            throw new ServiceException(400, "调用SF接口写入潜能等级和可变动性 | Upsert potentialCategory&availability error");
        }
    }

    /**
     * 处理模板字符串
     * @param template 模板
     * @param params 参数
     * @return 格式化后字符串
     */
    public String processTemplate(String template, Map<String, Object> params){
        Matcher m = Pattern.compile("\\{\\{\\w+\\}\\}").matcher(template);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            String param = m.group();
            Object value = params.get(param.substring(2, param.length() - 2));
            if(value != null){
                m.appendReplacement(sb, value.toString());
            }
        }
        m.appendTail(sb);
        return sb.toString();
    }

    /**
     * 处理模板字符串
     * @param template 模板
     * @param obj 参数对象
     * @return 格式化后字符串
     */
    public String processTemplate(String template, Object obj){
        return processTemplate(template, obj, false);
    }

    /**
     * 处理模板字符串
     * @param template 模板
     * @param obj 参数对象
     * @param force 是否强制替换变量为空字符
     * @return 格式化后字符串
     */
    public String processTemplate(String template, Object obj, boolean force){
        Class<?> clazz = obj.getClass();
        Matcher m = Pattern.compile("\\{\\{\\w+\\}\\}").matcher(template);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            String fieldName = null;
            Object value = null;
            try {
                String param = m.group();
                fieldName = param.substring(2, param.length() - 2);
                Field nameField = clazz.getDeclaredField(fieldName);
                nameField.setAccessible(true);
                value = nameField.get(obj);
                if(value != null){
                    m.appendReplacement(sb, value.toString());
                } else if (force) {
                    m.appendReplacement(sb, "");
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            } catch (NoSuchFieldException e) {
                log.debug("processTemplate NoSuchField:{}", fieldName);
            }
        }
        m.appendTail(sb);
        return sb.toString();
    }

    /**
     * 转义html字符串
     * @param htmlStr html字符串
     * @return 转义后字符串
     */
    public String unescapeHtml(String htmlStr){
        if(StringUtils.isEmpty(htmlStr)){
            return htmlStr;
        }
        String escaped = htmlStr.replaceAll("<br[^>]*>", "<br>\n");
        escaped = escaped.replaceAll("</p[^>]*>", "</p>\n");
        escaped = escaped.replaceAll("<[^>]+>", "");
        return HtmlUtil.unescape(escaped);
    }
}
