/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.event;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpException;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserPunishDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserRelationInfo;
import com.bbac.hrsf.admin.api.entity.HrsfPmsWriteDataBackLog;
import com.bbac.hrsf.admin.api.entity.HrsfPmsdeptHead;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.service.IHrsfPmsUserRelationInfoService;
import com.bbac.hrsf.admin.service.IHrsfPmsWriteDataBackLogService;
import com.bbac.hrsf.admin.service.IHrsfPmsdeptHeadService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.enums.AssessTypeEnum;
import com.bbac.hrsf.common.core.constant.enums.EmailStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.ErrorFlag;
import com.bbac.hrsf.common.core.constant.enums.LevelFlagEnum;
import com.bbac.hrsf.common.core.util.DatetimeUtils;
import com.bbac.hrsf.performance.api.dto.EmailInfoDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.api.feign.RemoteEmailService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> 异步监听日志事件
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PmsUpdateListener {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 500;


    @Value("${sf.sync.username}")
    private String userName;
    @Value("${sf.sync.pwd}")
    private String pwd;
    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;
    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;

    @Value("${sf.sync.overallFormRating}")
    private String sfSyncOverallFormRating;

    @Value("${sf.sync.overallAdjustedRating}")
    private String overallAdjustedRating;

    @Value("${sf.sync.deepLink}")
    private String deepLink;

    @Value("${sf.sync.upsertApi}")
    private String upsertApi;

    @Value("${sf.sync.operation.email}")
    private String operationEmail;

    private final IHrsfPmsuserBaseService hrsfPmsuserBaseService;

    private final IHrsfPmsUserRelationInfoService hrsfPmsUserRelationInfoService;

    private final IHrsfPmsWriteDataBackLogService writeDataBackLogService;

    private final IHrsfPmsdeptHeadService hrsfPmsdeptHeadService;

    private final IHrsfUserBaseService hrsfUserBaseService;

    private final RemoteEmailService remoteEmailService;

    private static final String UPDATE_PMS_FORMATTER = "User(userId='{}')";

    private final long size = 1000;

    private final String CONTENT_TRANS = "最终总分Final Result: {}%; 业绩等级Performance Category: {}";
    private final String USER_ID = "User(userId='{}')";

    @Async("batchTaskExecutor")
    @Order
    @TransactionalEventListener(PmsUpdateEvent.class)
    public void handlePmsUpdate(PmsUpdateEvent event) {
        switch (event.getEventEnum()) {
            case A:
                handlePmsUpdateA(event);
                break;
            case B:
                handlePmsUpdateB(event);
                break;
            case C:
                handlePmsUpdateC(event);
                break;
            case D:
                handlePmsUpdateD(event);
                break;
            case I:
                handlePmsUpdateI(event);
                break;
            case L:
                handlePmsUpdateL(event);
                break;
            case M:
                handlePmsUpdateM(event);
            default:
                break;
        }

    }

    /**
     * 处理机构负责人-未在主数据中则置空
     *
     * @param event
     */
    private void handlePmsUpdateL(PmsUpdateEvent event) {
        List<String> staffIdList = hrsfPmsdeptHeadService.list(Wrappers.<HrsfPmsdeptHead>query().select("DISTINCT STAFF_ID").lambda()
                .isNotNull(HrsfPmsdeptHead::getStaffId)).stream().map(HrsfPmsdeptHead::getStaffId).collect(Collectors.toList());
        List<String> userBaseStaffIdList = hrsfUserBaseService.list(Wrappers.<HrsfUserBase>lambdaQuery().select(HrsfUserBase::getStaffId)
                .isNotNull(HrsfUserBase::getStaffId)).stream().map(HrsfUserBase::getStaffId).collect(Collectors.toList());
        /**
         * 比较两个list
         * 取出未在主数据的机构负责人ID
         * 将主数据不存在的机构负责人置空
         */
        // 差集 (list1 - list2)
        List<String> reduce1 = staffIdList.stream().filter(item -> !userBaseStaffIdList.contains(item)).collect(toList());
        log.info("获取到机构负责人两者的差集数量{},详情:{}", reduce1.size(), reduce1);
        if (CollUtil.isNotEmpty(reduce1)) {
            hrsfPmsdeptHeadService.lambdaUpdate().set(HrsfPmsdeptHead::getStaffId, "")
                    .set(HrsfPmsdeptHead::getUpdateTime, LocalDateTime.now())
                    .set(HrsfPmsdeptHead::getUpdateBy, "inner")
                    .in(HrsfPmsdeptHead::getStaffId, reduce1).update();
        }
    }

    /**
     * 同步同步绩效协调员到SF系统
     *
     * @param event
     */
    private void handlePmsUpdateI(PmsUpdateEvent event) {
        Set<HrsfPmsUserRelationInfo> relationInfoSet = hrsfPmsUserRelationInfoService.list().stream().collect(Collectors.toSet());
        /**
         * 根据员工号分组
         */
        Map<String, List<String>> staffIdMap = relationInfoSet.stream().collect(Collectors.groupingBy(HrsfPmsUserRelationInfo::getStaffId,
                Collectors.mapping(HrsfPmsUserRelationInfo::getPmsStaffId, Collectors.toList())));
        String format = StrUtil.format(event.getUrl());
        log.info("同步绩效协调员到SF系统URL:{},开始时间:{}", format, LocalDateTime.now());
        /**
         * 构造如下格式数据
         *
         * {
         *     "__metadata": {
         *         "uri": "User(userId='10000218')"
         *     },
         *     "customManager": [
         *         {
         *             "__metadata": {
         *                 "uri": "User('GPMuser16')"
         *             }
         *         },
         *         {
         *             "__metadata": {
         *                 "uri": "User('GPMuser17')"
         *             }
         *         }
         *     ]
         * }
         */
        List<Map> cachedDataList = new ArrayList<>();
        List<Map> finalCachedDataList = cachedDataList;
        staffIdMap.entrySet().stream().forEach(data -> {
            Map<String, Object> paramMap = new ConcurrentHashMap<>();
            Map<String, Object> subParamMap = new ConcurrentHashMap<>();
            paramMap.put("__metadata", subParamMap);
            subParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORMATTER, data.getKey()));
            ArrayList<Object> subTParamMapList = new ArrayList<>();
            data.getValue().stream().forEach(value -> {
                Map<String, Object> subTTParamMap = new ConcurrentHashMap<>(16);
                Map<String, Object> subTParamMap = new ConcurrentHashMap<>(16);
                subTTParamMap.put("uri", StrUtil.format(UPDATE_PMS_FORMATTER, value));
                subTParamMap.put("__metadata", subTTParamMap);
                subTParamMapList.add(subTParamMap);
            });
            paramMap.put("customManager", subTParamMapList);
            finalCachedDataList.add(paramMap);
        });
        List<List<Map>> split = ListUtil.split(finalCachedDataList, BATCH_COUNT);
        for (int i = 0; i < split.size(); i++) {
            log.info("同步绩效协调员到SF系统第{}次", i);
            try {
                log.info("本地同步绩效协调员数据:{}", JSONUtil.toJsonStr(split.get(i)));
                String body =
                        new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).method(Method.POST).body(JSONUtil.toJsonStr(split.get(i))).basicAuth(userName, pwd).execute().body();
                JSONObject jsonObject = JSONUtil.parseObj(body);
                log.error("macros|本地同步绩效协调员数据：{}",body);
                JSONArray jsonArray = jsonObject.getJSONArray("d");
                jsonArray.stream().forEach(returnObj -> {
                    JSONObject returnJson = (JSONObject) returnObj;
                    String status = returnJson.getStr("status");
                    /**
                     * 如果失败记录日志,继续执行其他的数据
                     */
                    if (!"OK".equals(status)) {
                        log.error("同步绩效协调员到SF系统失败:{}", JSONUtil.toJsonStr(returnJson));
                        //saveErrorLog(userId, null, null);
                    }
                });
            } catch (Exception e) {
                log.error("同步绩效协调员到SF系统异常:{}", e);
                //saveErrorLog(JSONUtil.toJsonStr(obj), null, body);
            }
        }
        log.info("同步绩效协调员到SF系统结束时间:{}", LocalDateTime.now());
    }

    private void saveErrorLog(String key, List<String> value, String body) {
        HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
        Map<String, Object> map = new HashMap<>();
        map.put("key", key);
        map.put("value", value);
        hrsfPmsWriteDataBackLog.setEmailStatus(EmailStatusEnum.ZERO.getType());
        hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.syncPmsJobHandler.getType());
        hrsfPmsWriteDataBackLog.setResponseBody(body);
        hrsfPmsWriteDataBackLog.setErrorInfo(JSONUtil.toJsonStr(map));
        hrsfPmsWriteDataBackLog.setName(key);
        writeDataBackLogService.save(hrsfPmsWriteDataBackLog);

    }


    /**
     * 惩处信息
     *
     * @param event
     */
    void handlePmsUpdateD(PmsUpdateEvent event) {
        /**
         * 等待2分钟后在执行，避免同时更新同一条数据，造成死锁
         */
        try {
            Thread.sleep(20 * 60 * 1000);

            Map<String, Long> baseMap = event.getBaseMap();
            Map<String, Long> empIdMap = event.getEmpIdMap();
            ArrayList<HrsfPmsuserBase> pmsUserBaseList = new ArrayList<>();
            String punish = StrUtil.format(event.getUrl());
            LocalDate startLocalDate = event.getStartLocalDate();
            LocalDate endLocalDate = event.getEndLocalDate();
            /**
             * 存了一个当前绩效类型和绩效年度的List
             */
            log.info("惩处信息的url:{}", punish);
            String bodyPunish;
            try {
                bodyPunish = new HttpRequestCustom(punish).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            } catch (HttpException e) {
                log.error("惩处信息的url失败,自动重试一次");
                bodyPunish = new HttpRequestCustom(punish).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            }
            JSONObject punishJson = JSONUtil.parseObj(bodyPunish);
            log.error("macros|惩处信息的body：{}",bodyPunish);
            List<HrsfPmsUserPunishDTO> punishList = punishJson.getJSONObject("d").getJSONArray("results")
                    .stream().map(obj -> {
                        String userId = ((JSONObject) obj).getStr("userId");
                        String startDate = ((JSONObject) obj).getStr("startDate");
                        //这里的startDate是时间戳格式
                        startDate = startDate.substring(startDate.indexOf("(") + 1, startDate.indexOf(")"));
                        /**
                         * --todo
                         * 过滤出当前考核期的惩罚信息
                         * startDate>=考核开始日期，且startDate<=考核结束日期
                         */
                        Optional<Object> labelOption = ((JSONObject) obj).getJSONObject("nameNav").getJSONObject("picklistLabels").getJSONArray("results")
                                .stream().filter(data -> (CommonConstants.EN_US.equals(((JSONObject) data).getStr("locale")))).findFirst();
                        String punishLabel = "";
                        if (labelOption.isPresent()) {
                            punishLabel = ((JSONObject) labelOption.get()).getStr("label");
                        }
                        /**
                         * 返回是时间戳类型
                         * 先转成LocalDate在转化成YYYY-MM-DD
                         */
                        LocalDate localDate = Instant.ofEpochMilli(Long.valueOf(startDate)).atZone(ZoneOffset.ofHours(8)).toLocalDate();
                        if (localDate.isAfter(startLocalDate) && localDate.isBefore(endLocalDate)) {
                            return new HrsfPmsUserPunishDTO(userId, localDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + punishLabel);
                        } else {
                            return null;
                        }
                    }).collect(Collectors.toList());
            //惩罚信息
            Map<String, String> punishMap = punishList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(HrsfPmsUserPunishDTO::getStaffId, Collectors
                    .mapping(HrsfPmsUserPunishDTO::getPunishInfo, Collectors.joining(", "))));
            punishMap.entrySet().stream().forEach(obj -> {
                HrsfPmsuserBase pmsuserBase = new HrsfPmsuserBase();
                pmsuserBase.setStaffId(obj.getKey());
                pmsuserBase.setPunishInfo(obj.getValue());
                pmsuserBase.setId(baseMap.get(obj.getKey()));
                pmsUserBaseList.add(pmsuserBase);
            });
            if (CollectionUtil.isNotEmpty(pmsUserBaseList)) {
                hrsfPmsuserBaseService.updateBatchById(pmsUserBaseList.stream().filter(p -> p.getId() != null).collect(Collectors.toList()));
            }
        } catch (Exception e) {
            log.error("同步惩处信息失败:{}", e);
            // --todo
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setTemplate("launch_failed_template.html");
            emailInfoDTO.setSubject("开发平台绩效考核启动失败通知");
            emailInfoDTO.setSendTo(operationEmail);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
        }
    }

    /**
     * 调岗考核最终总分、调岗考核业绩等级
     *
     * @param event
     */
    void handlePmsUpdateC(PmsUpdateEvent event) {
        try {
            /**
             * 等待6分钟后在执行，避免同时更新同一条数据，造成死锁
             */
            Thread.sleep(16 * 1000 * 60);
            int i = 0;
            Boolean sync = true;
            while (sync) {
                sync = this.syncRatingLevel(i * size, size, event);
                i++;
            }
        } catch (Exception e) {
            log.error("同步调岗考核最终总分、调岗考核业绩等级:{}", e);
            // --todo
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setTemplate("launch_failed_template.html");
            emailInfoDTO.setSubject("开发平台绩效考核启动失败通知");
            emailInfoDTO.setSendTo(operationEmail);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
        }
    }

    /**
     * 调岗考核最终总分、调岗考核业绩等级
     *
     * @param event
     * @return
     */
    private Boolean syncRatingLevel(long current, long size, PmsUpdateEvent event) {
        Map<String, Long> baseMap = event.getBaseMap();
        Map<String, Long> empIdMap = event.getEmpIdMap();
        ArrayList<HrsfPmsuserBase> pmsuserBaseList = new ArrayList<>();
        //调岗分数
        String trans = StrUtil.format(event.getUrl(), event.getStartDate(), event.getEndDate(), current, size);
        log.info("调岗考核最终总分、调岗考核业绩等级的url:{}", trans);
        String bodyTrans;
        try {
            bodyTrans = new HttpRequestCustom(trans).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                    .basicAuth(userName, pwd).execute().body();
        } catch (HttpException e) {
            log.error("调岗考核最终总分、调岗考核业绩等级的url失败,自动重试一次");
            bodyTrans = new HttpRequestCustom(trans).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                    .basicAuth(userName, pwd).execute().body();
        }
        JSONObject transJson = JSONUtil.parseObj(bodyTrans);
        log.error("macros|调岗考核最终总分body：{}",bodyTrans);
        JSONArray jsonArray = transJson.getJSONObject("d").getJSONArray("results");
        jsonArray.stream().forEach(obj -> {
            //员工ID
            String formSubjectId = ((JSONObject) obj).getStr("formSubjectId");
            JSONObject contentJson = ((JSONObject) obj).getJSONObject("formLastContent");
            JSONObject formSubjectJson = ((JSONObject) obj).getJSONObject("formSubject");
            String formContentId = contentJson.getStr("formContentId");
            String formDataId = contentJson.getStr("formDataId");

            //根据formContentId、formDataId再去调接口获取调岗信息
            /**
             * 获取等级字段
             */
            String ratingStr = StrUtil.format(sfSyncOverallFormRating, Long.valueOf(formContentId), Long.valueOf(formDataId));
            String bodyRating = new HttpRequestCustom(ratingStr).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                    .basicAuth(userName, pwd).execute().body();
            JSONObject ratingJson = JSONUtil.parseObj(bodyRating);
            log.error("macros|获取等级字段body：{}",bodyRating);
            //最终业绩等级
            String textRating = ratingJson.getJSONObject("d").getStr("textRating");
            /**
             * 获取评分字段
             */
            String ratingStrTwo = StrUtil.format(overallAdjustedRating, Long.valueOf(formContentId), Long.valueOf(formDataId));
            String bodyRatingTwo = new HttpRequestCustom(ratingStrTwo).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                    .basicAuth(userName, pwd).execute().body();
            JSONObject ratingJsonTwo = JSONUtil.parseObj(bodyRatingTwo);
            log.error("macros|获取评分字段body：{}",bodyRatingTwo);
            String rating = ratingJsonTwo.getJSONObject("d").getStr("rating");
            //获取员工号
            String empId = formSubjectJson == null ? null : formSubjectJson.getStr("empId");
            if (StrUtil.isNotBlank(empId)) {
                Long id = empIdMap.get(empId);
                String idTransTotalScore = StrUtil.format(CONTENT_TRANS, rating, textRating);
                if (id != null) {
                    pmsuserBaseList.add(new HrsfPmsuserBase(formSubjectId, idTransTotalScore, id));
                }
            }

        });
        /**
         * 1、先处理当前页的id重复数据
         * 重复的数据用TransTotalScore相加
         *
         */
        if (CollectionUtil.isNotEmpty(pmsuserBaseList)) {
            Map<Long, String> sameIdDataMap = pmsuserBaseList.stream().collect(Collectors.toMap(
                    HrsfPmsuserBase::getId, HrsfPmsuserBase::getTransTotalScore, (e1, e2) -> (e1 + " | " + e2)
            ));

            Map<Long, String> existDataMap = hrsfPmsuserBaseService.lambdaQuery().select(HrsfPmsuserBase::getTransTotalScore, HrsfPmsuserBase::getId)
                    .isNotNull(HrsfPmsuserBase::getTransTotalScore)
                    .in(HrsfPmsuserBase::getId, sameIdDataMap.keySet()).list()
                    .stream().collect(Collectors.toMap(HrsfPmsuserBase::getId, HrsfPmsuserBase::getTransTotalScore));

            /**
             * 处理表中的重复数据
             * 重复的数据用TransTotalScore字段拼接
             */
            existDataMap.entrySet().forEach(obj -> {
                        String transTotalScore = sameIdDataMap.get(obj.getKey());
                        sameIdDataMap.put(obj.getKey(), obj.getValue() + " | " + transTotalScore);
                    }
            );
            ArrayList<HrsfPmsuserBase> realList = new ArrayList<>();
            sameIdDataMap.entrySet().forEach(obj -> realList.add(new HrsfPmsuserBase(obj.getKey(), obj.getValue())));

            if (CollectionUtil.isNotEmpty(realList)) {
                hrsfPmsuserBaseService.updateBatchById(realList.stream().filter(p -> p.getId() != null).collect(Collectors.toList()));
            }
        }
        return jsonArray.size() > 0;
    }

    /**
     * 季度分数
     *
     * @param event
     */
    void handlePmsUpdateB(PmsUpdateEvent event) {
        /**
         * 等待4分钟后在执行，避免同时更新同一条数据，造成死锁
         */
        int errorNum = 0;
        try {
            Thread.sleep(10 * 60 * 1000);
        } catch (Exception e) {
            log.error("同步季度分数线程睡眠失败:{}", e);
        }
        int i = 0;
        Boolean sync = true;
        /**
         * 这里设置i小于100
         * 防止接口异常,导致sync始终返回true
         * 设置个最大值防止无限次调用
         */
        while (sync && i<100) {
            sync = this.syncRating(i * size, size, event, errorNum);
            i++;
        }
        /**
         * 如果其中的错误次数大于0
         * 则发送邮件提醒
         */
        if (errorNum > 0) {
            // --todo
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setTemplate("launch_failed_template.html");
            emailInfoDTO.setSubject("开发平台绩效考核启动失败通知");
            emailInfoDTO.setSendTo(operationEmail);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
        }
        /**
         * 更新均分字段
         */
        ArrayList<HrsfPmsuserBase> pmsUserBaseList = new ArrayList<>();
        List<HrsfPmsuserBase> noAverageScoreList;
        /**
         * 如果这里为True,则表示是新增员工
         * 则不需要更新整个员工表。只需要更新部分员工即可
         */
        if (event.getAddUserFlag()) {
            List<String> addUserIdList = event.getBaseMap().keySet().stream().collect(toList());
            noAverageScoreList = hrsfPmsuserBaseService.list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                    .eq(HrsfPmsuserBase::getAssessYear, event.getAssesYear())
                    .eq(HrsfPmsuserBase::getAssessType, event.getAssesType())
                    .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType())
                    .in(HrsfPmsuserBase::getStaffId, addUserIdList)
                    .and(wp -> wp.isNotNull(HrsfPmsuserBase::getQ1Score).or().isNotNull(HrsfPmsuserBase::getQ2Score).or().isNotNull(HrsfPmsuserBase::getQ3Score))
                    .select(HrsfPmsuserBase::getQ1Score, HrsfPmsuserBase::getQ2Score, HrsfPmsuserBase::getQ3Score, HrsfPmsuserBase::getId, HrsfPmsuserBase::getStaffId));
        } else {
            noAverageScoreList = hrsfPmsuserBaseService.list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                    .eq(HrsfPmsuserBase::getAssessYear, event.getAssesYear())
                    .eq(HrsfPmsuserBase::getAssessType, event.getAssesType())
                    .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType())
                    .and(wp -> wp.isNotNull(HrsfPmsuserBase::getQ1Score).or().isNotNull(HrsfPmsuserBase::getQ2Score).or().isNotNull(HrsfPmsuserBase::getQ3Score))
                    .select(HrsfPmsuserBase::getQ1Score, HrsfPmsuserBase::getQ2Score, HrsfPmsuserBase::getQ3Score, HrsfPmsuserBase::getId, HrsfPmsuserBase::getStaffId));
        }
        noAverageScoreList.stream().forEach(userBase -> {
            HrsfPmsuserBase hrsfPmsuserBase = new HrsfPmsuserBase();
            BigDecimal q1Score = userBase.getQ1Score();
            BigDecimal q2Score = userBase.getQ2Score();
            BigDecimal q3Score = userBase.getQ3Score();
            Double averageDouble = Arrays.asList(q1Score, q2Score, q3Score).stream().filter(obj -> obj != null).collect(Collectors.averagingDouble(BigDecimal::doubleValue));
            log.info("averageDouble={},q1Score={},q2Score={},q1Score3={}",averageDouble,q1Score,q2Score,q3Score);
            /**
             * 取出均分要做加5的逻辑
             */
            BigDecimal averageScore = new BigDecimal(averageDouble).setScale(2, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(5));
            log.info("averageScore={}",averageScore);

            hrsfPmsuserBase.setAverageScore(averageScore);
            hrsfPmsuserBase.setId(userBase.getId());
            hrsfPmsuserBase.setStaffId(userBase.getStaffId());
            pmsUserBaseList.add(hrsfPmsuserBase);
        });
        if (CollectionUtil.isNotEmpty(pmsUserBaseList)) {
            hrsfPmsuserBaseService.updateBatchById(pmsUserBaseList.stream().filter(p -> p.getId() != null).collect(Collectors.toList()));
        }
        List<List<HrsfPmsuserBase>> split = ListUtil.split(pmsUserBaseList, BATCH_COUNT);
        for (int l = 0; l < split.size(); l++) {
            log.info("回写季度均分给主数据到SF系统第{}次,共{}次", l + 1, split.size());
            List<HrsfPmsuserBase> baseList = split.get(l);
            List<Map> paraMapList = new ArrayList<>();
            if (CollUtil.isNotEmpty(baseList)) {
                for (HrsfPmsuserBase hrsfPmsuserBase : baseList) {
                    Map<String, Object> paraMap = new HashMap<>();
                    Map<String, Object> subParaMap = new HashMap<>();
                    subParaMap.put("uri", StrUtil.format(USER_ID, hrsfPmsuserBase.getStaffId()));
                    paraMap.put("fax", hrsfPmsuserBase.getAverageScore() + "%");
                    paraMap.put("__metadata", subParaMap);
                    paraMapList.add(paraMap);
                }
            }
            try {
                log.info("回写季度均分给主数据入参值:{}", JSONUtil.toJsonStr(paraMapList));
                String body = new HttpRequestCustom(upsertApi).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paraMapList)).basicAuth(userName, pwd).execute().body();
                JSONObject jsonObject2 = JSONUtil.parseObj(body);
                log.error("macros|回写季度均分给主数据body：{}",body);
                JSONArray jsonArray = jsonObject2.getJSONArray("d");
                jsonArray.stream().forEach(returnObj -> {
                    JSONObject returnJson = (JSONObject) returnObj;
                    String status = returnJson.getStr("status");
                    if (!"OK".equals(status)) {
                        log.error("回写季度均分给主数据失败:{}", JSONUtil.toJsonStr(returnJson));
                    }
                });
            } catch (Exception e) {
                log.error("回写季度均分给主数据异常:{}", e);
                //如果失败自动重试一次
                try {
                    String bodyRetry = new HttpRequestCustom(upsertApi).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paraMapList)).basicAuth(userName, pwd).execute().body();
                    log.error("macros|回写季度均分给主数据自动重试一次:{}", bodyRetry);
                } catch (Exception error) {
                    log.error("回写季度均分给主数据自动重试一次失败:{}", error);
                }
            }
        }
        log.info("回写季度均分给主数据到SF系统完成");
    }

    /**
     * 同步绩效分数
     *
     * @param event
     * @return
     */
    private Boolean syncRating(long current, long size, PmsUpdateEvent event, int errorNum) {

        try {
            Map<String, Long> baseMap = event.getBaseMap();
            Map<String, Long> empIdMap = event.getEmpIdMap();
            ArrayList<HrsfPmsuserBase> pmsuserBaseList = new ArrayList<>();
            //获取绩效分数
            String qPmsRating = StrUtil.format(event.getUrl(), event.getAssesYear(), current, size);
            log.error("季度分数的url:{}", qPmsRating);
            String bodyQ1Rating;
            try {
                bodyQ1Rating = new HttpRequestCustom(qPmsRating).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            } catch (HttpException e) {
                log.error("获取季度分数的url接口超时,自动重新执行一次");
                bodyQ1Rating = new HttpRequestCustom(qPmsRating).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            }
            log.error("macros|季度分数的body：{}",bodyQ1Rating);
            JSONObject qRationJson = JSONUtil.parseObj(bodyQ1Rating);
            /**
             * 处理季度均分的逻辑
             * 先排除cust_type为YEAR的数据
             */
            /**
             * 取出均分要做加5的逻辑
             */
            /**
             * JSONArray jsonArray = qRationJson.getJSONObject("d").getJSONArray("results");
             Map<String, Double> averageMap = jsonArray.stream().filter(obj -> !AssessTypeEnum.YEAR.getType()
             .equals(((JSONObject) obj).getStr("cust_type"))).collect(Collectors.groupingBy(obj -> ((JSONObject) obj).getStr("externalCode")
             , Collectors.averagingDouble(obj -> Double.valueOf(((JSONObject) obj).getStr("cust_rating")))));


             Map<String, BigDecimal> averageNewMap = averageMap.entrySet().stream().collect(Collectors.toMap(obj -> obj.getKey(),
             obj -> new BigDecimal(obj.getValue()).setScale(2, BigDecimal.ROUND_HALF_UP).add(BigDecimal.valueOf(5))));*/
            JSONArray jsonArray = qRationJson.getJSONObject("d").getJSONArray("results");
            jsonArray.stream().forEach(obj -> {
                //员工ID
                String externalCode = ((JSONObject) obj).getStr("externalCode");

                String custRating = ((JSONObject) obj).getStr("cust_rating");
                //季度Q1/Q2/Q3
                String custType = ((JSONObject) obj).getStr("cust_type");

                //获取EMPID信息
                JSONObject externalCodeNav = ((JSONObject) obj).getJSONObject("externalCodeNav");
                String empId = externalCodeNav == null ? null : externalCodeNav.getStr("empId");
                log.info("syncRating|{}=custRating={}",custType,custRating);
                if (StrUtil.isNotBlank(empId)) {
                    HrsfPmsuserBase pmsuserBase = new HrsfPmsuserBase();
//                    pmsuserBase.setStaffId(externalCode);
                    pmsuserBase.setQ1Score(AssessTypeEnum.Q1.getType().equals(custType) ? BigDecimal.valueOf(Long.parseLong(custRating)) : null);
                    pmsuserBase.setQ2Score(AssessTypeEnum.Q2.getType().equals(custType) ? BigDecimal.valueOf(Long.parseLong(custRating)) : null);
                    pmsuserBase.setQ3Score(AssessTypeEnum.Q3.getType().equals(custType) ? BigDecimal.valueOf(Long.parseLong(custRating)) : null);
                    /**
                     * 2022-09-21
                     * 这里改成用empID去匹配
                     */
                    //pmsuserBase.setId(baseMap.get(externalCode));
                    pmsuserBase.setId(empIdMap.get(empId));

                    pmsuserBaseList.add(pmsuserBase);
                }

            });

            if (CollectionUtil.isNotEmpty(pmsuserBaseList)) {
                hrsfPmsuserBaseService.updateBatchById(pmsuserBaseList.stream().filter(p -> p.getId() != null).collect(Collectors.toList()));
            }
            return jsonArray.size() > 0;
        } catch (Exception e) {
            log.error("同步季度分数失败:{}", e);
            errorNum++;
            return true;
        }

    }

    /**
     * 表单ID、Content ID | 生成员工档案的DeepLink
     *
     * @param event
     */
    void handlePmsUpdateA(PmsUpdateEvent event) {
        try {
            int i = 0;
            Boolean sync = true;
            /**
             * 这里设置i小于50
             * 防止接口异常,导致sync始终返回true
             * 设置个最大值防止无限次调用
             */
            while (sync && i<50) {
                sync = this.syncContentID(i * size, size, event);
                i++;
            }
        } catch (Exception e) {
            log.error("同步表单ID、Content ID | 生成员工档案的DeepLink失败:{}", e);
            // --todo
            EmailInfoDTO emailInfoDTO = new EmailInfoDTO();
            emailInfoDTO.setTemplate("launch_failed_template.html");
            emailInfoDTO.setSubject("开发平台绩效考核启动失败通知");
            emailInfoDTO.setSendTo(operationEmail);
            remoteEmailService.sendEmailByInfo(emailInfoDTO, SecurityConstants.FROM_IN);
        }
    }

    /**
     * 同步表单ID、Content ID
     *
     * @param current
     * @param size
     * @return
     */
    private Boolean syncContentID(long current, long size, PmsUpdateEvent event) {
        String format = StrUtil.format(event.getUrl(), event.getStartDate(), event.getEndDate(), current, size);
        try {
            Map<String, Long> baseMap = event.getBaseMap();
            ArrayList<HrsfPmsuserBase> pmsuserBaseList = new ArrayList<>();
            log.error("macros|获取表单ID、Content ID的url:{}", format);
            String body;
            try {
                body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            } catch (HttpException e) {
                log.error("获取表单ID、Content ID的url接口超时,自动重新执行一次");
                body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                        .basicAuth(userName, pwd).execute().body();
            }
            JSONObject jsonObject = JSONUtil.parseObj(body);
            log.error("macros|获取表单ID、Content body:{}", body);
            JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
            Map<String, Object> dataMap = jsonArray.parallelStream().collect(Collectors.groupingBy(obj -> ((JSONObject) obj).getStr("formSubjectId")
                    , Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> {
                        String creationDate1 = ((JSONObject) c1).getStr("creationDate");
                        String creationDate2 = ((JSONObject) c2).getStr("creationDate");
                        return DatetimeUtils.DateStrToLong(creationDate1) > DatetimeUtils.DateStrToLong(creationDate2) ? c1 : c2;
                    }), Optional::get)));
            dataMap.values().stream().forEach(obj -> {
                //员工ID
                String formSubjectId = ((JSONObject) obj).getStr("formSubjectId");
                String formDataId = ((JSONObject) obj).getStr("formDataId");
                //更新员工deepLink、表单ID、Content ID到数据表中
                pmsuserBaseList.add(new HrsfPmsuserBase(formSubjectId, null, formDataId, baseMap.get(formSubjectId), StrUtil.format(deepLink, formSubjectId)));
            });
            if (CollectionUtil.isNotEmpty(pmsuserBaseList)) {
                hrsfPmsuserBaseService.updateBatchById(pmsuserBaseList.stream().filter(p -> p.getId() != null).collect(Collectors.toList()));
            }
            return jsonArray.size() > 0;
        } catch (HttpException err) {
            HrsfPmstart hrsfPmstart = event.getHrsfPmstart();
            Map<String, String> map = new HashMap<>();
            map.put("url", format);
            map.put("assessYear", hrsfPmstart.getAssesYear());
            map.put("assessType", hrsfPmstart.getAssesType());
            HrsfPmsWriteDataBackLog hrsfPmsWriteDataBackLog = new HrsfPmsWriteDataBackLog();
            hrsfPmsWriteDataBackLog.setEmailStatus(EmailStatusEnum.ZERO.getType());
            hrsfPmsWriteDataBackLog.setFlag(ErrorFlag.syncFormId.getType());
            hrsfPmsWriteDataBackLog.setErrorInfo(JSONUtil.toJsonStr(map));
            hrsfPmsWriteDataBackLog.setName("同步表单ID:" + current + "至" + size);
            writeDataBackLogService.saveOrUpdate(hrsfPmsWriteDataBackLog);
            /**
             * 这里出现超时异常
             * 则返回true,继续往下执行
             * 本次失败记录到重试表中,定时任务去处理失败记录
             */
            return true;
        }
    }

    /**
     * 季度均分值空
     * @param event
     */
    private void handlePmsUpdateM(PmsUpdateEvent event){
        log.info("{}年度{}季度绩效，开始设置空值的季度均分给主数据到SF系统",event.getAssesYear(),event.getAssesType());
        List<HrsfPmsuserBase> baseList = hrsfPmsuserBaseService.list(Wrappers.<HrsfPmsuserBase>lambdaQuery()
                .eq(HrsfPmsuserBase::getAssessYear, event.getAssesYear())
                .eq(HrsfPmsuserBase::getAssessType, event.getAssesType())
                .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType())
                .select(HrsfPmsuserBase::getId, HrsfPmsuserBase::getStaffId));

        List<List<HrsfPmsuserBase>> split = ListUtil.split(baseList, BATCH_COUNT);
        for (int i=0; i < split.size(); i++){
            log.info("设置空值的季度均分给主数据到SF系统第{}次,共{}次", i + 1, split.size());
            List<Map> paraMapList = new ArrayList<>();
            for (HrsfPmsuserBase hrsfPmsuserBase : split.get(i)) {
                Map<String, Object> paraMap = new HashMap<>();
                Map<String, Object> subParaMap = new HashMap<>();
                subParaMap.put("uri", StrUtil.format(USER_ID, hrsfPmsuserBase.getStaffId()));
                paraMap.put("fax", "");
                paraMap.put("__metadata", subParaMap);
                paraMapList.add(paraMap);
            }
            try {
                log.info("设置空值的季度均分给主数据入参值:{}", JSONUtil.toJsonStr(paraMapList));
                String body = new HttpRequestCustom(upsertApi).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paraMapList)).basicAuth(userName, pwd).execute().body();
                log.error("macros|调用接口同步均分值返参：{}",body);
                JSONObject jsonObject2 = JSONUtil.parseObj(body);
                JSONArray jsonArray = jsonObject2.getJSONArray("d");
                jsonArray.stream().forEach(returnObj -> {
                    JSONObject returnJson = (JSONObject) returnObj;
                    String status = returnJson.getStr("status");
                    if (!"OK".equals(status)) {
                        log.error("设置空值的季度均分给主数据失败:{}", JSONUtil.toJsonStr(returnJson));
                    }
                });
            } catch (Exception e) {
                log.error("设置空值的季度均分给主数据异常:{}", e);
                //如果失败自动重试一次
                try {
                    String bodyRetry = new HttpRequestCustom(upsertApi).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.POST).body(JSONUtil.toJsonStr(paraMapList)).basicAuth(userName, pwd).execute().body();
                    log.error("macros|设置空值的季度均分给主数据自动重试一次:{}", bodyRetry);
                } catch (Exception error) {
                    log.error("设置空值的季度均分给主数据自动重试一次失败:{}", error);
                }
            }
        }
        log.info("设置空值的季度均分给主数据到SF系统完成");
    }
}
