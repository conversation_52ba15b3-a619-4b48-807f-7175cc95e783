package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserBaseDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.service.IEmployeePerformanceService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.pojo.ErrorMessageSubVo;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.HrsfPmsuserBaseDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import com.bbac.hrsf.performance.api.feign.RemotePmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: liu, jie
 * @create: 2022-06-25
 **/
@Slf4j
@Service
public class EmployeePerformanceServiceImpl implements IEmployeePerformanceService {

    @Value("${sf.sync.userData}")
    private String sfSyncUserInfo;

    @Value("${sf.sync.username}")
    private String userName;

    @Value("${sf.sync.pwd}")
    private String pwd;

    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;
    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;

    @Resource
    private IHrsfUserBaseService hrsfUserBaseService;

    @Resource
    private RemotePmsService remotePmsService;

    @Resource
    private IHrsfPmsuserBaseService iHrsfPmsuserBaseService;


    @Override
    public ErrorMessageVo addUser(HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO) {
        ErrorMessageVo errorMessageVo = new ErrorMessageVo();
        List<HrsfPmsuserBase> existPmsUserBaseList = iHrsfPmsuserBaseService.selectPmsUserBaseByStaffIdList(hrsfPmsUserBaseDTO.getStaffIds(), hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType());
        if (CollUtil.isNotEmpty(existPmsUserBaseList)) {
            List<String> fullNameList = existPmsUserBaseList.stream().map(HrsfPmsuserBase::getFullName).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(fullNameList)) {
                String join = StringUtils.join(fullNameList, ",");
                String str = StrUtil.format("员工【{}】在当前校准会议或其他校准会议中存在，请先将员工从校准会议中移除，再进行添加操作！", join);
                throw new CheckedException(str);
            }
        }
        R<HrsfPmstart> pmsStartInfo = remotePmsService.getPmsStartInfoInner(hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType(), SecurityConstants.FROM_IN);

        if (pmsStartInfo.getCode() == 0 && Objects.isNull(pmsStartInfo.getData())) {
            throw new CheckedException("没有对应考核年度和考核类型的会员");
        }


        if (CollUtil.isNotEmpty(hrsfPmsUserBaseDTO.getStaffIds())) {
            for (String staffId : hrsfPmsUserBaseDTO.getStaffIds()) {
                //进行主数据同步，调取接口从SF系统中获取员工最新的主数据信息；
                String format = StrUtil.format(sfSyncUserInfo, staffId);
                log.info("同步员工数据url:{}", format);
                String body = new HttpRequestCustom(format).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET).basicAuth(userName, pwd).execute().body();
                JSONObject jsonObject = JSONUtil.parseObj(body);
                JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
                List<HrsfUserBase> userBaseList = new ArrayList<>();
                if (jsonArray.size() > 0) {
                    userBaseList = jsonArray.stream().map(obj ->
                            HrsfUpmsConvert.INSTANCE.toHrsfUserBase((JSONObject) obj)).collect(Collectors.toList());
                }
                //考核员工基础表初始化；
                if (CollectionUtil.isNotEmpty(userBaseList)) {
                    hrsfUserBaseService.saveOrUpdateBatch(userBaseList);
                }
            }

            if (pmsStartInfo.getCode() == 0) {
                HrsfPmstart data = pmsStartInfo.getData();
                iHrsfPmsuserBaseService.startPmsUser(data, hrsfPmsUserBaseDTO.getStaffIds());
            } else {
                throw new CheckedException(pmsStartInfo.getMsg());
            }

            List<HrsfPmsuserBase> hrsfPmsuserBases = iHrsfPmsuserBaseService.selectPmsUserBaseByStaffIdList(hrsfPmsUserBaseDTO.getStaffIds(), hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType());
            List<HrsfPmsuserBaseDTO> hrsfPmsuserBaseDTOList = new ArrayList<>();
            for (HrsfPmsuserBase hrsfPmsuserBase : hrsfPmsuserBases) {
                HrsfPmsuserBaseDTO hrsfPmsuserBaseDTO = new HrsfPmsuserBaseDTO();
                BeanUtils.copyProperties(hrsfPmsuserBase, hrsfPmsuserBaseDTO);
                hrsfPmsuserBaseDTOList.add(hrsfPmsuserBaseDTO);
            }
            //运行初始化校准会议基础表逻辑，找到该员工的校准归属机构
            //根据机构和职级信息，以及考核年份和考核类型，找到目前存在的校准会议，将员工加入该校准会议
            //若找不到现有的校准会议，新建一个校准会议并生成相应审批流信息，发放给相关的步骤负责人
            List<ErrorMessageSubVo> errorMessageList = remotePmsService.updatePmsUserBase(hrsfPmsuserBaseDTOList, hrsfPmsUserBaseDTO.getAssesYear(), hrsfPmsUserBaseDTO.getAssesType()
                    , SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getUsername()
                    , SecurityUtils.getUser() == null ? "" : SecurityUtils.getUser().getFullName()
                    , SecurityConstants.FROM_IN);
            errorMessageVo.setErrorMessage(errorMessageList);
            errorMessageVo.setSuccessNum(hrsfPmsUserBaseDTO.getStaffIds().size() - errorMessageList.size());
            errorMessageVo.setErrorNum(errorMessageList.size());

        }
        return errorMessageVo;
    }

    /**
     * 与新增几乎一致
     *
     * @param hrsfPmsUserBaseDTO
     * @return
     */
    @Override
    public ErrorMessageVo updateUser(HrsfPmsUserBaseDTO hrsfPmsUserBaseDTO) {
        iHrsfPmsuserBaseService.removeFormId(hrsfPmsUserBaseDTO);
        return addUser(hrsfPmsUserBaseDTO);
    }


}
