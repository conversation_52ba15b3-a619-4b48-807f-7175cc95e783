package com.bbac.hrsf.admin.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfOrganization;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.Set;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Mapper
public interface HrsfOrganizationMapper extends BaseMapper<HrsfOrganization> {

    @Select("select * from HRSF_ORGANIZATION where ROWNUM=1")
    HrsfOrganization selectOneData();

    /**
     * 每天晚上同步到HRSF_ORGANIZATION数据
     * <p>
     * select LISTAGG(DISTINCT SYSTEM, ',') from HRSF_USER_BASE where  SYSTEM is NOT NULL;
     * select LISTAGG(DISTINCT DEPARTMENT, ',') from HRSF_USER_BASE where  DEPARTMENT is NOT NULL;
     * select LISTAGG(DISTINCT SECTION, ',') from HRSF_USER_BASE where  SECTION is NOT NULL;
     * select LISTAGG(DISTINCT USER_GROUP, ',') from HRSF_USER_BASE where  USER_GROUP is NOT NULL;
     * select LISTAGG(DISTINCT USER_LEVEL, ',') from HRSF_USER_BASE where  USER_LEVEL is NOT NULL;
     */
    @Select("select DISTINCT SYSTEM from HRSF_USER_BASE where  SYSTEM is NOT NULL ORDER BY SYSTEM DESC")
    Set<String> selectSystem();

    @Select("select DISTINCT DEPARTMENT from HRSF_USER_BASE where  DEPARTMENT is NOT NULL ORDER BY DEPARTMENT DESC")
    Set<String> selectDepartment();

    @Select("select DISTINCT SECTION from HRSF_USER_BASE where  SECTION is NOT NULL ORDER BY SECTION DESC")
    Set<String> selectSection();

    @Select("select DISTINCT USER_GROUP from HRSF_USER_BASE where  USER_GROUP is NOT NULL ORDER BY USER_GROUP DESC")
    Set<String> selectUserGroup();

    @Select("select DISTINCT USER_LEVEL from HRSF_USER_BASE where  USER_LEVEL is NOT NULL ORDER BY USER_LEVEL DESC")
    Set<String> selectUserLevel();


    @Delete("delete from HRSF_ORGANIZATION")
    void delTable();

}
