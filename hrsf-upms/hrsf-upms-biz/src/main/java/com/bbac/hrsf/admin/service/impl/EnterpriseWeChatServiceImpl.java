package com.bbac.hrsf.admin.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bbac.hrsf.admin.api.dto.TokenReturnDTO;
import com.bbac.hrsf.admin.api.dto.UserInfoDetailReturnDTO;
import com.bbac.hrsf.admin.api.dto.UserInfoReturnDTO;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.PerformanceRatingVO;
import com.bbac.hrsf.admin.config.HttpRequestCustom;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.service.EnterpriseWeChatService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.common.core.exception.HrsfDeniedException;
import com.bbac.hrsf.common.core.exception.ServiceException;
import com.bbac.hrsf.common.core.util.WebUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.service.impl.EnterpriseWeChatServiceImpl</li>
 * <li>CreateTime : 2022/08/10 11:11:24</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EnterpriseWeChatServiceImpl implements EnterpriseWeChatService {

    @Value("${sf.sync.performanceRating}")
    private String sfSyncPerformanceRating;
    @Value("${sf.sync.proxyHost}")
    private String proxyHost;
    @Value("${sf.sync.proxyPort}")
    private int proxyPort;
    @Value("${sf.sync.proxyEnable}")
    private boolean proxyEnable;
    @Value("${sf.sync.username}")
    private String userName;
    @Value("${sf.sync.pwd}")
    private String pwd;
    @Value("${qy.weChat.accessToken}")
    private String accessTokenUrl;
    @Value("${qy.weChat.userInfo}")
    private String userInfo;
    @Value("${qy.weChat.userInfoDetail}")
    private String userInfoDetail;
    private final RedisTemplate<String, Object> redisTemplate;
    private final String COMMON_ACCESS_TOKEN = "COMMON_ACCESS_TOKEN";
    private final String TOKEN_USER_ID_MAPPING_KEY = "hrsf:upms:enterprise_weChat:access_token-user_id-mapping:%s";
    private final IHrsfUserBaseService hrsfUserBaseService;

    /**
     * @param userId
     * @return
     */
    @Override
    public List<PerformanceRatingVO> performanceRating(String userId) {
        List<PerformanceRatingVO> performanceRatingVOList = new ArrayList<>();
        if (StrUtil.isBlank(userId)) {
            log.info("企业微信获取的userID为空,请重试!");
            return performanceRatingVOList;
        }
        // 检查token和userId是否存在对应关系
        String authHeader = WebUtils.getRequest().orElseThrow(ServiceException::new).getHeader(HttpHeaders.AUTHORIZATION);
        String accessToken = authHeader.replace(OAuth2AccessToken.BEARER_TYPE, StrUtil.EMPTY).trim();
        String cacheUserId = this.getMappingCache(accessToken);
        log.warn("\nperformanceRating|accessToken:{},params.userId={},cacheUserId={}\n", accessToken,userId,cacheUserId);
        if (!StrUtil.equals(userId, cacheUserId)) {
            throw new HrsfDeniedException();
        }
        String performanceRatingUrl = StrUtil.format(sfSyncPerformanceRating, userId);
        log.info("企业微信获取绩效数据:{}", performanceRatingUrl);
        String body = new HttpRequestCustom(performanceRatingUrl).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                .basicAuth(userName, pwd).execute().body();
        JSONObject jsonObject = JSONUtil.parseObj(body);
        JSONArray jsonArray = jsonObject.getJSONObject("d").getJSONArray("results");
        if (jsonArray.size() > 0) {
            performanceRatingVOList = jsonArray.stream().map(obj ->
                    HrsfUpmsConvert.INSTANCE.toPerformanceRatingVO((JSONObject) obj)).collect(Collectors.toList());
        }
        /**
         * 2022-09-20
         * 新增逻辑,查询empId
         * 根据userID从主数据里查询empId
         * 然后根据empID查询userID,判断是否存在旧工号
         */
        HrsfUserBase hrsfUserBase = hrsfUserBaseService.getById(userId);
        if (hrsfUserBase != null) {
            String empId = hrsfUserBase.getEmpId();
            log.warn("\n macros>>>>>>>>>>>>>>:userId={},empId={} \n", userId,empId);
            List<HrsfUserBase> list = hrsfUserBaseService.list(Wrappers.<HrsfUserBase>lambdaQuery().eq(HrsfUserBase::getEmpId, empId));
            if (list.size() >= 2) {
                //如果这里大于等于2,则表示存在旧工号
                List<PerformanceRatingVO> finalPerformanceRatingVOList = performanceRatingVOList;
                list.stream().filter(obj -> !userId.equals(obj.getStaffId())).forEach(obj -> {
                    String staffId = obj.getStaffId();
                    String performanceRatingUrlOld = StrUtil.format(sfSyncPerformanceRating, staffId);
                    log.info("企业微信获取旧员工号绩效数据:{}", performanceRatingUrlOld);
                    String bodyOld = new HttpRequestCustom(performanceRatingUrlOld).setHttpProxy(proxyHost, proxyPort, proxyEnable).setMethod(Method.GET)
                            .basicAuth(userName, pwd).execute().body();
                    JSONObject jsonObjectOld = JSONUtil.parseObj(bodyOld);
                    JSONArray jsonArrayOld = jsonObjectOld.getJSONObject("d").getJSONArray("results");
                    if (jsonArrayOld.size() > 0) {
                        finalPerformanceRatingVOList.addAll(jsonArrayOld.stream().map(rating ->
                                HrsfUpmsConvert.INSTANCE.toPerformanceRatingVO((JSONObject) rating)).collect(Collectors.toList()));
                    }
                });
                /**
                 * 这里要重新排序取前4位
                 *
                 */
                return finalPerformanceRatingVOList.stream().filter(obj -> obj.getEffectiveStartDate() != null)
                        .sorted(Comparator.comparing(PerformanceRatingVO::getEffectiveStartDate).reversed()).limit(4).collect(Collectors.toList());
            }
        }

        /**
         * 2024-05-08
         * 新增逻辑，改造此TOKEN为一次性使用，修复企业微信登录后抓包取得请求地址后进行反复操作的漏洞
         * TOKEN改造成一次性使用
         */
        this.removeCacheMapping(accessToken);
        //
        return performanceRatingVOList;
    }

    @Override
    public Map getUserIdByCode(String code, String state) {
        HashMap<String, String> hashMap = new HashMap<>(16);
        try {
            String accessToken = (String) redisTemplate.opsForValue().get(COMMON_ACCESS_TOKEN);
            log.info("getUserIdByCode|accessToken={}", accessToken);
            if (StrUtil.isBlank(accessToken)) {
                String body = new HttpRequestCustom(accessTokenUrl).setHttpProxy(proxyHost, proxyPort, false).setMethod(Method.GET).execute().body();
                TokenReturnDTO tokenReturnDTO = JSONUtil.toBean(body, TokenReturnDTO.class);
                if (tokenReturnDTO.getErrcode() == 0) {
                    accessToken = tokenReturnDTO.getAccess_token();
                    redisTemplate.opsForValue().set(COMMON_ACCESS_TOKEN, accessToken, 7000, TimeUnit.SECONDS);
                } else {
                    log.error("获取ACCESS_TOKEN出现错误!");
                }
            }
            /**
             * 解析body
             */
            if (StrUtil.isNotBlank(accessToken)) {
                String userInfoUrl = StrUtil.format(userInfo, accessToken, code);
                String userInfoStr = new HttpRequestCustom(userInfoUrl).setHttpProxy(proxyHost, proxyPort, false).setMethod(Method.GET).execute().body();
                log.info("返回输出日志userInfoStr:{}",userInfoStr);
                UserInfoReturnDTO userInfoReturnDTO = JSONUtil.toBean(userInfoStr, UserInfoReturnDTO.class);
                log.info("userInfoReturnDTO:{}",userInfoReturnDTO);
                if (userInfoReturnDTO.getErrcode() == 0) {
                    String userId = userInfoReturnDTO.getUserId();
                    log.info("getUserIdByCode|userId={}", userId);
                    if (StrUtil.isNotBlank(userId)) {
                        String userInfoDetailUrl = StrUtil.format(userInfoDetail, accessToken, userId);
                        log.info("请求URL-userInfoDetailUrl:{}",userInfoDetailUrl);
                        String userInfoStrDetail = new HttpRequestCustom(userInfoDetailUrl).setHttpProxy(proxyHost, proxyPort, false).setMethod(Method.GET).execute().body();
                        log.info("返回输出日志userInfoStrDetail:{}",userInfoStrDetail);
                        UserInfoDetailReturnDTO userInfoDetailReturnDTO = JSONUtil.toBean(userInfoStrDetail, UserInfoDetailReturnDTO.class);
                        if (userInfoDetailReturnDTO.getErrcode() == 0) {
                            String staffId = userInfoDetailReturnDTO.getEnglish_name();
                            if (StrUtil.isNotBlank(staffId)) {
                                hashMap.put("userId", staffId);
                                //此处调用自身接口获取TOKEN
                                String tokenStr = new HttpRequestCustom("http://hrsf-gateway:9999/auth/oauth/token?grant_type=client_credentials&scope=server")
                                        .setHttpProxy(proxyHost, proxyPort, false).setMethod(Method.POST).basicAuth("peoplesoft", "peoplesoft").execute().body();
                                JSONObject tokenJSON = JSONUtil.parseObj(tokenStr);
                                String access_token = tokenJSON.getJSONObject("data").getStr("access_token");
                                hashMap.put("access_token", access_token);
                                // 保存token-userId映射关系到redis
                                this.cacheMapping(access_token, staffId);
                                return hashMap;
                            } else {
                                log.error("获取员工STAFF_ID为空!");
                            }
                        } else {
                            log.error("获取员工STAFF_ID出现错误!");
                        }
                    } else {
                        log.error("获取员工USER_ID为空!");
                    }
                } else {
                    log.error("获取员工USER_ID出现错误!");
                }
            } else {
                log.error("为获取到正确的accessToken!");
            }
        } catch (Exception e) {
            log.error("获取员工绩效数据出现错误:{}", e);
        }
        return new HashMap(8);
    }

    /**
     * 获取token-userId映射关系的Redis Key
     */
    private String getMappingKey(String accessToken) {
        return String.format(TOKEN_USER_ID_MAPPING_KEY, accessToken);
    }

    /**
     * 缓存token-userId映射关系
     */
    private void cacheMapping(String accessToken, String userId) {
        redisTemplate.opsForValue().set(getMappingKey(accessToken), userId, 1800, TimeUnit.SECONDS);
    }

    /**
     * 删除token
     */
    private void removeCacheMapping(String accessToken) {
        redisTemplate.delete(getMappingKey(accessToken));
    }

    /**
     * 根据token-userId映射关系获取缓存值
     */
    private String getMappingCache(String accessToken) {
        return (String) redisTemplate.opsForValue().get(getMappingKey(accessToken));
    }
}