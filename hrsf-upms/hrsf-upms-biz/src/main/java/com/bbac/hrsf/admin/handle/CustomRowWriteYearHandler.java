package com.bbac.hrsf.admin.handle;

import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import org.apache.poi.ss.usermodel.Row;

/**
 * @author: liu, jie
 * @create: 2022-06-24
 **/
public class CustomRowWriteYearHandler implements RowWriteHandler {


    @Override
    public void afterRowDispose(RowWriteHandlerContext context) {


        RowWriteHandler.super.afterRowDispose(context);
    }
}
