package com.bbac.hrsf.admin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bbac.hrsf.admin.api.entity.HrsfPmsTalentCardPDF;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface HrsfPmsTalentCardPDFMapper extends BaseMapper<HrsfPmsTalentCardPDF> {

    @Update("TRUNCATE TABLE HRSF_PMS_USER_TALENT_CARD_PDF")
    int truncateCardPdf();

    @Delete("DELETE HRSF_PMS_USER_TALENT_CARD_PDF WHERE STAFF_ID=#{staffId}")
    void deleteInfoById(@Param("staffId") String staffId);
}
