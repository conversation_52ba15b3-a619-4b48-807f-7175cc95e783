package com.bbac.hrsf.admin.event;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.FormulaData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.vo.PmsUserExcelYearUploadVO;
import com.bbac.hrsf.admin.api.vo.UploadMessageSubVo;
import com.bbac.hrsf.admin.convert.HrsfUpmsConvert;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.enums.FormStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.PmsLevelEnumNew;
import com.bbac.hrsf.common.core.constant.enums.ScoreSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <ul>
 * <li>Project : FAW-VW-PRIME-bbac-hrsf</li>
 * <li>ClassName : com.bbac.hrsf.admin.event.PmsUserExcelDataListener</li>
 * <li>CreateTime : 2022/05/24 14:14:03</li>
 * <li>Description :
 * <p>
 * </ul>
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public class PmsUserExcelYearDataListener implements ReadListener<PmsUserExcelYearUploadVO> {

    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 1000;

    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */
    private IHrsfPmsuserBaseService hrsfPmsuserBaseService;

    private Map<String, HrsfPmsuserBase> pmsUserBaseMap;

    private List<UploadMessageSubVo> messageVoList;

    private List<HrsfPmsuserBase> cachedDataList;

    private List<String> existStaffIdList;

    private static List<Double> targetScoreList = Arrays.asList(0d, 0.1d, 0.2d, 0.3d, 0.4d, 0.5d, 0.6d, 0.7d, 0.8d, 0.9d, 1.0d, 1.1d, 1.2d, 1.3d);

    private static List<String> pmsLevelDescList = Arrays.asList("需改进 Improvement Required","及格 Sufficient","良好 Successful","优秀 Excellent","杰出 Outstanding");


    private final String error1 = "该员工分数已提交，目前无法修改 Rating has already submitted, and cannot be changed now.";
    private final String error2 = "该员工不在本校准会议中，请确认后再次上传 The employee is not belong to this calibration session, please check and try again.";
    private final String error3 = "该员工的分数或等级信息为空，请检查后再次上传 Final result or performance category is unrated, please check and try again.";
    private final String error4 = "该员工需通过评分表进行分数修改 Rating should be changed through the  assessment form.";
    private final String error5 = "该员工的分数计算有误，请检查后再次上传 The rating calculated is wrong, please check and try again.";
    private final String error6 = "评分或等级与机构负责人校准结果不一致，请检查。Rating or category is not consistent with the result of calibration execution.";
    private final String error7 = "该员工重复上传，系统将上传第一条数据，本条数据不上传；Data not uploaded as there are repetitive records for the employee. Only first data record uploaded.";
    private final String error8 = "该员工小指标分数或业绩等级填写格式有误 Incorrect data format for item rating or performance category.";

    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param hrsfPmsuserBaseService
     */
    public PmsUserExcelYearDataListener(IHrsfPmsuserBaseService hrsfPmsuserBaseService
            , List<HrsfPmsuserBase> cachedDataList, List<UploadMessageSubVo> messageVoList, Map<String, HrsfPmsuserBase> pmsUserBaseMap, List<String> existStaffIdList) {
        this.hrsfPmsuserBaseService = hrsfPmsuserBaseService;
        this.cachedDataList = cachedDataList;
        this.messageVoList = messageVoList;
        this.pmsUserBaseMap = pmsUserBaseMap;
        this.existStaffIdList = existStaffIdList;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(PmsUserExcelYearUploadVO data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSONUtil.toJsonStr(data));
        /**
         * 从数据库中获取的对象
         * 1.判断若员工的表单状态不等于评分中或已评分，跳过该条记录，分数不上传，并在报错反馈中展示报错原因：
         * 该员工分数已提交，目前无法修改 Rating has already submitted, and cannot be changed now;
         * 2.判断若上传文件中的工号字段不在该校准会议的员工名单中，跳过该条记录，分数不上传，并在报错反馈中展示报错原因：
         * 该员工不在本校准会议中，请确认后再次上传 The employee is not belong to this calibration session, please check and try again;
         */

        Integer rowIndex = context.readRowHolder().getRowIndex();

        /**
         * 解决问题清单374的问题
         * 行数要加上1
         *
         */
        rowIndex = rowIndex + 1;
        /**
         * 先判断是不是存在
         */
        if (existStaffIdList.contains(data.getStaffId())) {
            messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error7));
            return;
        }
        if (StrUtil.isNotBlank(data.getStaffId())) {
            existStaffIdList.add(data.getStaffId());
        }
        HrsfPmsuserBase hrsfPmsuserBase = pmsUserBaseMap.get(data.getStaffId());
        if (hrsfPmsuserBase == null) {
            messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error2));
            return;
        }
        String scoreSource = hrsfPmsuserBase.getScoreSource();
        if (ScoreSourceEnum.SF.getType().equals(scoreSource)) {
            messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error4));
            return;
        }

        String formStatus = hrsfPmsuserBase.getFormStatus();
        if (!(FormStatusEnum.In_Rating.getType().equals(formStatus) || FormStatusEnum.Rated.getType().equals(formStatus))) {
            messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error1));
            return;
        }
        /**
         * 校验得分是否为空
         */
        if (data.getTarget1Score() == null || data.getTarget2Score() == null ||
                data.getTarget3Score() == null || data.getTarget4Score() == null ||
                data.getTarget5Score() == null ||
                data.getTarget6Score() == null || data.getTarget7Score() == null ||
                data.getTarget8Score() == null || data.getTarget9Score() == null ||
                data.getTarget10Score() == null ||
                CellDataTypeEnum.EMPTY.equals(data.getTotalScore().getType()) ||
                CellDataTypeEnum.EMPTY.equals(data.getWeightRating().getType()) ||
                StrUtil.isBlank(data.getPmsLevelDesc())
        ) {
            messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error3));
            return;
        }
        /**
         * 校验分数在不在固定值里
         */
        if (!targetScoreList.contains(data.getTarget1Score()) || !targetScoreList.contains(data.getTarget2Score()) || !targetScoreList.contains(data.getTarget3Score())
                || !targetScoreList.contains(data.getTarget4Score())
                || !targetScoreList.contains(data.getTarget5Score())
                || !targetScoreList.contains(data.getTarget6Score())
                || !targetScoreList.contains(data.getTarget7Score())
                || !targetScoreList.contains(data.getTarget8Score())
                || !targetScoreList.contains(data.getTarget9Score())
                || !targetScoreList.contains(data.getTarget10Score())
                || !pmsLevelDescList.contains(data.getPmsLevelDesc())
        ) {
            messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error8));
            return;
        }

        /**
         * 校验上传的加权总分和最总得分是否一致
         * 如果不一致先计算一版实际数字
         * 从excel解析到的分时是带着百分号,例如15% 从数据库中取出的数据则不带百分号, 例如15
         */

        //根据公式再做一次计算
        BigDecimal weightRatingCalculate =
                BigDecimal.valueOf(data.getTarget1Score()).multiply(BigDecimal.valueOf(0.2))
                        .add(BigDecimal.valueOf(data.getTarget2Score()).multiply(BigDecimal.valueOf(0.2)))
                        .add(BigDecimal.valueOf(data.getTarget3Score()).multiply(BigDecimal.valueOf(0.1)))
                        .add(BigDecimal.valueOf(data.getTarget4Score()).multiply(BigDecimal.valueOf(0.1)))
                        .add(BigDecimal.valueOf(data.getTarget5Score()).multiply(BigDecimal.valueOf(0.1)))
                        .add(BigDecimal.valueOf(data.getTarget6Score()).multiply(BigDecimal.valueOf(0.05)))
                        .add(BigDecimal.valueOf(data.getTarget7Score()).multiply(BigDecimal.valueOf(0.05)))
                        .add(BigDecimal.valueOf(data.getTarget8Score()).multiply(BigDecimal.valueOf(0.1)))
                        .add(BigDecimal.valueOf(data.getTarget9Score()).multiply(BigDecimal.valueOf(0.05)))
                        .add(BigDecimal.valueOf(data.getTarget10Score()).multiply(BigDecimal.valueOf(0.05)))
                        .multiply(BigDecimal.valueOf(100));
        /**
         * 总分计算按照半分发
         * 先除以5取余数
         * 在用余数跟2.5作比较,比2.5大则取5否则取0
         * 然后再用结果加上这个值
         */

        BigDecimal[] resultBigDecimal = weightRatingCalculate.divideAndRemainder(BigDecimal.valueOf(5));
        BigDecimal totalScoreCalculate = resultBigDecimal[0].multiply(BigDecimal.valueOf(5))
                .add(resultBigDecimal[1].compareTo(BigDecimal.valueOf(2.5)) != -1 ? BigDecimal.valueOf(5) : BigDecimal.ZERO);
        BigDecimal totalScoreFromExcel;
        CellData<String> weightRating = data.getWeightRating();
        CellData<String> totalScore = data.getTotalScore();
        FormulaData formulaData = weightRating.getFormulaData();
        if (formulaData != null && CellDataTypeEnum.NUMBER.equals(weightRating.getType())) {
            totalScoreFromExcel = totalScoreCalculate;
        } else {
            String weightRatingStr = weightRating.getData();
            String totalScoreStr = totalScore.getData();
            totalScoreFromExcel = BigDecimal.valueOf(Double.valueOf(totalScoreStr.replace(
                    CommonConstants.PERCENT, "")));
            BigDecimal weightRatingFromExcel = BigDecimal.valueOf(Double.valueOf(weightRatingStr.replace(
                    CommonConstants.PERCENT, "")));
            if (totalScoreFromExcel.compareTo(totalScoreCalculate) != 0 || weightRatingFromExcel.compareTo(weightRatingCalculate) != 0) {
                messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error5));
                return;
            }
        }

        /**
         * 因此蓝领分数校验逻辑为：
         * 1.判断若员工有绿色标记
         * 判断若导入的最终总分和业绩等级与考核员工基础表中存储的值相同，无需写入值，取消绿色标记；
         * 判断若导入的最终总分和业绩等级与当前表中存储的值有任一值不同，无需写入值，保持绿色标记不做处理；
         * 2.判断若员工没有绿色标记
         * 将小指标分和最终总分以及业绩等级写回数据表中更新数据；
         */
        HrsfPmsuserBase updateBase;
        String changeFlag = hrsfPmsuserBase.getChangeFlag();
        if (CommonConstants.FLAG_Y.equals(changeFlag)) {
            BigDecimal pmsLevel = hrsfPmsuserBase.getPmsLevel();

            if (hrsfPmsuserBase.getTotalScore().compareTo(totalScoreFromExcel) == 0
                    && PmsLevelEnumNew.getDescByCode(pmsLevel).equals(data.getPmsLevelDesc())) {
                updateBase = new HrsfPmsuserBase();
                updateBase.setId(hrsfPmsuserBase.getId());
                updateBase.setChangeFlag(CommonConstants.FLAG_NO);
                /**
                 * 4.针对上传成功的员工，将分数来源字段标记为批量导入；
                 * 5.针对上传成功的员工，将表单状态变更为已评分；
                 */
                updateBase.setScoreSource(ScoreSourceEnum.IMPORT.getType());
                updateBase.setFormStatus(FormStatusEnum.Rated.getType());
                updateBase.setTarget1Score(BigDecimal.valueOf(data.getTarget1Score()));
                updateBase.setTarget2Score(BigDecimal.valueOf(data.getTarget2Score()));
                updateBase.setTarget3Score(BigDecimal.valueOf(data.getTarget3Score()));
                updateBase.setTarget4Score(BigDecimal.valueOf(data.getTarget4Score()));
                updateBase.setTarget5Score(BigDecimal.valueOf(data.getTarget5Score()));
                updateBase.setTarget6Score(BigDecimal.valueOf(data.getTarget6Score()));
                updateBase.setTarget7Score(BigDecimal.valueOf(data.getTarget7Score()));
                updateBase.setTarget8Score(BigDecimal.valueOf(data.getTarget8Score()));
                updateBase.setTarget9Score(BigDecimal.valueOf(data.getTarget9Score()));
                updateBase.setTarget10Score(BigDecimal.valueOf(data.getTarget10Score()));
                cachedDataList.add(updateBase);
            } else {
                log.info("不满足条件的员工号:{}", data.getStaffId());
                messageVoList.add(new UploadMessageSubVo(rowIndex.longValue(), data.getStaffId(), error6));
                return;
            }
        } else {
            updateBase = HrsfUpmsConvert.INSTANCE.toHrsfPmsuserBase(data, hrsfPmsuserBase);
            /**
             * 4.针对上传成功的员工，将分数来源字段标记为批量导入；
             * 5.针对上传成功的员工，将表单状态变更为已评分；
             */
            updateBase.setScoreSource(ScoreSourceEnum.IMPORT.getType());
            updateBase.setFormStatus(FormStatusEnum.Rated.getType());
            updateBase.setTotalScore(totalScoreFromExcel);
            cachedDataList.add(updateBase);
        }

        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        log.info("最后才执行一次！");
    }


    public static void main(String[] args) {
        Double a = 103.24;
        BigDecimal[] results = BigDecimal.valueOf(a).divideAndRemainder(BigDecimal.valueOf(5));
        System.out.println(results[0]);
        System.out.println(results[1]);

    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());
        hrsfPmsuserBaseService.saveOrUpdateBatch(cachedDataList);
        log.info("存储数据库成功！");
    }
}