package com.bbac.hrsf.admin.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bbac.hrsf.admin.api.entity.HrsfPmsdeptHead;
import com.bbac.hrsf.admin.event.PmsUpdateEvent;
import com.bbac.hrsf.admin.mapper.HrsfPmsdeptHeadMapper;
import com.bbac.hrsf.admin.mapper.SysUserRoleMapper;
import com.bbac.hrsf.admin.service.IHrsfPmsdeptHeadService;
import com.bbac.hrsf.common.core.constant.enums.PmsUpdateEventEnum;
import com.bbac.hrsf.common.core.constant.enums.RoleTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class HrsfPmsdeptHeadServiceImpl extends ServiceImpl<HrsfPmsdeptHeadMapper, HrsfPmsdeptHead> implements IHrsfPmsdeptHeadService {

    private final HrsfPmsdeptHeadMapper principalMapper;

    private final SysUserRoleMapper sysUserRoleMapper;

    private final ApplicationEventPublisher publisher;

    /**
     * 每天同步先清空机构负责人表
     */
    @Override
    public void delTable() {
        principalMapper.delTable();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean syncPrincipalInfo(List<HrsfPmsdeptHead> principalList) {
        log.debug("当前时间:{},数据量:{}条,获取到的机构负责人数据", LocalDate.now(), principalList.size());
        delTable();
        saveBatch(principalList);
        publisher.publishEvent(PmsUpdateEvent.of(PmsUpdateEventEnum.L));
        return true;
    }

    @Override
    public Set<String> selectDeptHeadList(String organId) {
        if (StrUtil.isNotBlank(organId)) {
            /**
             * 先从HRSF_DEPT_HEAD表中查询数据
             *
             */
            Set<String> deptHeadSet = list(Wrappers.<HrsfPmsdeptHead>lambdaQuery().eq(HrsfPmsdeptHead::getOrganId, organId)
                    .isNotNull(HrsfPmsdeptHead::getStaffId)).stream()
                    .map(HrsfPmsdeptHead::getStaffId).filter(data->StrUtil.isNotBlank(data)).collect(Collectors.toSet());
            if (CollectionUtil.isEmpty(deptHeadSet)) {
                return sysUserRoleMapper.selectByRoleCode(RoleTypeEnum.HR.getType());
            } else {
                return deptHeadSet;
            }
        }
        return new HashSet<>();
    }
}
