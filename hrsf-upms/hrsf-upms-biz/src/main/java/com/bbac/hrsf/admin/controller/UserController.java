/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.dto.CalibrationBaseAndIdDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserBaseFilterDTO;
import com.bbac.hrsf.admin.api.dto.HrsfUserFilterDTO;
import com.bbac.hrsf.admin.api.dto.UserInfo;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsdeptHead;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfOrganizationVO;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.admin.api.vo.ScoreCollectVO;
import com.bbac.hrsf.admin.api.vo.UploadMessageVo;
import com.bbac.hrsf.admin.api.vo.UserInfoVO;
import com.bbac.hrsf.admin.service.IHrsfOrganizationService;
import com.bbac.hrsf.admin.service.IHrsfPmsWriteDataBackLogService;
import com.bbac.hrsf.admin.service.IHrsfPmsdeptHeadService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseDownloadService;
import com.bbac.hrsf.admin.service.IHrsfPmsuserBaseService;
import com.bbac.hrsf.admin.service.IHrsfUserBaseService;
import com.bbac.hrsf.admin.service.SysFileService;
import com.bbac.hrsf.admin.service.SysUserService;
import com.bbac.hrsf.common.core.constant.CommonConstants;
import com.bbac.hrsf.common.core.constant.enums.CalibrationTemplateEnum;
import com.bbac.hrsf.common.core.constant.enums.FormStatusEnum;
import com.bbac.hrsf.common.core.constant.enums.LevelFlagEnum;
import com.bbac.hrsf.common.core.constant.enums.ProcessStepEnum;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.CollectionUtils;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.security.annotation.Inner;
import com.bbac.hrsf.common.security.service.HrsfLoginUser;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import com.bbac.hrsf.performance.api.dto.CalibrationListQueryDTO;
import com.bbac.hrsf.performance.api.dto.ExcelExportQueryDTO;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.bbac.hrsf.common.core.util.R.restResult;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/user")
@Api(value = "user", tags = "用户管理模块")
public class UserController {

    private final SysUserService userService;

    private final IHrsfPmsdeptHeadService HrsfPmsdeptHeadService;

    private final IHrsfOrganizationService hrsfOrganizationService;

    private final IHrsfUserBaseService hrsfUserBaseService;

    private final IHrsfPmsuserBaseService iHrsfPmsuserBaseService;

    private final IHrsfPmsWriteDataBackLogService iHrsfPmsWriteDataBackLogService;

    private final SysFileService sysFileService;

    private final IHrsfPmsdeptHeadService hrsfPmsdeptHeadService;

    private final IHrsfPmsuserBaseDownloadService hrsfPmsuserBaseDownloadService;

    private final long size = 1000;

    /**
     * 获取当前用户全部信息
     *
     * @return 用户信息
     */
    @GetMapping(value = {"/info"})
    public R<UserInfoVO> info() {
        /**
         * 这里需要改造一下,从员工基础表中获取数据
         */
        HrsfLoginUser loginUser = SecurityUtils.getUser();
        if(loginUser == null){
            return R.failed("获取当前用户信息失败1");
        }
        String username = loginUser.getUsername();
        HrsfUserBase hrsfUserBase = hrsfUserBaseService.getOne(Wrappers.<HrsfUserBase>query().lambda().eq(HrsfUserBase::getStaffId, username));
        if (hrsfUserBase == null) {
            return R.failed("获取当前用户信息失败2");
        }
        UserInfo userInfo = userService.getUserInfo(hrsfUserBase);
        UserInfoVO vo = new UserInfoVO();
        vo.setSysUser(userInfo.getSysUser());
        vo.setRoles(userInfo.getRoles());
        vo.setPermissions(userInfo.getPermissions());
        return R.ok(vo);
    }

    /**
     * 获取指定用户全部信息
     *
     * @return 用户信息
     */
    @Inner
    @GetMapping("/info/{username}")
    public R<UserInfo> info(@PathVariable String username) {
        /**
         * 这里需要改造一下,从员工基础表中获取数据
         */
        HrsfUserBase hrsfUserBase = hrsfUserBaseService.getOne(Wrappers.<HrsfUserBase>query().lambda().eq(HrsfUserBase::getStaffId, username));
        if (hrsfUserBase == null) {
            return R.failed(String.format("用户信息为空 %s", username));
        }
        return R.ok(userService.getUserInfo(hrsfUserBase));
    }

    /**
     * 导出excel 表格
     * 蓝领分数导入模板下载
     *
     * @param
     * @return
     */
    @PostMapping("/export")
    public void export(@RequestBody ExcelExportQueryDTO excelExportQueryDTO, HttpServletResponse response) {
        iHrsfPmsuserBaseService.export(excelExportQueryDTO.getCalibrationId(),
                excelExportQueryDTO.getAssessType(), excelExportQueryDTO.getCalibrationName(), response);
    }

    /**
     * 上传excel表格
     *
     * @param
     * @return
     */
    @PostMapping("/upload/{calibrationId}")
    public R<UploadMessageVo> upload(@PathVariable Long calibrationId, @RequestParam String assessType, MultipartFile file) {
        try {
            return R.ok(iHrsfPmsuserBaseService.upload(calibrationId, assessType, file));
        } catch (Exception e) {
            return restResult(null, CommonConstants.EXPORT_VERIFY_ERROR, null);
        }
    }

    /**
     * 上传文件 文件名采用uuid,避免原始文件名中带"-"符号导致下载的时候解析出现异常
     *
     * @param file 资源
     * @return R(/ admin / bucketName / filename)
     */
    @PostMapping(value = "/uploadAttachment/{calibrationBaseId}")
    public R uploadAttachment(MultipartFile file, @PathVariable Long calibrationBaseId) {
        HrsfLoginUser user = SecurityUtils.getUser();
        if(user != null){
            return sysFileService.uploadFile(file, calibrationBaseId);
        }
        return R.failed(null,"无操作权限");
    }

    /**
     * 同步机构负责人信息
     *
     * @return R
     */
    @PostMapping("/syncInfo")
    public R syncPrincipalInfo(@RequestBody List<HrsfPmsdeptHead> principalList) {
        return R.ok(HrsfPmsdeptHeadService.syncPrincipalInfo(principalList));
    }

    /**
     * 权限设置功能--获取筛选器接口
     *
     * @return R
     */
    @GetMapping("/getSelectInfo")
    public R syncPrincipalInfo() {
        return R.ok(hrsfUserBaseService.getCalibrationPmsSelect());
    }

    /**
     * 分页获取用户列表接口
     *
     * @return R
     */
    @GetMapping("/pageInfo")
    public R<IPage<HrsfUserVO>> getUserPageInfo(Page page, HrsfUserBaseFilterDTO hrsfUserDTO) {
        return R.ok(hrsfUserBaseService.getUserPageInfo(page, hrsfUserDTO));
    }

    /**
     * 获取用户列表接口
     * (针对需求定制化开发接口,进用于绩效协调员查询绑定用户)
     *
     * @return R
     */
    @PostMapping("/getUserInfoList")
    public R<Set<HrsfUserVO>> getUserInfoList(@RequestBody List<HrsfUserFilterDTO> dtoList) {
        Set<HrsfUserVO> userInfoList = new HashSet<>();
        dtoList.stream().forEach(hrsfUserDTO -> userInfoList.addAll(hrsfUserBaseService.getUserInfoList(hrsfUserDTO)));
        return R.ok(userInfoList);
    }

    /**
     * 新增绩效考核员工基础表
     *
     * @return R
     */
    @PostMapping("/startPmsUser")
    @Inner
    public R<Boolean> startPmsUser(@RequestBody HrsfPmstart hrsfPmstart) {
        return R.ok(iHrsfPmsuserBaseService.startPmsUser(hrsfPmstart, null));
    }

    /**
     * 同步员工基础表数据
     *
     * @return R
     */
    @PostMapping("/syncPmsUser")
    @Inner
    public R<Boolean> syncPmsUser() {
        /**
         * 这里的current入参是从0开始，是SF平台接口的限制
         */
        int i = 0;
        Boolean sync = true;
        while (sync) {
            sync = hrsfUserBaseService.sync(i * size, size, "0", null);
            i++;
        }
        return R.ok();
    }

    /**
     *
     */
    @PostMapping("/getPmsUserList")
    @Inner
    public R<List<HrsfPmsuserBase>> getPmsUserList(@RequestParam("assesYear") String assesYear, @RequestParam("assesType") String assesType) {
        return R.ok(iHrsfPmsuserBaseService.getPmsUserList(assesYear, assesType));
    }

    /**
     *
     */
    @PostMapping("/getTotalNumByCalibrationBaseId")
    @Inner
    public Long getTotalNumByCalibrationBaseId(@RequestParam("calibrationBaseId") Long calibrationBaseId) {
        if (calibrationBaseId != null) {
            return iHrsfPmsuserBaseService.count(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId, calibrationBaseId));
        } else {
            return null;
        }
    }

    @PostMapping("/updateCalibrationBaseId")
    @Inner
    public R<Boolean> updateCalibrationBaseId(@RequestParam(value = "id", required = false) Long id, @RequestParam("assesType") String assesType,
                                              @RequestParam("assesYear") String assesYear, @RequestBody Set<String> staffIds) {
        return R.ok(iHrsfPmsuserBaseService.updateCalibrationBaseId(id, assesType, assesYear, staffIds));
    }

    /**
     * 查看校准会议收件箱详情-用户列表视图
     *
     * @return R
     */
    @PostMapping("/getCalibrationListById")
    public R<List<HrsfPmsuserBase>> getCalibrationListById(@RequestBody CalibrationListQueryDTO queryDTO) {
        Boolean isVerify = false;
        if (StrUtil.isNotBlank(queryDTO.getSign()) && CommonConstants.MEETING_INBOX.equals(queryDTO.getSign())) {
            isVerify = true;
        }
        try {
            return R.ok(iHrsfPmsuserBaseService.getCalibrationListById(queryDTO, isVerify));
        } catch (CheckedException e) {
            return R.restResult(null, CommonConstants.NO_PERMISSION, "该用户没有权限查看数据,请联系管理员");
        }
    }

    /**
     * 查看校准会议收件箱详情-等级视图
     *
     * @return R
     */
    @PostMapping("/getCalibrationPmsById")
    public R<Map<String, Object>> getCalibrationPmsById(@RequestBody CalibrationListQueryDTO queryDTO) {
        Boolean isVerify = false;
        if (StrUtil.isNotBlank(queryDTO.getSign()) && CommonConstants.MEETING_INBOX.equals(queryDTO.getSign())) {
            isVerify = true;
        }
        try {
            return R.ok(iHrsfPmsuserBaseService.getCalibrationPmsById(queryDTO, isVerify));
        } catch (CheckedException e) {
            return R.restResult(null, CommonConstants.NO_PERMISSION, "该用户没有权限查看数据,请联系管理员");
        }
    }

    /**
     * 查看校准会议收件箱详情-潜力等级视图
     *
     * @return R
     */
    @PostMapping("/getCalibrationPotencyById")
    public R<Map<String, Object>> getCalibrationPotencyById(@RequestBody CalibrationListQueryDTO queryDTO) {
        Boolean isVerify = false;
        if (StrUtil.isNotBlank(queryDTO.getSign()) && CommonConstants.MEETING_INBOX.equals(queryDTO.getSign())) {
            isVerify = true;
        }
        try {
            return R.ok(iHrsfPmsuserBaseService.getCalibrationPotencyById(queryDTO, isVerify));
        } catch (CheckedException e) {
            return R.restResult(null, CommonConstants.NO_PERMISSION, "该用户没有权限查看数据,请联系管理员");
        }
    }

    /**
     * 获取校准会议收件箱详情-筛选器接口
     *
     * @return R
     */
    @GetMapping("/getCalibrationPmsSelect/{id}")
    public R<HrsfOrganizationVO> getCalibrationPmsSelect(@PathVariable Long id) {
        return R.ok(iHrsfPmsuserBaseService.getCalibrationPmsSelect(id));
    }

    /**
     * 获取校准会议总览收件箱详情-筛选器接口
     *
     * @return R
     */
    @PostMapping("/getCalibrationPmsSelectByYearAndType")
    public R<HrsfOrganizationVO> getCalibrationPmsSelectByYearAndType(@RequestParam("assesYear") String assesYear, @RequestParam("assesType") String assesType) {
        return R.ok(iHrsfPmsuserBaseService.getCalibrationPmsSelectByYearAndType(assesYear, assesType));
    }

    /**
     * 获取校准会议收件箱详情-筛选器接口(根据员工名称获取员工,从基础表中获取指定校准会议中)
     *
     * @return R
     */
    @PostMapping("/getCalibrationPmsSelectNameByYearAndType")
    public R<List<HrsfUserVO>> getCalibrationPmsSelectNameByYearAndType(@RequestParam("assesYear") String assesYear, @RequestParam("assesType") String assesType,
                                                                        @RequestParam("fullName") String fullName) {
        return R.ok(iHrsfPmsuserBaseService.getCalibrationPmsSelectNameByYearAndType(assesYear, assesType, fullName));
    }

    /**
     * 获取校准会议收件箱详情-筛选器接口(根据员工名称获取员工,从基础表中获取指定校准会议中)
     *
     * @return R
     */
    @GetMapping("/getCalibrationPmsSelectName/{id}")
    public R<List<HrsfUserVO>> getCalibrationPmsSelectName(@PathVariable Long id, @RequestParam String fullName) {
        return R.ok(iHrsfPmsuserBaseService.getCalibrationPmsSelectName(id, fullName));
    }

    /**
     * 获取校准会议收件箱详情-获取评分信息汇总
     *
     * @return R
     */
    @PostMapping("/getCalibrationScoreCollect")
    public R<ScoreCollectVO> getCalibrationScoreCollect(@RequestBody CalibrationListQueryDTO queryDTO) {
        Boolean isVerify = false;
        if (StrUtil.isNotBlank(queryDTO.getSign()) && CommonConstants.MEETING_INBOX.equals(queryDTO.getSign())) {
            isVerify = true;
        }
        try {
            return R.ok(iHrsfPmsuserBaseService.getCalibrationScoreCollect(queryDTO, isVerify));
        } catch (CheckedException e) {
            return R.restResult(null, CommonConstants.NO_PERMISSION, "该用户没有权限查看数据,请联系管理员");
        }
    }

    /**
     * 获取筛选器接口(根据员工名称获取员工,从主数据表中获取)
     *
     * @return R
     */
    @GetMapping("/getUserBaseSelectName")
    public R<List<HrsfUserVO>> getUserBaseSelectName(@RequestParam(value = "fullName", required = false) String fullName, @RequestParam(value = "staffId", required = false) String staffId) {
        if (StrUtil.isNotBlank(staffId)) {
            return R.ok(hrsfUserBaseService.getUserBaseSelectByStaffId(staffId));
        } else {
            return R.ok(hrsfUserBaseService.getUserBaseSelectName(fullName));
        }
    }

    /**
     * 从SF系统同步最终得分、业绩等级数据
     *
     * @return R
     */
    @PostMapping("/syncScoreBySF")
    @Inner
    R<ErrorMessageVo> syncScoreBySF(@RequestBody CalibrationBaseAndIdDTO calibrationBaseAndIdDTO) {
        return R.ok(iHrsfPmsuserBaseService.syncScoreBySF(calibrationBaseAndIdDTO.getUserBaseIdList(), calibrationBaseAndIdDTO.getCalibrationBase()));
    }

    /**
     * 从SF系统退回到表单
     *
     * @return R
     */
    @PostMapping("/returnCalibration")
    @Inner
    R<ErrorMessageVo> returnCalibration(@RequestBody List<Long> userBaseIdList) {
        return iHrsfPmsuserBaseService.returnCalibration(userBaseIdList);
    }

    /**
     * 根据员工ID获取主数据表中的员工信息
     *
     * @param staffIdList
     * @return
     */
    @PostMapping("/getUserInfoByStaffId")
    @Inner
    R<List<HrsfUserVO>> getUserInfoByStaffId(@RequestBody List<String> staffIdList) {
        return R.ok(hrsfUserBaseService.getUserInfoByStaffId(staffIdList));
    }

    /**
     * 根据角色ID获取主数据表中的员工工号
     * selectByRoleCode
     *
     * @return
     */
    @GetMapping("/selectByRoleCode")
    @Inner
    R<Set<String>> selectByRoleCode(@RequestParam("roleCode") Long roleCode) {
        return R.ok(hrsfUserBaseService.selectByRoleCode(roleCode));
    }

    /**
     * 根据员工ID获取主数据表中的员工信息
     * selectByRoleCode
     *
     * @return
     */
    @PostMapping("/selectByStaffIdList")
    @Inner
    R<Set<String>> selectByStaffIdList(@RequestBody Set<String> staffIdList) {
        return R.ok(hrsfUserBaseService.selectByStaffIdList(staffIdList));
    }

    @PostMapping("/selectEmailAddressByStaffIdList")
    @Inner
    R<List<HrsfUserBase>> selectEmailAddressByStaffIdList(@RequestBody Set<String> staffIdList) {
        return R.ok(hrsfUserBaseService.selectEmailAddressByStaffIdList(staffIdList));
    }

    /**
     * 根据员工ID获取机构负责人的员工信息
     * selectByRoleCode
     *
     * @return
     */
    @PostMapping("/selectDeptHeadList")
    @Inner
    R<Set<String>> selectDeptHeadList(@RequestParam(value = "organId", required = false) String organId) {
        return R.ok(hrsfPmsdeptHeadService.selectDeptHeadList(organId));
    }

    @PostMapping("/selectByBaseId")
    @Inner
    R<List<HrsfPmsuserBase>> selectByBaseId(@RequestParam("baseId") Long baseId) {
        return R.ok(iHrsfPmsuserBaseService.selectByBaseId(baseId));
    }

    @PostMapping("/selectErrorInfoByTaskIds")
    @Inner
    R<Boolean> selectErrorInfoByTaskIds(@RequestParam("taskId") List<String> taskId) {
        return R.ok(iHrsfPmsWriteDataBackLogService.selectErrorInfoByTaskIds(taskId));
    }

    @PostMapping("/selectByStaffIdAndASSESS")
    @Inner
    R<List<HrsfPmsuserBase>> selectByStaffIdAndASSESS(@RequestBody List<String> staffIds, @RequestParam("assesYear") String assesYear, @RequestParam("assesType") String assesType) {
        return R.ok(iHrsfPmsuserBaseService.selectByStaffIdAndASSESS(staffIds, assesYear, assesType));
    }


    /**
     * 蓝白季度，蓝年数据回写
     */
    @PostMapping("/writeDataBack")
    @Inner
    public R<Boolean> writeDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS) {
        return R.ok(iHrsfPmsuserBaseService.writeDataBack(writeDataBackDTOS));
    }

    /**
     * 非白领年度数据回写
     * 数据回写逻辑：HR L3审批完成后，针对被审批的校准会议中的所有员工，需要进行两部分回写工作：
     */
    @PostMapping("/finalWriteDataBack")
    @Inner
    public R<Boolean> finalWriteDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS) {
        return R.ok(iHrsfPmsuserBaseService.finalWriteDataBack(writeDataBackDTOS));
    }


    /**
     * 白年
     * 5.0	机构负责人校准结束后，数据回写
     */
    @PostMapping("white/year/WriteDataBack")
    @Inner
    public R<Boolean> whiteYearWriteDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS) {
        return R.ok(iHrsfPmsuserBaseService.whiteYearWriteDataBack(writeDataBackDTOS));
    }

    /**
     * 白年
     * 5.1	HR L3审批结束后，数据回写
     */
    @PostMapping("white/year/FinalWriteDataBack")
    @Inner
    public R<Boolean> whiteYearFinalWriteDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS) {
        return R.ok(iHrsfPmsuserBaseService.whiteYearFinalWriteDataBack(writeDataBackDTOS));
    }


    /**
     * 机构负责人-点击保存按钮
     * <p>
     * (业绩视图和潜力等级视图通用)
     *
     * @return
     */
    @PostMapping("/updateHrsfPmsUserInfo/{calibrationBaseId}")
    public R<Boolean> updateHrsfPmsUserInfo(@RequestBody Map<String, List<HrsfPmsuserBase>> dataMap, @PathVariable Long calibrationBaseId) {
        return R.ok(iHrsfPmsuserBaseService.updateHrsfPmsUserInfo(dataMap, calibrationBaseId));
    }

    @PostMapping("/saveCompanyLevelCalibration")
    @Inner
    public R<Object> saveCompanyLevelCalibration(@RequestBody List<HrsfPmsuserBase> pmsUserList) {
        //失败员工姓名List
        ArrayList<String> failNameList = new ArrayList<>();
        try {
            iHrsfPmsuserBaseService.saveCompanyLevelCalibration(pmsUserList, failNameList);
        } catch (Exception e) {
            String join = StringUtils.join(failNameList, ",");
            return R.failed(StrUtil.format("员工【{}】在当前校准会议或其他校准会议中存在，请先将员工从校准会议中移除，再进行添加操作！", join));
        }
        return R.ok();
    }

    @PostMapping("/removeCalibrationBaseId")
    @Inner
    public R<Object> removeCalibrationBaseId(@RequestParam("id") Long id) {
        return R.ok(iHrsfPmsuserBaseService.removeCalibrationBaseId(id));
    }

    @PostMapping("/batchUpdateByCalibrationBaseId")
    @Inner
    public R<Object> batchUpdateByCalibrationBaseId(@RequestParam("baseId") Long baseId) {
        return R.ok(iHrsfPmsuserBaseService.batchUpdateByCalibrationBaseId(baseId));
    }

    @PostMapping("/removeCalibrationUser")
    @Inner
    public R<Object> removeCalibrationUser(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(iHrsfPmsuserBaseService.removeCalibrationUser(addCalibrationUserDTO));
    }


    /**
     * 下载excel(总览页面)
     * @param addCalibrationUserDTO
     * @param response
     * @throws Exception
     */
    @PostMapping(value = "/report/overview/excel")
    @PreAuthorize("@pmsRole.hasPermission()")
    public void reportOverviewExcel(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception {
        hrsfPmsuserBaseDownloadService.reportOverviewExcel(addCalibrationUserDTO, response);
    }

    /**
     * 下载excel
     * @param addCalibrationUserDTO
     * @param response
     * @throws Exception
     */
    @PostMapping(value = "/report/calibration/excel")
    public void reportCalibrationExcel(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception {
        hrsfPmsuserBaseDownloadService.reportCalibrationExcel(addCalibrationUserDTO, response);
    }
    /***
     * 下载ppt
     * @param addCalibrationUserDTO
     * @param response
     */
    @PostMapping(value = "/report/download/ppt")
    public void reportDownloadPpt(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) {
        hrsfPmsuserBaseDownloadService.reportDownloadPpt(addCalibrationUserDTO, response);
    }
    /***
     * 下载ppt(校准会议总览页面)
     * @param addCalibrationUserDTO
     * @param response
     */
    @PostMapping(value = "/overview/report/download/ppt")
    @PreAuthorize("@pmsRole.hasPermission()")
    public void overviewReportDownloadPpt(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) {
        hrsfPmsuserBaseDownloadService.reportDownloadPpt(addCalibrationUserDTO, response);
    }

    /**
     * 下载excel(校准会议总览页面)
     * @param addCalibrationUserDTO
     * @param response
     * @throws Exception
     */
    @PostMapping(value = "/overview/report/calibration/excel")
    @PreAuthorize("@pmsRole.hasPermission()")
    public void overViewReportCalibrationExcel(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, HttpServletResponse response) throws Exception {
        hrsfPmsuserBaseDownloadService.reportCalibrationExcel(addCalibrationUserDTO, response);
    }

    /**
     * 校准会议总览页面
     * 新增按钮下的
     * 列表视图接口
     * @param addCalibrationUserDTO
     * @return
     */
    @PostMapping("/getPmsUserListByOverView")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<HrsfPmsuserBase>> getPmsUserListByOverView(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(iHrsfPmsuserBaseService.getPmsUserListByOverView(addCalibrationUserDTO));
    }

    /**
     * 总览-获取分页数据
     *
     * @return R
     */
    @PostMapping("/getOverviewPage")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<HrsfPmsuserBase>> getOverviewPage(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(iHrsfPmsuserBaseService.getOverviewPage(addCalibrationUserDTO));
    }

    @PostMapping("/selectPmsUserBaseByStaffIdList")
    @Inner
    public R<List<HrsfPmsuserBase>> selectPmsUserBaseByStaffIdList(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO) {
        return R.ok(iHrsfPmsuserBaseService.selectPmsUserBaseByStaffIdList(addCalibrationUserDTO.getStaffIds(), addCalibrationUserDTO.getAssesYear(), addCalibrationUserDTO.getAssesType()));
    }

    @GetMapping("/updateFormStatusByBaseId")
    @Inner
    public void updateFormStatusByBaseId(@RequestParam(value = "formStatus", required = false) String formStatus
            , @RequestParam(value = "processStep", required = false) String processStep,
                                         @RequestParam(value = "template", required = false) String template, @RequestParam("baseId") Long baseId) {

        /**
         * 根据processStep和template是否为空
         * 来判断是提交还是退回操作
         */

        /**
         * 如果状态返回是BACK_SPECIAL
         * 则表示是白领年度退回操作
         * 需要特殊处理逻辑
         */

        if (CommonConstants.BACK_SPECIAL.equals(formStatus)) {
            List<HrsfPmsuserBase> userBaseList = iHrsfPmsuserBaseService.list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId, baseId));
            //未被标记的ID
            List<Long> noChangeList = userBaseList.stream()
                    /*
                     fix bug问题清单248 ---  update by 2022-07-11
                    .filter(data -> !data.getAdjusted() && !data.getPAdjusted())
                    */
                    .map(HrsfPmsuserBase::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(noChangeList)) {
                iHrsfPmsuserBaseService.update(Wrappers.<HrsfPmsuserBase>lambdaUpdate().set(HrsfPmsuserBase::getFormStatus,
                        FormStatusEnum.Rated.getType()).in(HrsfPmsuserBase::getId, noChangeList));
            }
            /**
             *
             * fix bug问题清单247---  update by 2022-07-11
             *
             *
             * iHrsfPmsuserBaseService.update(Wrappers.<HrsfPmsuserBase>lambdaUpdate()
             *                     .set(HrsfPmsuserBase::getAdjusted, false).set(HrsfPmsuserBase::getPAdjusted, false).eq(HrsfPmsuserBase::getCalibrationBaseId, baseId));
             */

        } else {
            if (baseId != null && StrUtil.isNotBlank(formStatus)) {
                iHrsfPmsuserBaseService.update(Wrappers.<HrsfPmsuserBase>lambdaUpdate().set(HrsfPmsuserBase::getFormStatus,
                        formStatus).set(HrsfPmsuserBase::getAdjusted, false).set(HrsfPmsuserBase::getPAdjusted, false).eq(HrsfPmsuserBase::getCalibrationBaseId, baseId));
            }
        }
        /**
         * 2022/06/29 这里新增逻辑
         * 如果是HRBP节点或者公司级校准会议HR_MANAGER节点
         * 提交时需要更新字段TotalScoreOriginal
         */
        if (ProcessStepEnum.HRBP.getType().equals(processStep) ||
                (CalibrationTemplateEnum.THREE.getType().equals(template) && ProcessStepEnum.HR_MANAGER.getType().equals(processStep))) {
            iHrsfPmsuserBaseService.updateTotalScoreOriginal(baseId);
        }

        /**
         * fixbug问题清单338
         * 2022-07-26
         * 这里要特殊处理,如果是公司级校准会议审批完成后
         * 要把在原来的校准会议的分数、等级同步变更
         */
        if (FormStatusEnum.Completed.getType().equals(formStatus) && CalibrationTemplateEnum.THREE.getType().equals(template)) {
            /**
             * 1、根据baseID查出公司级校准会议的分数、等级
             * 2、然后遍历更新到原来的校准会议中去
             */
            List<HrsfPmsuserBase> userBaseList = iHrsfPmsuserBaseService.list(Wrappers.<HrsfPmsuserBase>lambdaQuery().eq(HrsfPmsuserBase::getCalibrationBaseId, baseId));
            userBaseList.stream().forEach(data -> {
                iHrsfPmsuserBaseService.update(Wrappers.<HrsfPmsuserBase>lambdaUpdate()
                        .set(HrsfPmsuserBase::getTotalScore, data.getTotalScore())
                        .set(HrsfPmsuserBase::getPmsLevel, data.getPmsLevel())
                        .set(HrsfPmsuserBase::getPmsLevelDesc, data.getPmsLevelDesc())
                        .set(HrsfPmsuserBase::getPotentialLevel, data.getPotentialLevel())
                        .set(HrsfPmsuserBase::getPotentialLevelDesc, data.getPotentialLevelDesc())
                        .set(HrsfPmsuserBase::getVariable, data.getVariable())
                        .set(HrsfPmsuserBase::getVariableDesc, data.getVariableDesc())
                        .eq(HrsfPmsuserBase::getStaffId, data.getStaffId())
                        .eq(HrsfPmsuserBase::getAssessYear, data.getAssessYear())
                        .eq(HrsfPmsuserBase::getAssessType, data.getAssessType())
                        .eq(HrsfPmsuserBase::getLevelFlag, LevelFlagEnum.IS_CHECK.getType())
                );
            });

        }

    }

    /**
     * 6、容错补偿机制
     *
     * @param
     * @return
     */
    @PostMapping("/compensator")
    public R<Boolean> compensator(@RequestParam("assessYear") String assessYear, @RequestParam("assessType") String assessType, @RequestParam("calibrationBaseId") Long calibrationBaseId) {
        return R.ok(iHrsfPmsuserBaseService.compensator(assessYear, assessType, calibrationBaseId));
    }

    @PostMapping("white/year/FinalWriteDataBack2")
    public R<Boolean> whiteYearFinalWriteDataBack2() {
        hrsfOrganizationService.syncFinalWriteDataBackErrorHandler();
        return R.ok(true);
    }

    @PostMapping("/syncPmsJobHandlerAgain")
    public R<Boolean> syncPmsJobHandlerAgain() {
        hrsfOrganizationService.syncPmsJobHandlerAgain();
        return R.ok(true);
    }
}
