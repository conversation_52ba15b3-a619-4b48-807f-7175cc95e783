package com.bbac.hrsf.admin.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserScoreTaskQueryDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserScoreTask;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;

import java.util.List;

/**
 * <p>
 * 历史绩效分数任务
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface IHrsfUserScoreTaskService extends IService<HrsfPmsUserScoreTask> {

    /**
     * 启动历史绩效分数任务
     * @param currTaskId 当前任务ID
     * @return message
     */
    String startUserScoreTask(Long currTaskId);

    /**
     * 检查历史绩效分数任务
     */
    void checkUserScoreTask();

    /**
     * 获取任务状态
     * @return 获取任务的状态和时间
     */
    HrsfPmsUserScoreTask getCurrentTaskStatus();

    /**
     * 查询失败的任务明细情况
     * @param taskId 查询条件
     * @return 任务明细列表
     */
    List<HrsfPmsUserScoreTaskDetailVo> queryFailedTaskDetailList(String taskId);

    /**
     * 分页查询失败的任务明细情况
     * @param queryDTO 查询条件
     * @return 任务明细列表
     */
    IPage<HrsfPmsUserScoreTaskDetailVo> queryTaskDetailList(HrsfPmsUserScoreTaskQueryDTO queryDTO);
}
