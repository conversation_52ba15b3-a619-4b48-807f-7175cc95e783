/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.dto.*;
import com.bbac.hrsf.admin.api.entity.*;
import com.bbac.hrsf.admin.api.vo.*;
import com.bbac.hrsf.admin.mapper.HrsfPmsUserRelationMapper;
import com.bbac.hrsf.admin.service.*;
import com.bbac.hrsf.common.core.exception.CheckedException;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.log.annotation.SysLog;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/role")
@Api(value = "role", tags = "角色管理模块")
public class RoleController {

    private final SysRoleService sysRoleService;

    private final SysRoleMenuService sysRoleMenuService;

    private final SysUserRoleService sysUserRoleService;

    private final IHrsfPmsUserService hrsfPmsUserService;

    private final IHrsfUserBaseService hrsfUserBaseService;

    private final HrsfPmsUserRelationMapper hrsfPmsUserRelationMapper;

    /**
     * 通过ID查询角色信息
     *
     * @param id ID
     * @return 角色信息
     */
    @GetMapping("/{id:\\d+}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<SysRole> getById(@PathVariable Long id) {
        return R.ok(sysRoleService.getById(id));
    }

    /**
     * 添加角色
     *
     * @param sysRole 角色信息
     * @return success、false
     */
    @SysLog("添加角色")
    @PostMapping
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> save(@Valid @RequestBody SysRole sysRole) {
        try {
            //根据员工号去匹配员工姓名
            SysRole querySysRole = sysRoleService.getOne(Wrappers.<SysRole>query().lambda().eq(SysRole::getRoleName, sysRole.getRoleName()));
            if(querySysRole != null){
                return R.failed("当前角色名已存在，不能重复添加");
            }
            sysRoleService.save(sysRole);
        } catch (Exception e) {
            throw new CheckedException("当前角色英文标识已存在");
        }
        return R.ok(true);
    }

    /**
     * 修改角色
     *
     * @param sysRole 角色信息
     * @return success/false
     */
    @SysLog("修改角色")
    @PutMapping
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> update(@Valid @RequestBody SysRole sysRole) {
        return R.ok(sysRoleService.updateById(sysRole));
    }

    /**
     * 删除角色
     *
     * @param id
     * @return
     */
    @SysLog("删除角色")
    @DeleteMapping("/{id:\\d+}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> removeById(@PathVariable Long id) {
        return R.ok(sysRoleService.removeRoleById(id));
    }

    /**
     * 获取角色列表
     *
     * @return 角色列表
     */
    @GetMapping("/list")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<List<SysRole>> listRoles() {
        return R.ok(sysRoleService.list(Wrappers.emptyWrapper()));
    }

    /**
     * 分页查询角色信息
     *
     * @param page 分页对象
     * @return 分页对象
     */
    @GetMapping("/page")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<SysRole>> getRolePage(Page page, SysRoleDTO sysRoleDTO) {
        return R.ok(sysRoleService.getUserPageInfo(page, sysRoleDTO));
    }

    /**
     * 更新角色菜单
     *
     * @param roleVo 角色对象
     * @return success、false
     */
    @PutMapping("/menu")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> saveRoleMenus(@RequestBody RoleVo roleVo) {
        SysRole sysRole = sysRoleService.getById(roleVo.getRoleId());
        return R.ok(sysRoleMenuService.saveRoleMenus(roleVo.getRoleId(), roleVo.getMenuIds()));
    }

    /**
     * 角色管理-关联用户
     *
     * @param hrsfRoleUserDTO 角色对象
     * @return success、false
     */
    @PostMapping("/saveRoleUserInfo")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> saveRoleUserInfo(@RequestBody HrsfRoleUserDTO hrsfRoleUserDTO) {
        return R.ok(sysUserRoleService.saveRoleUserInfo(hrsfRoleUserDTO));
    }

    /**
     * 角色管理-根据角色ID获取关联用户数据
     *
     * @param roleId 角色对象
     * @return success、false
     */
    @GetMapping("/getRoleUserInfo/{roleId}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<List<HrsfUserVO>> getRoleUserInfo(@PathVariable Long roleId, String fullName) {
        return R.ok(sysUserRoleService.getRoleUserInfo(roleId, fullName));
    }

    /**
     * 角色管理-删除角色绑定的对象信息
     *
     * @param roleId 角色对象
     * @return success、false
     */
    @DeleteMapping("/deleteRoleUserInfo/{roleId}/{userId}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> deleteRoleUserInfo(@PathVariable Long roleId, @PathVariable String userId) {
        return R.ok(sysUserRoleService.deleteRoleUserInfo(roleId, userId));
    }

    /**
     * 添加绩效协调员角色
     *
     * @param hrsfPmsUser 添加绩效协调员角色
     * @return success、false
     */
    @PostMapping("/savePmsUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> savePmsUser(@Valid @RequestBody HrsfPmsUser hrsfPmsUser) {
        //根据员工号去匹配员工姓名
        HrsfPmsUser queryHrsfPmsUser = hrsfPmsUserService.getOne(Wrappers.<HrsfPmsUser>query().lambda().eq(HrsfPmsUser::getPmsStaff, hrsfPmsUser.getPmsStaff()));
        if(queryHrsfPmsUser != null){
            return R.failed("当前绩效协调员信息已存在，不能重复添加");
        }
        HrsfUserBase userBase = hrsfUserBaseService.getById(hrsfPmsUser.getPmsStaff());
        hrsfPmsUser.setPmsStaffName(userBase == null ? null : userBase.getFullName());
        return R.ok(hrsfPmsUserService.save(hrsfPmsUser));
    }


    /**
     * 修改绩效协调员角色
     *
     * @param updateDTO 角色信息
     * @return success/false
     */
    @PutMapping("/savePmsUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> updatePmsUser(@Valid @RequestBody HrsfPmsUserUpdateDTO updateDTO) {
        return R.ok(hrsfPmsUserService.updatePmsUser(updateDTO));
    }

    /**
     * 角色管理-获取绩效协调员角色信息
     *
     * @param pmsUserId 角色对象
     * @return success、false
     */
    @GetMapping("/getPmsUserInfo/{pmsUserId}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<HrsfPmsUserVO> getPmsUserInfo(@PathVariable Long pmsUserId) {
        return R.ok(hrsfPmsUserService.getPmsUserInfo(pmsUserId));
    }

    /**
     * 分页绩效协调员角色信息
     *
     * @param page 分页对象
     * @return 分页对象
     */
    @GetMapping("/pagePmsUser")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<IPage<HrsfPmsUser>> getPmsUserPage(Page page, HrsfPmsUserDTO sysRoleDTO) {
        return R.ok(hrsfPmsUserService.getUserPageInfo(page, sysRoleDTO));
    }

    /**
     * 删除绩效协调员角色
     *
     * @param id
     * @return
     */
    @DeleteMapping("/pmsUser/{id:\\d+}")
    @PreAuthorize("@pmsRole.hasPermission()")
    public R<Boolean> removePmsUserById(@PathVariable Long id) {
         HrsfPmsUser hrsfPmsUser = hrsfPmsUserService.getById(id);
        hrsfPmsUserService.removeById(id);
        /**
         * 同时删除HRSF_PMS_USER_RELATION表数据
         */
        if(hrsfPmsUser!=null && hrsfPmsUser.getPmsStaff()!=null){
            hrsfPmsUserRelationMapper.deleteByPmsStaffId(hrsfPmsUser.getPmsStaff());
            /**
             * 2022/10/09
             *
             */
            hrsfPmsUserRelationMapper.deleteByPmsStaffIdFormInfo(hrsfPmsUser.getPmsStaff());
        }
        /**
         * 同时删除HRSF_PMS_USER_RELATION表数据
         */
        return R.ok();
    }
}
