/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the bbac.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.bbac.hrsf.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bbac.hrsf.admin.api.entity.SysFile;
import com.bbac.hrsf.admin.service.SysFileService;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.common.log.annotation.SysLog;
import com.bbac.hrsf.common.security.annotation.Inner;
import com.bbac.hrsf.common.security.service.HrsfLoginUser;
import com.bbac.hrsf.common.security.util.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 文件管理
 *
 * <AUTHOR>
 * @date 2021-09-11
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/sys-file")
@Api(value = "sys-file", tags = "文件管理")
public class FileController {

    private final SysFileService sysFileService;

    /**
     * 分页查询
     *
     * @param page    分页对象
     * @param sysFile 文件管理
     * @return
     */
    @ApiOperation(value = "分页查询", notes = "分页查询")
    @GetMapping("/page")
    public R<IPage<SysFile>> getSysFilePage(Page page, SysFile sysFile) {
        return R.ok(sysFileService.page(page, Wrappers.query(sysFile)));
    }

    /**
     * 文件列表查询
     *
     * @return
     */
    @ApiOperation(value = "文件列表查询", notes = "文件列表查询")
    @GetMapping("/list/{calibrationBaseId}")
    public R<List<SysFile>> getSysFile(@PathVariable Long calibrationBaseId) {
        HrsfLoginUser user = SecurityUtils.getUser();
        if(user != null){
            return R.ok(sysFileService.list(Wrappers.<SysFile>lambdaQuery()
                    .eq(SysFile::getCalibrationBaseId, calibrationBaseId)));
        }
        return R.failed("无操作权限");
    }

    /**
     * 通过id删除文件管理
     *
     * @param id id
     * @return R
     */
    @ApiOperation(value = "通过id删除文件管理", notes = "通过id删除文件管理")
    @SysLog("删除文件管理")
    @DeleteMapping("/{id:\\d+}")
    public R<Boolean> removeById(@PathVariable Long id) {
        HrsfLoginUser user = SecurityUtils.getUser();
        if(user != null){
            return R.ok(sysFileService.deleteFile(id));
        }
        return R.failed("无操作权限");
    }

    /**
     * 获取文件
     *
     * @param bucket   桶名称
     * @param fileName 文件空间/名称
     * @param response
     * @return
     */
    @GetMapping("/{bucket}/{fileName}")
    public void file(@PathVariable String bucket, @PathVariable String fileName, HttpServletResponse response) {
        HrsfLoginUser user = SecurityUtils.getUser();
        if(user != null){
            sysFileService.getFile(bucket, fileName, response);
        }
    }
}
