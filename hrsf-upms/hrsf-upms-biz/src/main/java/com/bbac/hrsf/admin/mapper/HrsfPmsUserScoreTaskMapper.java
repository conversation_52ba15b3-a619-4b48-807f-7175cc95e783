package com.bbac.hrsf.admin.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.bbac.hrsf.admin.api.dto.HrsfPmsUserScoreTaskQueryDTO;
import com.bbac.hrsf.admin.api.entity.HrsfPmsUserScoreTask;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskCountingVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTemplateVo;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import org.apache.ibatis.annotations.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *  历史绩效分数任务 Mapper
 * </p>
 *
 * <AUTHOR> Xueke
 * @since 2025-01-06
 */
@Mapper
public interface HrsfPmsUserScoreTaskMapper extends BaseMapper<HrsfPmsUserScoreTask> {

    /**
     * 初始化任务明细
     * @param taskId 任务ID
     * @param createBy 创建人
     * @param createDate 创建时间
     */
    void initTaskDetail(@Param("taskId") Long taskId, @Param("createBy")String createBy,
                        @Param("createDate") LocalDateTime createDate);
    /**
     * 更新阻塞任务明细状态
     * @param taskId 任务ID
     * @param createBy 更新人
     * @param createDate 更新时间
     */
    void batchUpdatePendingTaskDetail(@Param("taskId") Long taskId, @Param("updateBy")String createBy, @Param("updateDate")LocalDateTime createDate);

    /**
     * 更新失败任务明细状态
     * @param taskId 任务ID
     * @param status 任务状态 0-待执行，1-执行中，2-成功，3-失败，4-终止
     * @param createBy 更新人
     * @param createDate 更新时间
     */
    void batchUpdateFailedTaskDetail(@Param("taskId") Long taskId, @Param("status") Integer status,
                                     @Param("updateBy")String createBy, @Param("updateDate")LocalDateTime createDate);

    /**
     * 查询处理中任务列表
     * @param taskId 任务ID
     * @param statusArr 任务状态集， 0-待执行，1-执行中，2-成功，3-失败
     * @return 任务列表
     */
    List<HrsfPmsUserScoreTaskDetailVo> queryProcessingDetailList(@Param("taskId") Long taskId,
                                                                 @Param("statusArr") Integer[] statusArr);

    /**
     * 分页查询任务明细
     * @param page 分页对象
     * @param wrapper 查询条件
     * @return 任务明细
     */
    IPage<HrsfPmsUserScoreTaskDetailVo> queryScoreTaskDetailListPage(HrsfPmsUserScoreTaskQueryDTO page,
                                                              @Param(Constants.WRAPPER) QueryWrapper<HrsfPmsUserScoreTaskDetailVo> wrapper);
    /**
     * 查询需要的模板数据 年度分数
     * @param staffIdList 员工号列表
     * @return 任务列表
     */
    List<HrsfPmsUserScoreTemplateVo> queryUserScoreYearList(@Param("year") String year, @Param("staffIdList") List<String> staffIdList);
    /**
     * 查询需要的模板数据 季度分数
     * @param staffIdList 员工号列表
     * @return 任务列表
     */
    List<HrsfPmsUserScoreTemplateVo> queryUserScoreQuarterList(@Param("year") String year, @Param("assessType") String assessType, @Param("staffIdList") List<String> staffIdList);

    /**
     * 获取任务详情计数
     * @param taskId 任务ID
     * @return 任务详情计数
     */
    HrsfPmsUserScoreTaskCountingVo obtainTaskDetailCounting(@Param("taskId") Long taskId);

    /**
     * 批量更新任务状态
     * @param taskDetailList 评分模板数据列表的
     */
    void batchUpdateStatus(@Param("taskDetailList") List<HrsfPmsUserScoreTaskDetailVo> taskDetailList,
                           @Param("updateBy")String updateBy, @Param("updateDate")LocalDateTime updateDate);

    /**
     * 批量插入任务详情
     * @param taskDetailList 列表
     * @param createBy
     * @param createDate
     */
    void batchInsertTaskDetail(@Param("taskDetailList") List<HrsfPmsUserScoreTaskDetailVo> taskDetailList,
                           @Param("createBy")String createBy, @Param("createDate")LocalDateTime createDate);
    /**
     * 批量更新任务状态
     * @param taskDetail 评分模板数据列表的
     */
    void updateStatus(@Param("taskDetail") HrsfPmsUserScoreTaskDetailVo taskDetail,
                           @Param("updateBy")String updateBy, @Param("updateDate")LocalDateTime updateDate);

    /**
     * 更新能力评估的年度小分
     * @param templateVo 评分对象
     */
    void updateCompetenciesYearScore(@Param("templateVo") HrsfPmsUserScoreTemplateVo templateVo,
                                     @Param("updateBy")String updateBy, @Param("updateDate")LocalDateTime updateDate);

    /**
     * 更新能力评估的季度小分
     * @param templateVo 评分对象
     */
    void updateCompetenciesQuarterScore(@Param("templateVo") HrsfPmsUserScoreTemplateVo templateVo,
                                     @Param("updateBy")String updateBy, @Param("updateDate")LocalDateTime updateDate);

    /** 获取当前的绩效考核 */
    @Results({
        @Result(property = "id",         column = "ID"),
        @Result(property = "assesYear", column = "ASSESS_YEAR"),
        @Result(property = "assesType",    column = "ASSESS_TYPE")
    })
    @Select("SELECT * FROM HRSF_PMSTART ORDER BY CREATE_TIME DESC FETCH FIRST 1 ROWS ONLY")
    HrsfPmstart getLastHrsfPmstart();

    /** 获取当前的绩效考核 YEAR */
    @Results({
            @Result(property = "id",         column = "ID"),
            @Result(property = "assesYear", column = "ASSESS_YEAR"),
            @Result(property = "assesType",    column = "ASSESS_TYPE")
    })
    @Select("SELECT * FROM HRSF_PMSTART WHERE ASSESS_TYPE = 'YEAR' ORDER BY ASSESS_YEAR DESC, ASSESS_TYPE DESC FETCH FIRST 1 ROWS ONLY")
    HrsfPmstart getLastHrsfPmstartYear();

    /** 获取当前的绩效考核 Quarter */
    @Results({
            @Result(property = "id",         column = "ID"),
            @Result(property = "assesYear", column = "ASSESS_YEAR"),
            @Result(property = "assesType",    column = "ASSESS_TYPE")
    })
    @Select("SELECT * FROM HRSF_PMSTART WHERE ASSESS_TYPE != 'YEAR' ORDER BY ASSESS_YEAR DESC, ASSESS_TYPE DESC FETCH FIRST 1 ROWS ONLY")
    HrsfPmstart getLastHrsfPmstartQuarter();

    /**
     * 获取任务详情中失败的数量
     * @param taskId 任务Id
     * @return errorCount
     */
    @Select("SELECT COUNT(1) as taskDetailCount FROM HRSF_PMS_USER_SCORE_TASK_DETAIL WHERE STATUS=3 " +
            "AND TASK_ID=#{taskId,jdbcType=BIGINT}")
    int getScoreTaskDetailErrorCount(@Param("taskId") Long taskId);

}
