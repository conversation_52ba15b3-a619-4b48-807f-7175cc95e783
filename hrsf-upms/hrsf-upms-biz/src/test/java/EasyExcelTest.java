import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.enums.WriteDirectionEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTaskDetailVo;
import com.bbac.hrsf.admin.api.vo.HrsfPmsUserScoreTemplateVo;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class EasyExcelTest {
    public static void main(String[] args) {
        long epochMilli = LocalDate.now().toEpochDay() * 1000 * 60 * 60 * 24;
//        long epochMilli = Instant.now().toEpochMilli()/1000/60/60/24;
        System.out.println(epochMilli);
    }
    public static void main1(String[] args) {
        String templateFolder = EasyExcelTest.class.getResource("/").getPath() + File.separator;
        String templateFileName =  templateFolder + "template_1.xlsx";
        String handledFileName = templateFolder + "handled_1.xlsx";
        List<HrsfPmsUserScoreTemplateVo> data = getHrsfPmsUserScoreTemplateVos();

        // Base information
        HrsfPmsUserScoreTaskDetailVo baseInfo = new HrsfPmsUserScoreTaskDetailVo();
        baseInfo.setStaffId("123");
        baseInfo.setStaffName("张 Petter");
        baseInfo.setPosition("架构");
        baseInfo.setSystem("CRM");
        baseInfo.setDepartment("数字化部门");
        baseInfo.setSection("架构组");
        baseInfo.setGroup("应用架构");

        // 转为为文件
//        handleTemplateFile(templateFileName, handledFileName, data, baseInfo);
        String base64Str = handleTemplateAsBase64(templateFileName, data, baseInfo);
        System.out.println("base64Str: "+ base64Str);
    }

    /**
     * 填充模板数据
     * @param templateFileName 模板文件名
     * @param handledFileName 处理后的文件名
     * @param data 数据
     */
    public static void handleTemplateFile(String templateFileName, String handledFileName, List<?> data, Object extData){
        ExcelWriter excelWriter = EasyExcel.write(handledFileName).withTemplate(templateFileName).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        excelWriter.fill(data, fillConfig, writeSheet);
        excelWriter.fill(extData, writeSheet);
        excelWriter.finish();
        System.out.println("handledFileName:"+handledFileName);
    }

    /**
     * 填充模板数据，然后转换为base64
     * @param templateFileName 模板文件名
     * @param data 数据
     */
    public static String handleTemplateAsBase64(String templateFileName, List<?> data, Object extData){
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        // 输出为文件流
        ExcelWriter excelWriter = EasyExcel.write(outputStream).withTemplate(templateFileName).inMemory(true).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();
        excelWriter.fill(data, fillConfig, writeSheet);
        excelWriter.fill(extData, writeSheet);
        excelWriter.finish();

        String base64Str = null;
        try{
            // hutool工具转换base64
//            byte[] decode = cn.hutool.core.codec.Base64.decode(outputStream.toByteArray());
//            base64Str = new String(decode, StandardCharsets.UTF_8);
            // jdk工具转换base64
             base64Str = java.util.Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (RuntimeException e){
            e.printStackTrace();
        } finally {
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return base64Str;
    }

    private static List<HrsfPmsUserScoreTemplateVo> getHrsfPmsUserScoreTemplateVos() {
        List<HrsfPmsUserScoreTemplateVo> data = new ArrayList<>();
        HrsfPmsUserScoreTemplateVo vo = new HrsfPmsUserScoreTemplateVo();
        vo.setAssessDesc("2025 第一季度\n2025 Q1");
        vo.setWorkQualityScore(new BigDecimal(0.6));
        vo.setCostControlScore(new BigDecimal(0.7));
        vo.setCompletionOfWorkScore(new BigDecimal(0.8));
        data.add(vo);
        vo = new HrsfPmsUserScoreTemplateVo();
        vo.setAssessDesc("2025 第二季度\n2025 Q2");
        vo.setWorkQualityScore(new BigDecimal(0.5));
        vo.setCostControlScore(new BigDecimal(0.8));
        vo.setCompletionOfWorkScore(new BigDecimal(0.9));
        data.add(vo);
        data.add(new HrsfPmsUserScoreTemplateVo("2025 第三季度\n2025 Q3"));
        data.add(new HrsfPmsUserScoreTemplateVo("2025 年度\n2025 Year"));
        data.add(new HrsfPmsUserScoreTemplateVo("2026 第一季度\n2026 Q1"));
        data.add(new HrsfPmsUserScoreTemplateVo("2026 第二季度\n2026 Q2"));
        data.add(new HrsfPmsUserScoreTemplateVo("2026 第三季度\n2026 Q3"));
        data.add(new HrsfPmsUserScoreTemplateVo("2026 年度\n2026 Year"));
        data.add(new HrsfPmsUserScoreTemplateVo("2027 第一季度\n2027 Q1"));
        data.add(new HrsfPmsUserScoreTemplateVo("2027 第二季度\n2027 Q2"));
        data.add(new HrsfPmsUserScoreTemplateVo("2027 第三季度\n2027 Q3"));
        data.add(new HrsfPmsUserScoreTemplateVo("2027 年度\n2027 Year"));
        data.add(new HrsfPmsUserScoreTemplateVo("2028 第一季度\n2028 Q1"));
        data.add(new HrsfPmsUserScoreTemplateVo("2028 第二季度\n2028 Q2"));
        data.add(new HrsfPmsUserScoreTemplateVo("2028 第三季度\n2028 Q3"));
        data.add(new HrsfPmsUserScoreTemplateVo("2028 年度\n2028 Year"));
        data.add(new HrsfPmsUserScoreTemplateVo("2029 第一季度\n2029 Q1"));
        data.add(new HrsfPmsUserScoreTemplateVo("2029 第二季度\n2029 Q2"));
        data.add(new HrsfPmsUserScoreTemplateVo("2029 第三季度\n2029 Q3"));
        data.add(new HrsfPmsUserScoreTemplateVo("2029 年度\n2029 Year"));
        return data;
    }
}
