package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-15
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMSUSER_BASE")
@ApiModel(value = "HrsfPmsuserBase对象", description = "")
@NoArgsConstructor
@AllArgsConstructor
public class HrsfPmsuserBase extends BaseEntity {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("工号")
    @TableField(value = "STAFF_ID")
    private String staffId;

    @ApiModelProperty("员工状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("员工姓名")
    @TableField("FULL_NAME")
    private String fullName;

    @ApiModelProperty("所属系统")
    @TableField("SYSTEM")
    private String system;

    @ApiModelProperty("部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty("科室")
    @TableField("SECTION")
    private String section;

    @ApiModelProperty("工段/组")
    @TableField("USER_GROUP")
    private String userGroup;

    @ApiModelProperty("岗位")
    @TableField("POSITION")
    private String position;

    @ApiModelProperty("职称")
    @TableField("JOB_TITLE")
    private String jobTitle;

    @ApiModelProperty("职级")
    @TableField("USER_LEVEL")
    private String userLevel;

    @ApiModelProperty("用工形式")
    @TableField("EMPLOYMENT_TYPE")
    private String employmentType;

    @ApiModelProperty("蓝领/白领")
    @TableField("BC_WC")
    private String bcWc;

    @ApiModelProperty("是否是顺义L4")
    @TableField("SYL4")
    private String syl4;

    @ApiModelProperty("是否为助理人员")
    @TableField("ASSISTANT")
    private String assistant;

    @ApiModelProperty("隔级上级评分")
    @TableField("LEVEL_ASSESSMENT")
    private String levelAssessment;

    @ApiModelProperty("绩效评分人")
    @TableField("EVALUATORS")
    private String evaluators;

    @ApiModelProperty("新入职员工")
    @TableField("NEW_HIRE")
    private String newHire;

    @ApiModelProperty("新晋升人员")
    @TableField("NEW_PROMOTION")
    private String newPromotion;

    @ApiModelProperty("股东方加入BBAC人员")
    @TableField("SHAREHOLDERS_JOIN_BBAC")
    private String shareholdersJoinBbac;

    @ApiModelProperty("出勤率")
    @TableField("ATTENDANCE_RATE")
    private String attendanceRate;

    @ApiModelProperty("产假")
    @TableField("MATERNITY_LEAVE")
    private String maternityLeave;

    @ApiModelProperty("旷工")
    @TableField("ABSENCE")
    private String absence;

    @ApiModelProperty("考核年度")
    @TableField("ASSESS_YEAR")
    private String assessYear;

    @ApiModelProperty("考核类型")
    @TableField("ASSESS_TYPE")
    private String assessType;

    @ApiModelProperty("表单ID")
    @TableField("FORM_ID")
    private String formId;

    @ApiModelProperty("Content ID")
    @TableField("CONTENT_ID")
    @Deprecated
    private String contentId;

    @ApiModelProperty("员工档案Deeplink")
    @TableField("USER_DEEPLINK")
    private String userDeeplink;

    @ApiModelProperty("一季度分数")
    @TableField("Q1_SCORE")
    private BigDecimal q1Score;

    @ApiModelProperty("二季度分数")
    @TableField("Q2_SCORE")
    private BigDecimal q2Score;

    @ApiModelProperty("三季度分数")
    @TableField("Q3_SCORE")
    private BigDecimal q3Score;

    @ApiModelProperty("季度均分+5%")
    @TableField("AVERAGE_SCORE")
    private BigDecimal averageScore;

    @ApiModelProperty("调岗考核最终总分")
    @TableField("TRANS_TOTAL_SCORE")
    private String transTotalScore;

    @ApiModelProperty("惩处信息")
    @TableField("PUNISH_INFO")
    private String punishInfo;

    @ApiModelProperty("最终总分")
    @TableField("TOTAL_SCORE")
    private BigDecimal totalScore;

    @ApiModelProperty("最终总分原始值")
    @TableField("TOTAL_SCORE_ORIGINAL")
    private BigDecimal totalScoreOriginal;

    @ApiModelProperty("业绩等级")
    @TableField("PMS_LEVEL")
    private BigDecimal pmsLevel;

    @ApiModelProperty("业绩等级描述")
    @TableField("PMS_LEVEL_DESC")
    private String pmsLevelDesc;

    @ApiModelProperty("潜力等级")
    @TableField("POTENTIAL_LEVEL")
    private String potentialLevel;

    @ApiModelProperty("潜力等级描述")
    @TableField("POTENTIAL_LEVEL_DESC")
    private String potentialLevelDesc;

    @ApiModelProperty("可变动性")
    @TableField("VARIABLE")
    private String variable;

    @ApiModelProperty("可变动性描述")
    @TableField("VARIABLE_DESC")
    private String variableDesc;

    @ApiModelProperty("表单状态")
    @TableField("FORM_STATUS")
    private String formStatus;

    @ApiModelProperty("指标1得分")
    @TableField("TARGET1_SCORE")
    private BigDecimal target1Score;

    @ApiModelProperty("指标2得分")
    @TableField("TARGET2_SCORE")
    private BigDecimal target2Score;

    @ApiModelProperty("指标3得分")
    @TableField("TARGET3_SCORE")
    private BigDecimal target3Score;

    @ApiModelProperty("指标4得分")
    @TableField("TARGET4_SCORE")
    private BigDecimal target4Score;

    @ApiModelProperty("指标5得分")
    @TableField("TARGET5_SCORE")
    private BigDecimal target5Score;

    @ApiModelProperty("指标6得分")
    @TableField("TARGET6_SCORE")
    private BigDecimal target6Score;

    @ApiModelProperty("校准会议基础表ID")
    @TableField("CALIBRATION_BASE_ID")
    private Long calibrationBaseId;

    @ApiModelProperty("分数来源 0评分表 1批量导入")
    @TableField("SCORE_SOURCE")
    private String scoreSource;

    @ApiModelProperty("指标7得分")
    @TableField("TARGET7_SCORE")
    private BigDecimal target7Score;
    @ApiModelProperty("指标8得分")
    @TableField("TARGET8_SCORE")
    private BigDecimal target8Score;
    @ApiModelProperty("指标9得分")
    @TableField("TARGET9_SCORE")
    private BigDecimal target9Score;
    @ApiModelProperty("指标10得分")
    @TableField("TARGET10_SCORE")
    private BigDecimal target10Score;

    @ApiModelProperty("标绿员工字段")
    @TableField(value = "CHANGE_FLAG")
    private String changeFlag;

    @ApiModelProperty("是否调整")
    @TableField(value = "ADJUSTED")
    private Boolean adjusted;

    @ApiModelProperty("潜力试图是否调整 0：否 1：是")
    @TableField(value = "P_ADJUSTED")
    private Boolean pAdjusted;

    @ApiModelProperty("校验模板类型")
    @TableField(value = "LEVEL_FLAG")
    private String levelFlag;

    @ApiModelProperty("员工号")
    @TableField(value = "EMP_ID")
    private String empId;


    public HrsfPmsuserBase(String formSubjectId, String formContentId, String formDataId, Long id, String userDeeplink) {
        super();
        this.staffId = formSubjectId;
        this.contentId = formContentId;
        this.formId = formDataId;
        this.id = id;
        this.userDeeplink = userDeeplink;
    }

    public HrsfPmsuserBase(String assessYear, String assessType, String formDataId, String staffId, String userDeeplink) {
        super();
        this.assessYear = assessYear;
        this.assessType = assessType;
        this.formId = formDataId;
        this.staffId = staffId;
        this.userDeeplink = userDeeplink;
    }

    public HrsfPmsuserBase(String formSubjectId, String transTotalScore, Long id) {
        super();
        this.staffId = formSubjectId;
        this.transTotalScore = transTotalScore;
        this.id = id;
    }

    public HrsfPmsuserBase(Long id,String transTotalScore) {
        super();
        this.transTotalScore = transTotalScore;
        this.id = id;
    }

    public HrsfPmsuserBase(BigDecimal totalScore, BigDecimal pmsLevel, String pmsLevelDesc, String formStatus, String scoreSource, Long id) {
        super();
        this.totalScore = totalScore;
        this.pmsLevel = pmsLevel;
        this.pmsLevelDesc = pmsLevelDesc;
        this.formStatus = formStatus;
        this.scoreSource = scoreSource;
        this.id = id;
    }

    public HrsfPmsuserBase(BigDecimal totalScore, BigDecimal pmsLevel, String pmsLevelDesc, String formStatus, String scoreSource, Long id
            , String potentialLevel, String potentialLevelDesc, String variable, String variableDesc) {
        super();
        this.totalScore = totalScore;
        this.pmsLevel = pmsLevel;
        this.pmsLevelDesc = pmsLevelDesc;
        this.formStatus = formStatus;
        this.scoreSource = scoreSource;
        this.id = id;
        this.potentialLevel = potentialLevel;
        this.potentialLevelDesc = potentialLevelDesc;
        this.variable = variable;
        this.variableDesc = variableDesc;
    }

    public HrsfPmsuserBase(String formStatus, Long id) {
        super();
        this.formStatus = formStatus;
        this.id = id;
    }

}
