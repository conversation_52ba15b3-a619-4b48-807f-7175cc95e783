package com.bbac.hrsf.admin.api.vo;

import com.bbac.hrsf.admin.api.dto.HrsfUserFilterDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class HrsfPmsUserVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("绩效协调员ID")
    private Long pmsUserId;

    @ApiModelProperty("绩效协调员")
    private String pmsStaff;

    @ApiModelProperty("绩效协调员")
    private String pmsStaffName;

    @ApiModelProperty("绩效协调员描述")
    private String description;

    @ApiModelProperty("授权员工删选器")
    private List<HrsfUserFilterDTO> selectList;


    @ApiModelProperty("授权员工信息")
    private List<HrsfUserVO> hrsfUserList;


}
