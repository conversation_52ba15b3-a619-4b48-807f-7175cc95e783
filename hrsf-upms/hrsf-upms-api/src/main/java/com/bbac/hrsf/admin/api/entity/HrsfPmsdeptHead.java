package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMSDEPT_HEAD")
@ApiModel(value = "HrsfPmsdeptHead对象", description = "")
public class HrsfPmsdeptHead extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("岗位ID")
    @TableField("POSITION_ID")
    private String positionId;

    @ApiModelProperty("机构ID")
    @TableField("ORGAN_ID")
    private String organId;

    @ApiModelProperty("员工ID")
    @TableField("STAFF_ID")
    private String staffId;


}
