/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.api.feign;

import com.bbac.hrsf.admin.api.dto.CalibrationBaseAndIdDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.ServiceNameConstants;
import com.bbac.hrsf.common.core.pojo.ErrorMessageVo;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.entity.HrsfPmstart;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
@FeignClient(contextId = "remoteUserServiceTime", value = ServiceNameConstants.UMPS_SERVICE)
public interface RemoteUserTimeService {

    /**
     * @param calibrationBaseAndIdDTO
     * @param from
     * @return
     */
    @PostMapping("/user/syncScoreBySF")
    R<ErrorMessageVo> syncScoreBySF(@RequestBody CalibrationBaseAndIdDTO calibrationBaseAndIdDTO, @RequestHeader(SecurityConstants.FROM) String from);
    /**
     * @param userBaseIdList
     * @param from
     * @return
     */
    @PostMapping("/user/returnCalibration")
    R<ErrorMessageVo> returnCalibration(@RequestBody List<Long> userBaseIdList, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 根据部门id，查询对应的用户 id 集合
     * hrsfPmstart
     */
    @PostMapping("/user/syncPmsUser")
    R<Boolean> syncPmsUser(@RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 根据部门id，查询对应的用户 id 集合
     * hrsfPmstart
     */
    @PostMapping("/user/startPmsUser")
    R<Boolean> startPmsUser(@RequestBody HrsfPmstart hrsfPmstart,
                            @RequestHeader(SecurityConstants.FROM) String from);


}
