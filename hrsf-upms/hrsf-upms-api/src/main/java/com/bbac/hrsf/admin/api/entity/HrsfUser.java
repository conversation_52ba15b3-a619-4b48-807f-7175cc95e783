package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-10
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_USER")
@ApiModel(value = "HrsfUser对象", description = "")
public class HrsfUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("员工状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("用户名")
    @TableField("USER_NAME")
    private Long userName;

    @ApiModelProperty("工号")
    @TableField("STAFF_ID")
    private Long staffId;

    @ApiModelProperty("员工姓名")
    @TableField("FULL_NAME")
    private String fullName;

    @ApiModelProperty("员工英文名")
    @TableField("FULL_NAME_EN")
    private String fullNameEn;

    @ApiModelProperty("性别")
    @TableField("GENDER")
    private String gender;

    @ApiModelProperty("年龄")
    @TableField("AGE")
    private Long age;

    @ApiModelProperty("邮箱")
    @TableField("EMAIL")
    private String email;

    @ApiModelProperty("国籍")
    @TableField("COUNTRY")
    private String country;

    @ApiModelProperty("最高学历")
    @TableField("HIGHTEST_EDUCATION")
    private String hightestEducation;

    @ApiModelProperty("最高学位")
    @TableField("HIGHTEST_DEGREE")
    private String hightestDegree;

    @ApiModelProperty("专业")
    @TableField("MAJOR")
    private String major;

    @ApiModelProperty("入职日期")
    @TableField("HIRE_DATE")
    private Date hireDate;

    @ApiModelProperty("参加工作时间")
    @TableField("INITIAL_WORKING_DATE")
    private Date initialWorkingDate;

    @ApiModelProperty("BBAC工作时长")
    @TableField("LENOFS_IN_BBAC")
    private BigDecimal lenofsInBbac;

    @ApiModelProperty("现职位工作时长")
    @TableField("LENOFS_IN_CURRENT_POSITION")
    private BigDecimal lenofsInCurrentPosition;

    @ApiModelProperty("现级别工作时长")
    @TableField("LENOFS_IN_CURRNET_LEVEL")
    private String lenofsInCurrnetLevel;

    @ApiModelProperty("所属系统")
    @TableField("SYSTEM")
    private String system;

    @ApiModelProperty("部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty("科室")
    @TableField("SECTION")
    private String section;

    @ApiModelProperty("工段/组")
    @TableField("USER_GROUP")
    private String userGroup;

    @ApiModelProperty("岗位")
    @TableField("POSITION")
    private String position;

    @ApiModelProperty("职称")
    @TableField("JOB_TITLE")
    private String jobTitle;

    @ApiModelProperty("职级")
    @TableField("USER_LEVEL")
    private String userLevel;

    @ApiModelProperty("职务|岗位级别")
    @TableField("POSITION_LEVEL")
    private String positionLevel;

    @ApiModelProperty("职位分类")
    @TableField("JOB_CODE")
    private String jobCode;

    @ApiModelProperty("用工形式")
    @TableField("EMPLOYMENT_TYPE")
    private String employmentType;

    @ApiModelProperty("蓝领/白领")
    @TableField("COLLAR")
    private String collar;

    @ApiModelProperty("机构负责人")
    @TableField("HEAD_OF_ORGANIZATION")
    private String headOfOrganization;

    @ApiModelProperty("是否为助理人员")
    @TableField("ASSISTANT")
    private String assistant;

    @ApiModelProperty("是否为直属")
    @TableField("DIRECT_REPORT")
    private String directReport;

    @ApiModelProperty("人才推荐项目")
    @TableField("SUGGESTED_PROJECT")
    private String suggestedProject;

    @ApiModelProperty("绩效评分人1")
    @TableField("EVALUATORS1")
    private String evaluators1;

    @ApiModelProperty("绩效评分人2")
    @TableField("EVALUATORS2")
    private String evaluators2;

    @ApiModelProperty("绩效HR")
    @TableField("PERFORMANCE_HR")
    private String performanceHr;

    @ApiModelProperty("人才HR")
    @TableField("TALENT_HR")
    private String talentHr;

    @ApiModelProperty("新入职员工")
    @TableField("NEW_HIRE")
    private String newHire;

    @ApiModelProperty("新晋升人员")
    @TableField("NEW_PROMOTION")
    private String newPromotion;

    @ApiModelProperty("股东方加入BBAC人员")
    @TableField("SHAREHOLDERS_JOIN_BBAC")
    private String shareholdersJoinBbac;

    @ApiModelProperty("股东方加入BBAC时间")
    @TableField("SHAREHOLDERS_JOIN_DATE")
    private Date shareholdersJoinDate;

    @ApiModelProperty("特殊离职人员")
    @TableField("SPECIAL_RESIGNED_EMPLOYEE")
    private String specialResignedEmployee;

    @ApiModelProperty("出勤率")
    @TableField("ATTENDANCE_RATE")
    private String attendanceRate;

    @ApiModelProperty("产假")
    @TableField("MATERNITY_LEAVE")
    private String maternityLeave;

    @ApiModelProperty("旷工")
    @TableField("ABSENCE")
    private String absence;


}
