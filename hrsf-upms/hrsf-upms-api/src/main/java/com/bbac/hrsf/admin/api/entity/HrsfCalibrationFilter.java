package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_CALIBRATION_FILTER")
@ApiModel(value = "HrsfCalibrationFilter对象", description = "")
public class HrsfCalibrationFilter {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("职级")
    @TableField("USER_LEVEL")
    private String userLevel;

    @ApiModelProperty("是否为助理")
    @TableField("ASSISTANT")
    private String assistant;

    @ApiModelProperty("是否为顺义L4")
    @TableField("SYL4")
    private String syl4;

    @ApiModelProperty("员工")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty("机构信息")
    @TableField("ORGANIZATION")
    private String organization;

    @ApiModelProperty("考核年度")
    @TableField("ASSESS_YEAR")
    private String assessYear;

    @ApiModelProperty("考核类型")
    @TableField("ASSESS_TYPE")
    private String assessType;

    @ApiModelProperty("校准会议基础表ID")
    @TableField("CALIBRATION_BASE_ID")
    private Long calibrationBaseId;

}
