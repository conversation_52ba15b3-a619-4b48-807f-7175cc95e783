package com.bbac.hrsf.admin.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户excel 对应的实体
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
@ColumnWidth(40)
public class PmsUserExcelYearDownVO implements Serializable {

    private static final long serialVersionUID = -7293020830534147811L;
    /**
     * StaffID
     */
    @ExcelProperty("工号Staff ID")
    private String staffId;

    /**
     * 用户名
     */
    @ExcelProperty("姓名 Full Name")
    private String fullName;

    /**
     * 部门名称
     */
    @ExcelProperty("部门 Department")
    private String department;


    /**
     * 科室 Section
     */
    @ExcelProperty("科室 Section")
    private String section;

    /**
     * 工段/组 Group
     */
    @ExcelProperty("工段/组 Group")
    private String userGroup;

    /**
     * 1.工作任务完成 Completion of Work
     */
    @ExcelProperty("(20%)工作任务完成 Completion of Work")
    private String target1Score;

    /**
     * 2.质量目标 Work Quality Safety
     */
    @ExcelProperty("(20%)质量目标 Work Quality Safety")
    private String target2Score;

    /**
     * 3.安全生产 Production Safety
     */
    @ExcelProperty("(10%)安全生产 Production Safety")
    private String target3Score;

    /**
     * 4.出勤 Attendance
     */
    @ExcelProperty("(10%)出勤 Attendance")
    private String target4Score;

    /**
     * 5.成本控制 Cost Control
     */
    @ExcelProperty("(10%)成本控制 Cost Control")
    private String target5Score;

    /**
     * 6.人际沟通 Communication
     */
    @ExcelProperty("(5%)人际沟通 Communication")
    private String target6Score;

    /**
     * 7.团队协作 Team Work
     */
    @ExcelProperty("(5%)团队协作 Team Work")
    private String target7Score;

    /**
     * 8.专业技能 Professional
     */
    @ExcelProperty("(10%)专业技能 Professional")
    private String target8Score;

    /**
     * 9.敬业负责 Responsibility
     */
    @ExcelProperty("(5%)敬业负责 Responsibility")
    private String target9Score;

    /**
     * 10.严谨规范 Discipline
     */
    @ExcelProperty("(5%)严谨规范 Discipline")
    private String target10Score;

    /**
     * 业绩等级 Performance Rank
     */
    @ExcelProperty("业绩等级 Performance Category")
    private String pmsLevelDesc;

    /**
     * 加权总分 Weighted Rating
     */
    @ExcelProperty("加权总分 Weighted Rating")
    @NumberFormat("#.##%")
    private WriteCellData<Double> weightRating;

    /**
     * 最终总分 Final Rating
     */
    @ExcelProperty("最终总分 Final Result")
    @NumberFormat("#.##%")
    private WriteCellData<Double> finalRating;

    /**
     * 新入职员工 New Hire
     */
    @ExcelProperty("新入职员工 New Hire")
    private String newHire;
    /**
     * 新晋升员工 New Promotion
     */
    @ExcelProperty("新晋升员工 New Promotion")
    private String newPromotion;
    /**
     * 股东方加入BBAC人员 Shareholders Join BBAC
     */
    @ExcelProperty("股东方加入BBAC人员 Shareholders Join BBAC")
    private String shareholdersJoinBbac;
    /**
     * 出勤 Attendance Rate
     */
    @ExcelProperty("出勤 Attendance Rate")
    private String attendanceRate;

    /**
     * 产假 Maternity Leave
     */
    @ExcelProperty("产假 Maternity Leave")
    private String maternityLeave;
    /**
     * 旷工 Absence
     */
    @ExcelProperty("旷工 Absence")
    private String absence;

    /**
     * 惩处信息
     */
    @ExcelProperty("惩处信息 Punishment")
    private String punishInfo;

    /**
     * 季度均分+5% Average(Q1+Q2+Q3)+5%
     */
    @ExcelProperty("季度均分+5% Average(Q1+Q2+Q3)+5%")
    private String averageScore;


}
