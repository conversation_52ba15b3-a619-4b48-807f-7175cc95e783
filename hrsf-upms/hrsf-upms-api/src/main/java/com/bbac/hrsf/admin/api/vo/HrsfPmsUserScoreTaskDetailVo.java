package com.bbac.hrsf.admin.api.vo;

import com.bbac.hrsf.common.core.constant.CommonConstants;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;

/**
 * <p>
 * 历史绩效分数任务明细 Entity
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Getter
@Setter
@Slf4j
public class HrsfPmsUserScoreTaskDetailVo {

    /** ID */
    private Long id;
    /** 姓名 */
    private String staffName;
    /** 员工号 */
    private String staffId;
    /** 员工类型：1-白领，2-蓝领, 3-编外白领, 4-编外蓝领 */
    private Integer staffType;
    /** SF formDataId */
    private String formDataId;
    /** 岗位 */
    private String position;
    /** 系统 */
    private String system;
    /** 部门 */
    private String department;
    /** 科室 */
    private String section;
    /** 工段/组 */
    private String group;

    //////////// 任务辅助信息 ////////////
    /** 任务ID */
    private Long taskId;
    /** 任务状态 0-待执行，1-执行中，2-成功，3-失败 */
    private Integer taskStatus;
    /** 任务错误消息, taskStatus为3时返回 */
    private String taskErrorMessage;
    /** SF文件上传时间 */
    private LocalDateTime sfUploadTime;

    public void setTaskErrorMessage(String taskErrorMessage) {
        if(taskErrorMessage == null){
            return;
        }
        // 如果是超时，则将任务状态重置
        if(taskErrorMessage.contains(CommonConstants.HTTP_TIME_OUT_MESSAGE)){
            this.taskStatus = 0;
        } else {
            this.taskStatus = 3;
        }
        if(this.taskErrorMessage == null){
            this.taskErrorMessage = taskErrorMessage;
        } else {
            // 多个消息拼接
            this.taskErrorMessage = this.taskErrorMessage + "<br/>" + taskErrorMessage;
        }
        // 过长消息截断
        if(this.taskErrorMessage.length() > 200){
            log.warn("UserScoreTask error message over length(200)， taskErrorMessage: {}", this.taskErrorMessage);
            this.taskErrorMessage = this.taskErrorMessage.substring(0, 196) + "...";
        }
    }
}
