package com.bbac.hrsf.admin.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class PerformanceRatingVO implements Serializable {


    private static final long serialVersionUID = 7069777072727878496L;
    @ApiModelProperty("考核年度")
    private String custYear;

    @ApiModelProperty("考核类型")
    private String custType;

    @ApiModelProperty("最终总分")
    private String custRating;

    @ApiModelProperty("业绩等级")
    private String custPerank;

    @ApiModelProperty("可变动性")
    private String custTrend;

    @ApiModelProperty("潜能等级")
    private String custPorank;


    @ApiModelProperty("时间")
    private Long effectiveStartDate;


}
