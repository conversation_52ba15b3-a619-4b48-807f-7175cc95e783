package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <p>
 * 历史绩效分数任务 Entity
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-06
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_SCORE_TASK")
@ApiModel(value = "历史绩效分数任务", description = "")
public class HrsfPmsUserScoreTask extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("任务ID 主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    /** 任务类型：1-历史绩效汇总，2-上周期绩效复制") */
    @ApiModelProperty("任务类型：1-历史绩效汇总，2-上周期绩效复制")
    @TableField("TASK_TYPE")
    private Integer taskType;

    /** 考核年度, All-表示首次同步 */
    @ApiModelProperty("考核年度")
    @TableField("ASSESS_YEAR")
    private String assesYear;

    /** 考核类型, YEAR,Q1,Q2,Q3,All-表示首次同步 */
    @ApiModelProperty("考核类型")
    @TableField("ASSESS_TYPE")
    private String assesType;

    /** 表单数据拉取标识：<br>1-需从SF拉取(表示该周期未拉取过)<br>2-无需拉取(表示已拉取过该周期数据)<br>3-强制拉取(已拉取过，再次拉取) */
    @ApiModelProperty("表单数据拉取标识 1-需从SF拉取(表示该周期未拉取过)，2-无需拉取(表示已拉取过该周期数据)，3-强制拉取(已拉取过，再次拉取) ")
    @TableField("FORM_DATA_PULL_FLAG")
    private Integer formDataPullFlag;

    @ApiModelProperty("开始时间")
    @TableField("START_TIME")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    @TableField("END_TIME")
    private LocalDateTime endTime;

    /** 状态：1-执行中，2-成功，3-失败，4-终止 */
    @ApiModelProperty("状态：1-执行中，2-成功，3-失败，4-终止")
    @TableField("STATUS")
    private Integer status;

    @ApiModelProperty("任务明细的数量")
    @TableField("PROCESS_NUMBER")
    private Integer processNumber;

    @ApiModelProperty("该任务的最大明细记录ID，用于判断任务是否处理完成")
    @TableField("MAX_DETAIL_ID")
    private Long maxDetailId;

    /** 上一员工号，用于处理多个审批问题 */
    @TableField(exist = false)
    private String lastStaffId;

}
