package com.bbac.hrsf.admin.api.vo;

import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class HrsfUserVO extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("员工状态")
    private String status;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("工号")
    private String staffId;

    @ApiModelProperty("员工姓名")
    private String fullName;

    @ApiModelProperty("员工英文名")
    private String fullNameEn;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("科室")
    private String section;

    @ApiModelProperty("工段/组")
    private String userGroup;

    @ApiModelProperty("岗位")
    private String position;

    @ApiModelProperty("职称")
    private String jobTitle;

    @ApiModelProperty("职级")
    private String userLevel;

    @ApiModelProperty("职务|岗位级别")
    private String positionLevel;

    @ApiModelProperty("头像")
    private String photo;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HrsfUserVO that = (HrsfUserVO) o;
        return staffId.equals(that.staffId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(staffId);
    }
}
