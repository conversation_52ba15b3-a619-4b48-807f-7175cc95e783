package com.bbac.hrsf.admin.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liu, jie
 * @create: 2022-06-24
 **/
@Data
public class HrsfPmsUserBaseVO {

    @ApiModelProperty("员工ID")
    private String staffIds;

    @ApiModelProperty("员工姓名")
    private String name;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("科室")
    private String section;

    @ApiModelProperty("工段/组")
    private String userGroup;

    @ApiModelProperty("职级")
    private String userLevel;
}
