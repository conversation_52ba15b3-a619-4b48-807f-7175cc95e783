package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;

import static org.apache.ibatis.type.JdbcType.VARCHAR;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-12
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_USER_BASE")
@ApiModel(value = "HrsfUserBase对象", description = "")
@AllArgsConstructor
@NoArgsConstructor
public class HrsfUserBase extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("工号")
    @TableId(value = "STAFF_ID")
    private String staffId;

    @ApiModelProperty("员工状态")
    @TableField(value = "STATUS",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String status;

    @ApiModelProperty("用户名")
    @TableField("USER_NAME")
    private String userName;

    @ApiModelProperty("员工ID")
    @TableField("USER_ID")
    private String userId;

    @ApiModelProperty("员工姓名")
    @TableField(value = "FULL_NAME",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String fullName;

    @ApiModelProperty("性别")
    @TableField(value = "GENDER",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String gender;

    @ApiModelProperty("年龄")
    @TableField(value = "AGE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String age;

    @ApiModelProperty("邮箱")
    @TableField(value = "EMAIL",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String email;

    @ApiModelProperty("国籍")
    @TableField(value = "COUNTRY",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String country;

    @ApiModelProperty("最高学历")
    @TableField(value = "HIGHTEST_EDUCATION",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String hightestEducation;

    @ApiModelProperty("最高学位")
    @TableField(value = "HIGHTEST_DEGREE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String hightestDegree;

    @ApiModelProperty("专业")
    @TableField(value = "MAJOR",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String major;

    @ApiModelProperty("入职日期")
    @TableField("HIRE_DATE")
    private LocalDateTime hireDate;

    @ApiModelProperty("参加工作时间")
    @TableField(value = "INITIAL_WORKING_DATE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String initialWorkingDate;

    @ApiModelProperty("BBAC工作时长")
    @TableField(value = "LENOFS_IN_BBAC",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String lenofsInBbac;

    @ApiModelProperty("现职位工作时长")
    @TableField(value = "LENOFS_IN_CURRENT_POSITION",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String lenofsInCurrentPosition;

    @ApiModelProperty("现级别工作时长")
    @TableField(value = "LENOFS_IN_CURRNET_LEVEL",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String lenofsInCurrnetLevel;

    @ApiModelProperty("所属系统")
    @TableField(value = "SYSTEM",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String system;

    @ApiModelProperty("部门")
    @TableField(value = "DEPARTMENT",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String department;

    @ApiModelProperty("科室")
    @TableField(value = "SECTION",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String section;

    @ApiModelProperty("工段/组")
    @TableField(value = "USER_GROUP",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String userGroup;

    @ApiModelProperty("岗位")
    @TableField(value = "POSITION",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String position;

    @ApiModelProperty("职称")
    @TableField(value = "JOB_TITLE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String jobTitle;

    @ApiModelProperty("职级")
    @TableField(value = "USER_LEVEL",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String userLevel;

    @ApiModelProperty("职务|岗位级别")
    @TableField(value = "POSITION_LEVEL",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String positionLevel;

    @ApiModelProperty("职位分类")
    @TableField(value = "JOB_CODE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String jobCode;

    @ApiModelProperty("用工形式")
    @TableField(value = "EMPLOYMENT_TYPE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String employmentType;

    @ApiModelProperty("蓝领/白领")
    @TableField(value = "BC_WC",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String bcWc;

    @ApiModelProperty("是否是顺义L4")
    @TableField(value = "SYL4",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String syl4;

    @ApiModelProperty("是否为助理人员")
    @TableField(value = "ASSISTANT",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String assistant;

    @ApiModelProperty("隔级上级评分")
    @TableField(value = "LEVEL_ASSESSMENT",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String levelAssessment;

    @ApiModelProperty("绩效评分人1")
    @TableField(value = "EVALUATOR1",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String evaluator1;

    @ApiModelProperty("绩效评分人2")
    @TableField(value = "EVALUATOR2",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String evaluator2;

    @ApiModelProperty("新晋升人员")
    @TableField(value = "NEW_PROMOTION",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String newPromotion;

    @ApiModelProperty("股东方加入BBAC人员")
    @TableField(value = "SHAREHOLDERS_JOIN_BBAC",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String shareholdersJoinBbac;

    @ApiModelProperty("股东方加入BBAC时间")
    @TableField(value = "SHAREHOLDERS_JOIN_DATE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private LocalDateTime shareholdersJoinDate;

    @ApiModelProperty("特殊离职人员")
    @TableField(value = "SPECIAL_RESIGNED_EMPLOYEE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String specialResignedEmployee;

    @ApiModelProperty("出勤率")
    @TableField(value = "ATTENDANCE_RATE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String attendanceRate;

    @ApiModelProperty("产假")
    @TableField(value = "MATERNITY_LEAVE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String maternityLeave;

    @ApiModelProperty("旷工")
    @TableField(value = "ABSENCE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String absence;

    @ApiModelProperty("绩效潜力9宫格位置")
    @TableField(value = "PXP_PLACE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String pxpPlace;

    @ApiModelProperty("潜力能力9宫格位置")
    @TableField(value = "PXC_Y_PLACE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String pxcYPlace;

    @ApiModelProperty("新入职员工")
    @TableField(value = "NEW_HIRE",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String newHire;

    @ApiModelProperty("照片")
    @TableField(value = "PHOTO",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String photo;

    @ApiModelProperty("隔级上级")
    @TableField(value = "LEVEL_EVALUATOR",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String levelEvaluator;

    @ApiModelProperty("员工号")
    @TableField(value = "EMP_ID",updateStrategy = FieldStrategy.IGNORED,jdbcType = VARCHAR)
    private String empId;


    public HrsfUserBase(String staffId, String photo) {
        super();
        this.staffId = staffId;
        this.photo = photo;
    }
}
