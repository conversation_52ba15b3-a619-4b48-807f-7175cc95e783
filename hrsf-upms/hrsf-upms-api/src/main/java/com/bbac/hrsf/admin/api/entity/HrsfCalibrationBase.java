package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_CALIBRATION_BASE")
@ApiModel(value = "HrsfCalibrationBase对象", description = "")
public class HrsfCalibrationBase extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("考核年度")
    @TableField("ASSESS_YEAR")
    private String assesYear;

    @ApiModelProperty("考核类型")
    @TableField("ASSESS_TYPE")
    private String assesType;

    @ApiModelProperty("职级")
    @TableField("USER_LEVEL")
    private String userLevel;

    @ApiModelProperty("机构")
    @TableField("ORGANIZATION")
    private String organization;

    @ApiModelProperty("校准会议名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("校准会议模板")
    @TableField("TEMPLATE")
    private String template;

    @ApiModelProperty("校准会议状态")
    @TableField("STATUS")
    private Byte status;

    @ApiModelProperty("当前处理人")
    @TableField("CURRENT_HANDLER")
    private String currentHandler;

    /**
     * 该字段废弃
     * 校准会议层级没有这个状态
     */
    @ApiModelProperty("代办状态")
    @TableField("PROCESS_STATUS")
    @Deprecated()
    private String processStatus;

    @ApiModelProperty("流程步骤")
    @TableField("PROCESS_STEP")
    private String processStep;

    @ApiModelProperty("流程ID")
    @TableField("PROCESS_INSTANCE_ID")
    private String processInstanceId;

    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;



}
