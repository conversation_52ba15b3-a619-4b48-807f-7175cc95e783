package com.bbac.hrsf.admin.api.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 历史绩效分数Excel模板 Vo
 * </p>
 *
 * <AUTHOR> Xueke
 * @since 2025-01-07
 */
@Data
@NoArgsConstructor
public class HrsfPmsUserScoreTemplateVo {

    public HrsfPmsUserScoreTemplateVo(String assessDesc){
        this.assessDesc = assessDesc;
    }
    /** ID */
    private Long id;
    /** 工号 */
    private String staffId;
    /** 员工类型：1-白领，2-蓝领, 3-编外白领, 4-编外蓝领 */
    private Integer staffType;
    /** 考核年度 */
    private String assessYear;
    /** 考核类型: Q1,Q2,Q3,YEAR */
    private String assessType;
    /** 考核Header */
    private String assessDesc;
    /** SF formDataId */
    private String formId;
    /** SF formContentId */
    private String formContentId;

    /** 业绩得分itemId */
    private String ratingScoreItemId;
    /** 业绩得分 */
    private BigDecimal ratingScore;
    /** 质量目标 */
    private BigDecimal workQualityScore;
    /** 成本控制 */
    private BigDecimal costControlScore;
    /** 工作任务完成 */
    private BigDecimal completionOfWorkScore;
    /** 安全生产 */
    private BigDecimal productionSafetyScore;
    /** 出勤 */
    private BigDecimal attendanceScore;
    /** 人际沟通 */
    private BigDecimal communicationScore;
    /** 团队协作 */
    private BigDecimal teamWorkScore;
    /** 专业技能 */
    private BigDecimal professionalScore;
    /** 敬业负责 */
    private BigDecimal responsibilityScore;
    /** 严谨规范 */
    private BigDecimal disciplineScore;

    /** 轻微违规违纪扣减说明 */
    private String regulationDeductedRemark;
    /** 最终总分, 对应字段：Q1_SCORE,Q2_SCORE,Q3_SCORE,TOTAL_SCORE */
    private BigDecimal finalResultScore;
    /** 业绩等级 */
    private BigDecimal performanceCategory;
    /** 业绩等级 */
    private String performanceCategoryDesc;
    /** 潜能等级 */
    private String potentialCategory;
    /** 可变动性 */
    private String availability;
    /** 年度评价-主要成就 */
    private String annualMajorAccomplishments;
    /** 时间计划(Year) */
    private String availableFromYear;
    /** 时间计划(Month) */
    private String availableFromMonth;
    /** 职级 */
    private String staffLevel;
    /** 评价人1 */
    private String evaluator1;
    /** 评价人2 */
    private String evaluator2;

}
