package com.bbac.hrsf.admin.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class HrsfCalibrationPmsVO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    @ApiModelProperty("员工ID")
    private String staffId;

    @ApiModelProperty("员工姓名")
    private String fullName;

    @ApiModelProperty("最终分数(带百分号)")
    private BigDecimal totalScore;

    @ApiModelProperty("最终等级")
    private BigDecimal pmsLevel;

}
