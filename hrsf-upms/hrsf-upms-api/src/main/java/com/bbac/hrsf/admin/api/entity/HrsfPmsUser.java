package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER")
@ApiModel(value = "HrsfPmsUser对象", description = "")
public class HrsfPmsUser extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("绩效协调员")
    @TableField("PMS_STAFF")
    @NotBlank(message = "绩效协调员 不能为空")
    private String pmsStaff;

    @ApiModelProperty("绩效协调员姓名")
    @TableField("PMS_STAFF_NAME")
    private String pmsStaffName;

    @ApiModelProperty("绩效协调员CODE")
    @TableField("PMS_STAFF_CODE")
    private String pmsStaffCode;

    @ApiModelProperty("描述")
    @TableField("DESCRIPTION")
    private String description;

    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;


}
