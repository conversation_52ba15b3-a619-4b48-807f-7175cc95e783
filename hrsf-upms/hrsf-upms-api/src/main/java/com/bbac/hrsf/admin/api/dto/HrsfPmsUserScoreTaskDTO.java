package com.bbac.hrsf.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 历史绩效分数任务计数 Vo
 * </p>
 *
 * <AUTHOR> Xueke
 * @since 2025-01-07
 */
@Data
public class HrsfPmsUserScoreTaskDTO {


    @ApiModelProperty("任务ID 主键")
    private Long id;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty("状态：1-执行中，2-成功，3-失败")
    private Integer status;

    @ApiModelProperty("任务明细的数量")
    private Integer processNumber;

}
