/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.api.feign;

import com.bbac.hrsf.admin.api.dto.UserInfo;
import com.bbac.hrsf.admin.api.dto.WriteDataBackDTO;
import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import com.bbac.hrsf.admin.api.entity.HrsfPmsuserBase;
import com.bbac.hrsf.admin.api.entity.HrsfUserBase;
import com.bbac.hrsf.admin.api.vo.HrsfUserVO;
import com.bbac.hrsf.common.core.constant.SecurityConstants;
import com.bbac.hrsf.common.core.constant.ServiceNameConstants;
import com.bbac.hrsf.common.core.util.R;
import com.bbac.hrsf.performance.api.dto.AddCalibrationUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/2/1
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.UMPS_SERVICE)
public interface RemoteUserService {

    /**
     * 通过用户名查询用户、角色信息
     *
     * @param username 用户名
     * @param from     调用标志
     * @return R
     */
    @GetMapping("/user/info/{username}")
    R<UserInfo> info(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 通过手机号码查询用户、角色信息
     *
     * @param phone 手机号码
     * @param from  调用标志
     * @return R
     */
    @GetMapping("/app/info/{phone}")
    R<UserInfo> infoByMobile(@PathVariable("phone") String phone, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 根据部门id，查询对应的用户 id 集合
     *
     * @param deptIds 部门id 集合
     * @param from    调用标志
     * @return 用户 id 集合
     */
    @GetMapping("/user/ids")
    R<List<Long>> listUserIdByDeptIds(@RequestParam("deptIds") Set<Long> deptIds,
                                      @RequestHeader(SecurityConstants.FROM) String from);


    /**
     * 根据部门id，查询对应的用户 id 集合
     * hrsfPmstart
     */
    @PostMapping("/user/getPmsUserList")
    R<List<HrsfPmsuserBase>> getPmsUserList(@RequestParam("assesYear") String assesYear, @RequestParam("assesType") String assesType,
                                            @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 根据部门id，查询对应的用户 id 集合
     * hrsfPmstart
     */
    @PostMapping("/user/getTotalNumByCalibrationBaseId")
    Long getTotalNumByCalibrationBaseId(@RequestParam("calibrationBaseId") Long calibrationBaseId,
                                        @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * 更新校准会议ID到员工基础表
     *
     * @param id
     * @param assesType
     * @param assesYear
     * @param staffIds
     * @param from
     * @return
     */
    @PostMapping("/user/updateCalibrationBaseId")
    R<Boolean> updateCalibrationBaseId(@RequestParam("id") Long id, @RequestParam("assesType") String assesType,
                                       @RequestParam("assesYear") String assesYear, @RequestBody Set<String> staffIds
            , @RequestHeader(SecurityConstants.FROM) String from);


    @PostMapping("/user/getUserInfoByStaffId")
    R<List<HrsfUserVO>> getUserInfoByStaffId(@RequestBody List<String> staffIdList, @RequestHeader(SecurityConstants.FROM) String from);

    @GetMapping("/user/selectByRoleCode")
    R<List<String>> selectByRoleCode(@RequestParam("roleCode") Long roleCode, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * @param staffIdList
     * @return
     */
    @PostMapping("/user/selectByStaffIdList")
    R<List<String>> selectByStaffIdList(@RequestBody Set<String> staffIdList, @RequestHeader(SecurityConstants.FROM) String from);

    /**
     * @param staffIdList
     * @return emailAddress
     */
    @PostMapping("/user/selectEmailAddressByStaffIdList")
    R<List<HrsfUserBase>> selectEmailAddressByStaffIdList(@RequestBody Set<String> staffIdList, @RequestHeader(SecurityConstants.FROM) String from);


    /**
     * @param organId
     * @return
     */
    @PostMapping("/user/selectDeptHeadList")
    R<List<String>> selectDeptHeadList(@RequestParam(value = "organId", required = false) String organId, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/selectErrorInfoByTaskIds")
    R<Boolean> selectErrorInfoByTaskIds(@RequestParam(value = "taskId", required = false) List<String> taskId, @RequestHeader(SecurityConstants.FROM) String from);


    @PostMapping("/user/selectByBaseId")
    R<List<HrsfPmsuserBase>> selectByBaseId(@RequestParam("baseId") Long baseId, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/selectCalibrationByBaseId")
    R<HrsfCalibrationBase> selectCalibrationByBaseId(@RequestParam("baseId") Long baseId, @RequestHeader(SecurityConstants.FROM) String from);


    @PostMapping("/user/writeDataBack")
    R<Boolean> writeDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/finalWriteDataBack")
    R<Boolean> finalWriteDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/white/year/WriteDataBack")
    R<Boolean> whiteYearWriteDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/white/year/FinalWriteDataBack")
    R<Boolean> whiteYearFinalWriteDataBack(@RequestBody List<WriteDataBackDTO> writeDataBackDTOS, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/selectByStaffIdAndASSESS")
    R<List<HrsfPmsuserBase>> selectByStaffIdAndASSESS(@RequestBody List<String> staffIds, @RequestParam("assesYear") String assesYear, @RequestParam("assesType") String assesType, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/saveCompanyLevelCalibration")
    R<Object> saveCompanyLevelCalibration(@RequestBody List<HrsfPmsuserBase> pmsUserList, @RequestHeader(SecurityConstants.FROM) String from);

    @PostMapping("/user/removeCalibrationBaseId")
    R<Object> removeCalibrationBaseId(@RequestParam("id") Long id, @RequestHeader(SecurityConstants.FROM) String fromIn);

    @PostMapping("/user/batchUpdateByCalibrationBaseId")
    R<Object> batchUpdateByCalibrationBaseId(@RequestParam("baseId") Long baseId, @RequestHeader(SecurityConstants.FROM) String fromIn);

    @PostMapping("/user/removeCalibrationUser")
    R<Object> removeCalibrationUser(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, @RequestHeader(SecurityConstants.FROM) String fromIn);

    @PostMapping("/user/selectPmsUserBaseByStaffIdList")
    R<List<HrsfPmsuserBase>> selectPmsUserBaseByStaffIdList(@RequestBody AddCalibrationUserDTO addCalibrationUserDTO, @RequestHeader(SecurityConstants.FROM) String fromIn);

    @GetMapping("/user/updateFormStatusByBaseId")
    void updateFormStatusByBaseId(@RequestParam(value = "formStatus", required = false) String formStatus, @RequestParam("baseId") Long baseId
            , @RequestParam(value = "processStep", required = false) String processStep, @RequestParam(value = "template", required = false) String template, @RequestHeader(SecurityConstants.FROM) String fromIn);
}
