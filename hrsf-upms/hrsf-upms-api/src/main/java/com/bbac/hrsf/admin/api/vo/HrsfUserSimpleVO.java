package com.bbac.hrsf.admin.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class HrsfUserSimpleVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("工号")
    private String staffId;

    @ApiModelProperty("职位分类")
    private String jobCode;

}
