package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-31
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_RELATION_INFO")
@ApiModel(value = "HrsfPmsUserRelationInfo对象", description = "")
@AllArgsConstructor
@NoArgsConstructor
public class HrsfPmsUserRelationInfo {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("绩效协调员ID")
    @TableField("PMS_USER_ID")
    private Long pmsUserId;

    @ApiModelProperty("协调员员工工号")
    @TableField("PMS_STAFF_ID")
    private String pmsStaffId;

    @ApiModelProperty("员工工号")
    @TableField("STAFF_ID")
    private String staffId;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        HrsfPmsUserRelationInfo that = (HrsfPmsUserRelationInfo) o;
        return Objects.equals(pmsUserId, that.pmsUserId) &&
                Objects.equals(pmsStaffId, that.pmsStaffId) &&
                Objects.equals(staffId, that.staffId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(pmsUserId, pmsStaffId, staffId);
    }
}
