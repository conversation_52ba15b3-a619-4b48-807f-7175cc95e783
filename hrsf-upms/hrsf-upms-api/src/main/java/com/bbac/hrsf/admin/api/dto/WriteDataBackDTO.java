package com.bbac.hrsf.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * @author: liu, jie
 * @create: 2022-06-07
 **/
@Data
public class WriteDataBackDTO {
    private String name;
    private String staffId;
    private String formId;
    private BigDecimal pmsLevel;
    private BigDecimal totalScore;
    private String taskId;
    private String assessYear;
    private String assessType;
    private BigDecimal target1Score;
    private BigDecimal target2Score;
    private BigDecimal target3Score;
    private BigDecimal target4Score;
    private BigDecimal target5Score;
    private BigDecimal target6Score;
    private BigDecimal target7Score;
    private BigDecimal target8Score;
    private BigDecimal target9Score;
    private BigDecimal target10Score;
    private String scoreSource;

    //从绩效考核启动表（HRSF_PMSTART）中获取结束日期Enddate
    private LocalDate endDate;
    private LocalDate startDate;
    private String emailStatus;

    private Long id;
    @ApiModelProperty("可变动性")
    private String variable;

    private String potentialLevel;

    @ApiModelProperty("职级")
    private String userLevel;

    private String responseBody;

    private String flag;

    /**
     * 新增字段执行步骤
     */
    private int step;
}
