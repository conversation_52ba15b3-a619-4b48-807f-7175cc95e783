package com.bbac.hrsf.admin.api.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liu, jie
 * @create: 2022-06-24
 **/
@Data
public class HrsfPmsUserBaseFilterDTO extends Page {

    @ApiModelProperty("考核年度")
    private String assesYear;

    @ApiModelProperty("考核类型")
    private String assesType;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("科室")
    private String section;

    @ApiModelProperty("工段/组")
    private String userGroup;

    @ApiModelProperty("职级")
    private String userLevel;

    @ApiModelProperty("用工形式")
    private String employmentType;

    @ApiModelProperty("员工ID")
    private List<String> staffIds;
}
