package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_RELATION")
@ApiModel(value = "HrsfPmsUserRelation对象", description = "")
@AllArgsConstructor
@NoArgsConstructor
public class HrsfPmsUserRelation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("绩效员工ID")
    @TableField("PMS_USER_ID")
    private Long pmsUserId;

    @ApiModelProperty("绩效协调员")
    @TableField("PMS_STAFF_ID")
    @NotBlank(message = "绩效协调员 不能为空")
    private String pmsStaffId;

    @ApiModelProperty("描述")
    @TableField("DESCRIPTION")
    private String description;

    @ApiModelProperty("所属系统")
    @TableField("SYSTEM")
    private String system;

    @ApiModelProperty("部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty("科室")
    @TableField("SECTION")
    private String section;

    @ApiModelProperty("工段/组")
    @TableField("USER_GROUP")
    private String userGroup;

    @ApiModelProperty("职级")
    @TableField("USER_LEVEL")
    private String userLevel;

    @ApiModelProperty("是否跨级直属")
    @TableField("DIRECTLY_UNDER")
    private String directlyUnder;

    @ApiModelProperty("是否助理")
    @TableField("ASSISTANT")
    private String assistant;


}
