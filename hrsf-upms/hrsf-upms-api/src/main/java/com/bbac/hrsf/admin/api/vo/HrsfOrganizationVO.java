package com.bbac.hrsf.admin.api.vo;

import com.bbac.hrsf.common.core.constant.CommonConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HrsfOrganizationVO implements Serializable {

    private static final long serialVersionUID = 8095713535862012495L;
    @ApiModelProperty("系统")
    private List<String> system;

    @ApiModelProperty("部门")
    private List<String> department;

    @ApiModelProperty("科室")
    private List<String> section;

    @ApiModelProperty("工段/组")
    private List<String> userGroup;

    @ApiModelProperty("职级")
    private List<String> userLevel;

    @ApiModelProperty("特殊角色")
    private List<String> role;


    public HrsfOrganizationVO(List<String> system, List<String> department, List<String> section, List<String> userGroup) {
        this.system = system;
        this.department = department;
        this.section = section;
        this.userGroup = userGroup;
    }

    public HrsfOrganizationVO(List<String> system, List<String> department, List<String> section, List<String> userGroup, List<String> userLevel) {
        this.system = system;
        this.system.add(CommonConstants.DEFAULT_NA);
        this.department = department;
        this.department.add(CommonConstants.DEFAULT_NA);
        this.section = section;
        this.section.add(CommonConstants.DEFAULT_NA);
        this.userGroup = userGroup;
        this.userGroup.add(CommonConstants.DEFAULT_NA);
        this.userLevel = userLevel;
    }
}
