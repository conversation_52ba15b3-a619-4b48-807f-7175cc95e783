package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人才卡信息 下载用的pdf文件，预先生成的
 *CREATE TABLE `HRSF_PMS_USER_TALENT_CARD_PDF` (
 *   `id` varchar(64) NOT NULL COMMENT '主键',
 *   `STAFF_ID` varchar(16) DEFAULT NULL COMMENT '员工工号',
 *   `PDF_NAME` varchar(16) DEFAULT NULL COMMENT 'PDF文件名称',
 *   `PDF_DATA` varchar(32) DEFAULT NULL COMMENT 'PDF文件内容',
 *   `DEL_FLAG` char(1) DEFAULT NULL COMMENT '逻辑删除标记',
 *   `create_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `create_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   `update_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `update_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工人才卡生成的PDF文件';
 */ 
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_TALENT_CARD_PDF")
@ApiModel(value = "HrsfPmsTalentCardPDF对象", description = "人才卡PDF导出的数据")
public class HrsfPmsTalentCardPDF extends BaseEntity {


    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "员工ID")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty(value = "文件名称")
    @TableField("PDF_NAME")
    private String pdfName;

    @ApiModelProperty(value = "人才卡下载功能要下载的数据")
    @TableField("PDF_DATA")//mysql: longblob  oracle:
    private byte[] pdfDATA;

    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;

}
