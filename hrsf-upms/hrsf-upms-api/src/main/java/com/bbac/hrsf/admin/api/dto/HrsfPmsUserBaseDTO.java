package com.bbac.hrsf.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liu, jie
 * @create: 2022-06-24
 **/
@Data
public class HrsfPmsUserBaseDTO {

    @ApiModelProperty("考核年度")
    private String assesYear;

    @ApiModelProperty("考核类型")
    private String assesType;

    @ApiModelProperty("所属系统")
    private List<String> system;

    @ApiModelProperty("部门")
    private List<String> department;

    @ApiModelProperty("科室")
    private List<String> section;

    @ApiModelProperty("工段/组")
    private List<String> userGroup;

    @ApiModelProperty("职级")
    private List<String> userLevel;

    @ApiModelProperty("用工形式")
    private String employmentType;

    @ApiModelProperty("员工ID")
    private List<String> staffIds;
}
