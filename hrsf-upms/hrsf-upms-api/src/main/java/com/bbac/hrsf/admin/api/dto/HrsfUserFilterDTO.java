package com.bbac.hrsf.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class HrsfUserFilterDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("员工姓名")
    private String fullName;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职级")
    private String userLevel;

    @ApiModelProperty("是否跨级直属")
    private String directlyUnder;

    @ApiModelProperty("是否助理")
    private String assistant;

}
