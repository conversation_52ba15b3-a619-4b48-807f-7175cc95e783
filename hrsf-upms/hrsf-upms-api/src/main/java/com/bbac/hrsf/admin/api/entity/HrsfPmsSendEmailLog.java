package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @author: liu, jie
 * @create: 2022-06-17
 **/
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_SEND_EMAIL_LOG")
@ApiModel(value = "HrsfPmsSendEmailLog对象", description = "")
public class HrsfPmsSendEmailLog extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("emailAddress")
    @TableField("EMAIL_ADDRESS")
    private String emailAddress;

    @ApiModelProperty("template")
    @TableField("TEMPLATE")
    private String template;

    @ApiModelProperty("subject")
    @TableField("SUBJECT")
    private String subject;

    @ApiModelProperty("modelJson")
    @TableField("MODEL_JSON")
    private String modelJson;

    @ApiModelProperty("status")
    @TableField("STATUS")
    private String status;
}
