package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author: liu, jie
 * @create: 2022-06-08
 **/
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_WRITE_DATA_BACK_LOG")
@ApiModel(value = "HrsfPmsWriteDataBackLog对象", description = "")
@NoArgsConstructor
@AllArgsConstructor
public class HrsfPmsWriteDataBackLog extends BaseEntity {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("校准会议名称")
    @TableField("NAME")
    private String name;

    @ApiModelProperty("表单ID")
    @TableField("FORM_ID")
    private String formId;

    @ApiModelProperty("错误json")
    @TableField("ERROR_INFO")
    private String errorInfo;

    @ApiModelProperty("taskId")
    @TableField("TASK_ID")
    private String taskId;

    @ApiModelProperty("sendEmail")
    @TableField("EMAIL_STATUS")
    private String emailStatus;

    @ApiModelProperty("员工工号")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty("考核年度")
    @TableField("ASSESS_YEAR")
    private String assessYear;

    @ApiModelProperty("考核类型")
    @TableField("ASSESS_TYPE")
    private String assessType;

    @ApiModelProperty("返回BODY")
    @TableField("RESPONSE_BODY")
    private String responseBody;

    @ApiModelProperty("请求接口")
    @TableField("FLAG")
    private String flag;
}
