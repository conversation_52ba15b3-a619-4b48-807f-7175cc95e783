package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * 人才卡信息-参与的重点项目
 *
 * create table HRSF_PMS_USER_TALENT_CARD_KEY_PROJECT(
 *   `id` varchar(64) NOT NULL COMMENT '主键',
 *   `STAFF_ID` varchar(16) DEFAULT NULL COMMENT '员工工号',
 *   `LEFT_FLAG` tinyint(1) DEFAULT NULL COMMENT '是否应该显示在左边 0否 1是',
 *   `PROJECT` varchar(255) DEFAULT NULL COMMENT '项目名称',
 * 	`PROJECT_EN` varchar(255) DEFAULT NULL COMMENT '项目英文名称',
 *   `PARTICIPATE_DATE` varchar(32) DEFAULT NULL COMMENT '参与项目日期',
 *
 *   `DEL_FLAG` char(1) DEFAULT NULL COMMENT '逻辑删除标记',
 *   `create_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `create_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   `update_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `update_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人才卡需求-参与的重点项目';
 *
 */ 
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_TALENT_CARD_KEY_PROJECT")
@ApiModel(value = "HrsfPmsTalentCardKeyProject人才卡-参与的重点项目", description = "人才卡-参与的重点项目")
public class HrsfPmsTalentCardKeyProject extends BaseEntity {


    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "员工ID")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty(value = "应该呈现在左边 1左右 0右边")
    @TableField("LEFT_FLAG")
    private Boolean leftFlag;

    @ApiModelProperty(value = "项目名称")
    @TableField("PROJECT")
    private String project;

    @ApiModelProperty(value = "项目英文名称")
    @TableField("PROJECT_EN")
    private String projectEN;

    @ApiModelProperty(value = "参与项目日期")
    @TableField("PARTICIPATE_DATE")
    private String participateDate;


    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;

}
