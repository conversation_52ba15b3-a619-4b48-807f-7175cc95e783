package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_ORGANIZATION")
@ApiModel(value = "HrsfOrganization对象", description = "")
public class HrsfOrganization extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private Long id;

    @ApiModelProperty("系统")
    @TableField("SYSTEM")
    private String system;

    @ApiModelProperty("部门")
    @TableField("DEPARTMENT")
    private String department;

    @ApiModelProperty("科室")
    @TableField("SECTION")
    private String section;

    @ApiModelProperty("工段/组")
    @TableField("USER_GROUP")
    private String userGroup;

    @ApiModelProperty("职级")
    @TableField("USER_LEVEL")
    private String userLevel;

    @ApiModelProperty("特殊角色")
    @TableField("ROLE")
    private String role;


}
