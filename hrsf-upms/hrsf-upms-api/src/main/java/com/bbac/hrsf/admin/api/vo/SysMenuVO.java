/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.api.vo;

import com.baomidou.mybatisplus.annotation.*;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * <p>
 * 菜单权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Data
public class SysMenuVO {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @TableId(value = "menu_id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "菜单id")
    private Long menuId;

    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @ApiModelProperty(value = "菜单名称")
    private String name;

    /**
     * 父菜单ID
     */
    @NotNull(message = "菜单父ID不能为空")
    @ApiModelProperty(value = "菜单父id")
    private Long parentId;

    /**
     * 图标
     */
    @ApiModelProperty(value = "菜单图标")
    private String icon;
    /**
     * 属性
     */
    @ApiModelProperty(value = "属性")
    private Map<String,Object> meta;

    /**
     * 前端URL
     */
    @ApiModelProperty(value = "前端路由标识路径")
    private String path;

    /**
     * 菜单类型 （0菜单 1按钮）
     */
    @ApiModelProperty(value = "菜单类型")
    private String type;


}
