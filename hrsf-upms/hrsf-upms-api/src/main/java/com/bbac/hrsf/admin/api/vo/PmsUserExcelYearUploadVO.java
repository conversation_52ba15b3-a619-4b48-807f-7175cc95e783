package com.bbac.hrsf.admin.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.WriteCellData;

import java.io.Serializable;

/**
 * 用户excel 对应的实体
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
@ColumnWidth(40)
public class PmsUserExcelYearUploadVO implements Serializable {

    private static final long serialVersionUID = -4146344474000220266L;
    /**
     * StaffID
     */
    @ExcelProperty("工号Staff ID")
    private String staffId;

    /**
     * 用户名
     */
    @ExcelProperty("姓名 Full Name")
    private String fullName;

    /**
     * 部门名称
     */
    @ExcelProperty("部门 Department")
    private String department;


    /**
     * 科室 Section
     */
    @ExcelProperty("科室 Section")
    private String section;

    /**
     * 工段/组 Group
     */
    @ExcelProperty("工段/组 Group")
    private String userGroup;

    /**
     * 1.工作任务完成 Completion of Work
     */
    @ExcelProperty("(20%)工作任务完成 Completion of Work")
    private Double target1Score;


    /**
     * 2.质量目标 Work Quality Safety
     */
    @ExcelProperty("(20%)质量目标 Work Quality Safety")
    private Double target2Score;


    /**
     * 3.安全生产 Production Safety
     */
    @ExcelProperty("(10%)安全生产 Production Safety")
    private Double target3Score;


    /**
     * 4.出勤 Attendance
     */
    @ExcelProperty("(10%)出勤 Attendance")
    private Double target4Score;


    /**
     * 5.成本控制 Cost Control
     */
    @ExcelProperty("(10%)成本控制 Cost Control")
    private Double target5Score;


    /**
     * 6.人际沟通 Communication
     */
    @ExcelProperty("(5%)人际沟通 Communication")
    private Double target6Score;

    /**
     * 7.团队协作 Team Work
     */
    @ExcelProperty("(5%)团队协作 Team Work")
    private Double target7Score;


    /**
     * 8.专业技能 Professional
     */
    @ExcelProperty("(10%)专业技能 Professional")
    private Double target8Score;


    /**
     * 9.敬业负责 Responsibility
     */
    @ExcelProperty("(5%)敬业负责 Responsibility")
    private Double target9Score;


    /**
     * 10.严谨规范 Discipline
     */
    @ExcelProperty("(5%)严谨规范 Discipline")
    private Double target10Score;


    /**
     * 业绩等级 Performance Rank
     */
    @ExcelProperty("业绩等级 Performance Category")
    private String pmsLevelDesc;

    /**
     * 加权总分 Weighted Rating
     */
    @ExcelProperty("加权总分 Weighted Rating")
    private CellData<String> weightRating;

    /**
     * 最终总分 Final Rating
     */
    @ExcelProperty("最终总分 Final Result")
    private CellData<String> totalScore;

    /**
     * 新入职员工 New Hire
     */
    @ExcelProperty("新入职员工 New Hire")
    private String newHire;
    /**
     * 新晋升员工 New Promotion
     */
    @ExcelProperty("新晋升员工 New Promotion")
    private String newPromotion;
    /**
     * 股东方加入BBAC人员 Shareholders Join BBAC
     */
    @ExcelProperty("股东方加入BBAC人员 Shareholders Join BBAC")
    private String shareholdersJoinBbac;
    /**
     * 出勤 Attendance Rate
     */
    @ExcelProperty("出勤 Attendance Rate")
    private String attendanceRate;

    /**
     * 产假 Maternity Leave
     */
    @ExcelProperty("产假 Maternity Leave")
    private String maternityLeave;
    /**
     * 旷工 Absence
     */
    @ExcelProperty("旷工 Absence")
    private String absence;

    /**
     * 惩处信息
     */
    @ExcelProperty("惩处信息 Punishment")
    private String punishInfo;

    /**
     * 季度均分+5% Average(Q1+Q2+Q3)+5%
     */
    @ExcelProperty("季度均分+5% Average(Q1+Q2+Q3)+5%")
    private String averageScore;

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getUserGroup() {
        return userGroup;
    }

    public void setUserGroup(String userGroup) {
        this.userGroup = userGroup;
    }

    public Double getTarget1Score() {
        return target1Score;
    }

    public void setTarget1Score(Double target1Score) {
        this.target1Score = target1Score;
    }


    public Double getTarget2Score() {
        return target2Score;
    }

    public void setTarget2Score(Double target2Score) {
        this.target2Score = target2Score;
    }


    public Double getTarget3Score() {
        return target3Score;
    }

    public void setTarget3Score(Double target3Score) {
        this.target3Score = target3Score;
    }


    public Double getTarget4Score() {
        return target4Score;
    }

    public void setTarget4Score(Double target4Score) {
        this.target4Score = target4Score;
    }


    public Double getTarget5Score() {
        return target5Score;
    }

    public void setTarget5Score(Double target5Score) {
        this.target5Score = target5Score;
    }


    public Double getTarget6Score() {
        return target6Score;
    }

    public void setTarget6Score(Double target6Score) {
        this.target6Score = target6Score;
    }


    public Double getTarget7Score() {
        return target7Score;
    }

    public void setTarget7Score(Double target7Score) {
        this.target7Score = target7Score;
    }


    public Double getTarget8Score() {
        return target8Score;
    }

    public void setTarget8Score(Double target8Score) {
        this.target8Score = target8Score;
    }


    public Double getTarget9Score() {
        return target9Score;
    }

    public void setTarget9Score(Double target9Score) {
        this.target9Score = target9Score;
    }


    public Double getTarget10Score() {
        return target10Score;
    }

    public void setTarget10Score(Double target10Score) {
        this.target10Score = target10Score;
    }


    public String getPmsLevelDesc() {
        return pmsLevelDesc;
    }

    public void setPmsLevelDesc(String pmsLevelDesc) {
        this.pmsLevelDesc = pmsLevelDesc;
    }

    public CellData<String> getWeightRating() {
        return weightRating;
    }

    public void setWeightRating(CellData<String> weightRating) {
        this.weightRating = weightRating;
    }

    public String getPunishInfo() {
        return punishInfo;
    }

    public void setPunishInfo(String punishInfo) {
        this.punishInfo = punishInfo;
    }

    public CellData<String> getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(CellData<String> totalScore) {
        this.totalScore = totalScore;
    }

    public String getNewHire() {
        return newHire;
    }

    public void setNewHire(String newHire) {
        this.newHire = newHire;
    }

    public String getNewPromotion() {
        return newPromotion;
    }

    public void setNewPromotion(String newPromotion) {
        this.newPromotion = newPromotion;
    }

    public String getShareholdersJoinBbac() {
        return shareholdersJoinBbac;
    }

    public void setShareholdersJoinBbac(String shareholdersJoinBbac) {
        this.shareholdersJoinBbac = shareholdersJoinBbac;
    }

    public String getAttendanceRate() {
        return attendanceRate;
    }

    public void setAttendanceRate(String attendanceRate) {
        this.attendanceRate = attendanceRate;
    }

    public String getMaternityLeave() {
        return maternityLeave;
    }

    public void setMaternityLeave(String maternityLeave) {
        this.maternityLeave = maternityLeave;
    }

    public String getAbsence() {
        return absence;
    }

    public void setAbsence(String absence) {
        this.absence = absence;
    }

    public String getAverageScore() {
        return averageScore;
    }

    public void setAverageScore(String averageScore) {
        this.averageScore = averageScore;
    }
}
