package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 *人才卡-本岗位本年度培训记录
 *create table HRSF_PMS_USER_TALENT_CARD_COURSE_RECORD(
 *   `id` varchar(64) NOT NULL COMMENT '主键',
 *   `STAFF_ID` varchar(16) DEFAULT NULL COMMENT '员工工号',
 *   `COURSE` varchar(255) DEFAULT NULL COMMENT '课程名称',
 * 	`COURSE_EN` varchar(255) DEFAULT NULL COMMENT '课程英文名称',
 *   `START_DATE` varchar(32) DEFAULT NULL COMMENT '课程开始日期',
 *   `END_DATE` varchar(32) DEFAULT NULL COMMENT '课程结束日期',
 *   `STATUS` varchar(32) DEFAULT NULL COMMENT '课程状态',
 *
 *   `DEL_FLAG` char(1) DEFAULT NULL COMMENT '逻辑删除标记',
 *   `create_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `create_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   `update_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `update_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='人才卡需求-课程记录';
 */ 
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_TALENT_CARD_COURSE_RECORD")
@ApiModel(value = "HrsfPmsTalentCardCourseRecord人才卡-本岗位本年度培训记录", description = "人才卡-本岗位本年度培训记录")
public class HrsfPmsTalentCardCourseRecord extends BaseEntity {


    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "员工ID")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty(value = "课程名称")
    @TableField("COURSE")
    private String course;

    @ApiModelProperty(value = "课程英文名称")
    @TableField("COURSE_EN")
    private String courseEN;

    @ApiModelProperty(value = "课程开始日期")
    @TableField("START_DATE")
    private String startDate;

    @ApiModelProperty(value = "课程结束日期")
    @TableField("END_DATE")
    private String endDate;

    @ApiModelProperty(value = "课程状态")
    @TableField("STATUS")
    private String status;

    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;

}
