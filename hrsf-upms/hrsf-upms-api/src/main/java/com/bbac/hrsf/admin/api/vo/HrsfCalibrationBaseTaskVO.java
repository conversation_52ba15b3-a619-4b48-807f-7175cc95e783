package com.bbac.hrsf.admin.api.vo;

import com.bbac.hrsf.admin.api.entity.HrsfCalibrationBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-17
 */
@Data
public class  HrsfCalibrationBaseTaskVO extends HrsfCalibrationBase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键")
    private String taskId;

    @ApiModelProperty("校准会议人数")
    private Long totalNum;


}
