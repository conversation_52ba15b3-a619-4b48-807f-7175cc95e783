/*
 * Copyright (c) 2020 bbac Authors. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.bbac.hrsf.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2019/2/1
 */
@Data
public class HrsfPmsUserUpdateDTO {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty("绩效协调员ID")
    private Long pmsUserId;

    @ApiModelProperty("绩效协调员")
    private String pmsStaff;

    @ApiModelProperty("绩效协调员描述")
    private String description;

    @ApiModelProperty("授权员工删选器")
    private List<HrsfUserFilterDTO> selectList;



}
