package com.bbac.hrsf.admin.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.data.CellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户excel 对应的实体
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
@ColumnWidth(40)
public class PmsUserExcelUploadVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * StaffID
     */
    @ExcelProperty("工号Staff ID")
    private String staffId;

    /**
     * 用户名
     */
    @ExcelProperty("姓名 Full Name")
    private String fullName;

    /**
     * 部门名称
     */
    @ExcelProperty("部门 Department")
    private String department;


    /**
     * 科室 Section
     */
    @ExcelProperty("科室 Section")
    private String section;

    /**
     * 工段/组 Group
     */
    @ExcelProperty("工段/组 Group")
    private String userGroup;

    /**
     * 1.质量目标 Work Quality
     */
    @ExcelProperty("(30%)质量目标 Work Quality")
    private Double target1Score;
    /**
     * 1.权重 Weight
     */
    @ExcelProperty("1.权重 Weight")
    @NumberFormat("#%")
    private Double weight1;
    /**
     * 2.成本控制 Cost Control
     */
    @ExcelProperty("(10%)成本控制 Cost Control")
    private Double target2Score;
    /**
     * 2.权重 Weight
     */
    @ExcelProperty("2.权重 Weight")
    @NumberFormat("#%")
    private Double weight2;
    /**
     * 3.工作任务完成 Completion of work
     */
    @ExcelProperty("(30%)工作任务完成 Completion of work")
    private Double target3Score;
    /**
     * 3.权重 Weight
     */
    @ExcelProperty("3.权重 Weight")
    @NumberFormat("#%")
    private Double weight3;
    /**
     * 4.安全生产 Production Safety
     */
    @ExcelProperty("(20%)安全生产 Production Safety")
    private Double target4Score;
    /**
     * 4.权重 Weight
     */
    @ExcelProperty("4.权重 Weight")
    @NumberFormat("#%")
    private Double weight4;
    /**
     * 5.出勤 Attendance
     */
    @ExcelProperty("(10%)出勤 Attendance")
    private Double target5Score;
    /**
     * 5.权重 Weight
     */
    @ExcelProperty("5.权重 Weight")
    @NumberFormat("#%")
    private Double weight5;
    /**
     * 业绩等级 Performance Rank
     */
    @ExcelProperty("业绩等级 Performance Category")
    private String pmsLevelDesc;

    /**
     * 加权总分 Weighted Rating
     */
    @ExcelProperty("加权总分 Weighted Rating")
    private CellData<String> weightRating;

    /**
     * 最终总分 Final Rating
     */
    @ExcelProperty("最终总分 Final Result")
    private CellData<String> totalScore;
    /**
     * 新入职员工 New Hire
     */
    @ExcelProperty("新入职员工 New Hire")
    private String newHire;
    /**
     * 新晋升员工 New Promotion
     */
    @ExcelProperty("新晋升员工 New Promotion")
    private String newPromotion;
    /**
     * 股东方加入BBAC人员 Shareholders Join BBAC
     */
    @ExcelProperty("股东方加入BBAC人员 Shareholders Join BBAC")
    private String shareholdersJoinBbac;
    /**
     * 出勤 Attendance Rate
     */
    @ExcelProperty("出勤 Attendance Rate")
    private String attendanceRate;

    /**
     * 产假 Maternity Leave
     */
    @ExcelProperty("产假 Maternity Leave")
    private String maternityLeave;
    /**
     * 旷工 Absence
     */
    @ExcelProperty("旷工 Absence")
    private String absence;

    public String getStaffId() {
        return staffId;
    }

    public void setStaffId(String staffId) {
        this.staffId = staffId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getUserGroup() {
        return userGroup;
    }

    public void setUserGroup(String userGroup) {
        this.userGroup = userGroup;
    }

    public Double getTarget1Score() {
        return target1Score;
    }

    public void setTarget1Score(Double target1Score) {
        this.target1Score = target1Score;
    }

    public Double getWeight1() {
        return weight1;
    }

    public void setWeight1(Double weight1) {
        this.weight1 = weight1;
    }

    public Double getTarget2Score() {
        return target2Score;
    }

    public void setTarget2Score(Double target2Score) {
        this.target2Score = target2Score;
    }

    public Double getWeight2() {
        return weight2;
    }

    public void setWeight2(Double weight2) {
        this.weight2 = weight2;
    }

    public Double getTarget3Score() {
        return target3Score;
    }

    public void setTarget3Score(Double target3Score) {
        this.target3Score = target3Score;
    }

    public Double getWeight3() {
        return weight3;
    }

    public void setWeight3(Double weight3) {
        this.weight3 = weight3;
    }

    public Double getTarget4Score() {
        return target4Score;
    }

    public void setTarget4Score(Double target4Score) {
        this.target4Score = target4Score;
    }

    public Double getWeight4() {
        return weight4;
    }

    public void setWeight4(Double weight4) {
        this.weight4 = weight4;
    }

    public Double getTarget5Score() {
        return target5Score;
    }

    public void setTarget5Score(Double target5Score) {
        this.target5Score = target5Score;
    }

    public Double getWeight5() {
        return weight5;
    }

    public void setWeight5(Double weight5) {
        this.weight5 = weight5;
    }

    public String getPmsLevelDesc() {
        return pmsLevelDesc;
    }

    public void setPmsLevelDesc(String pmsLevelDesc) {
        this.pmsLevelDesc = pmsLevelDesc;
    }

    public CellData<String> getWeightRating() {
        return weightRating;
    }

    public void setWeightRating(CellData<String> weightRating) {
        this.weightRating = weightRating;
    }

    public CellData<String> getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(CellData<String> totalScore) {
        this.totalScore = totalScore;
    }

    public String getNewHire() {
        return newHire;
    }

    public void setNewHire(String newHire) {
        this.newHire = newHire;
    }

    public String getNewPromotion() {
        return newPromotion;
    }

    public void setNewPromotion(String newPromotion) {
        this.newPromotion = newPromotion;
    }

    public String getShareholdersJoinBbac() {
        return shareholdersJoinBbac;
    }

    public void setShareholdersJoinBbac(String shareholdersJoinBbac) {
        this.shareholdersJoinBbac = shareholdersJoinBbac;
    }

    public String getAttendanceRate() {
        return attendanceRate;
    }

    public void setAttendanceRate(String attendanceRate) {
        this.attendanceRate = attendanceRate;
    }

    public String getMaternityLeave() {
        return maternityLeave;
    }

    public void setMaternityLeave(String maternityLeave) {
        this.maternityLeave = maternityLeave;
    }

    public String getAbsence() {
        return absence;
    }

    public void setAbsence(String absence) {
        this.absence = absence;
    }
}
