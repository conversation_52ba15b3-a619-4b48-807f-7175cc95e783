package com.bbac.hrsf.admin.api.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.data.WriteCellData;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户excel 对应的实体
 *
 * <AUTHOR>
 * @date 2021/8/4
 */
@Data
@ColumnWidth(40)
public class PmsUserExcelDownVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * StaffID
     */
    @ExcelProperty("工号Staff ID")
    private String staffId;

    /**
     * 用户名
     */
    @ExcelProperty("姓名 Full Name")
    private String fullName;

    /**
     * 部门名称
     */
    @ExcelProperty("部门 Department")
    private String department;


    /**
     * 科室 Section
     */
    @ExcelProperty("科室 Section")
    private String section;

    /**
     * 工段/组 Group
     */
    @ExcelProperty("工段/组 Group")
    private String userGroup;

    /**
     * 1.质量目标 Work Quality
     */
    @ExcelProperty("(30%)质量目标 Work Quality")
    private Double target1Score;
    /**
     * 2.成本控制 Cost Control
     */
    @ExcelProperty("(10%)成本控制 Cost Control")
    private Double target2Score;
    /**
     * 3.工作任务完成 Completion of work
     */
    @ExcelProperty("(30%)工作任务完成 Completion of work")
    private Double target3Score;
    /**
     * 4.安全生产 Production Safety
     */
    @ExcelProperty("(20%)安全生产 Production Safety")
    private Double target4Score;
    /**
     * 5.出勤 Attendance
     */
    @ExcelProperty("(10%)出勤 Attendance")
    private Double target5Score;
    /**
     * 业绩等级 Performance Rank
     */
    @ExcelProperty("业绩等级 Performance Category")
    private String pmsLevelDesc;

    /**
     * 加权总分 Weighted Rating
     */
    @ExcelProperty("加权总分 Weighted Rating")
    @NumberFormat("#.##%")
    private WriteCellData<String> weightRating;

    /**
     * 最终总分 Final Rating
     */
    @ExcelProperty("最终总分 Final Result")
    @NumberFormat("#.##%")
    private WriteCellData<String> finalRating;
    /**
     * 新入职员工 New Hire
     */
    @ExcelProperty("新入职员工 New Hire")
    private String newHire;
    /**
     * 新晋升员工 New Promotion
     */
    @ExcelProperty("新晋升员工 New Promotion")
    private String newPromotion;
    /**
     * 股东方加入BBAC人员 Shareholders Join BBAC
     */
    @ExcelProperty("股东方加入BBAC人员 Shareholders Join BBAC")
    private String shareholdersJoinBbac;
    /**
     * 出勤 Attendance Rate
     */
    @ExcelProperty("出勤 Attendance Rate")
    private String attendanceRate;

    /**
     * 产假 Maternity Leave
     */
    @ExcelProperty("产假 Maternity Leave")
    private String maternityLeave;
    /**
     * 旷工 Absence
     */
    @ExcelProperty("旷工 Absence")
    private String absence;

    /**
     * 惩处信息
     */
    @ExcelProperty("惩处信息 Punishment")
    private String punishInfo;
}
