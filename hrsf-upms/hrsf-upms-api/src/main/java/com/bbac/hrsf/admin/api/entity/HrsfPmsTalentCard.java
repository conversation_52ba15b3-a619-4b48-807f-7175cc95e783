package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 人才卡信息
 *
 */ 
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_TALENT_CARD")
@ApiModel(value = "HrsfPmsTalentCard对象", description = "人才卡PDF导出需要的部分数据")
public class HrsfPmsTalentCard extends BaseEntity {


    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.ASSIGN_ID)
    private String id;

    @ApiModelProperty(value = "员工ID")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty(value = "能力分数(雷达图下的那个总分)")
    @TableField("COMPETENCY_SCORE")
    private String competencyScore;

    @ApiModelProperty(value = "潜力&绩效(九宫格数据结果)")
    @TableField("REVIEW_RESULT")
    private String reviewResult;

    @ApiModelProperty(value = "评价中心测评结果-晋升类型")
    @TableField("PROMOTE_TYPE")
    private String promoteType;

    @ApiModelProperty(value = "数据同步时间")
    @TableField("FORM_DATA_ID")
    private String formDataId;

    @ApiModelProperty(value = "评价中心测评结果-评价结果")
    @TableField("EVALUATE_RESULT")
    private String evaluateResult;

    @ApiModelProperty(value = "评价中心测评结果-评价日期")
    @TableField("EVALUATE_DATE")
    private String evaluateDate;

    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;

    @ApiModelProperty(value = "评价中心测评结果-同步数据中包含的时间")
    @TableField("CREATION_DATE")
    private Date creationDate;
}
