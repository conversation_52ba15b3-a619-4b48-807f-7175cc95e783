package com.bbac.hrsf.admin.api.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bbac.hrsf.common.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Transient;

/**
 * mysql:
 * CREATE TABLE `hrsf_pms_user_competency` (
 *   `id` varchar(64) NOT NULL COMMENT '主键',
 *   `STAFF_ID` varchar(16) DEFAULT NULL COMMENT '员工工号',
 *   `JOB_CODE` varchar(16) DEFAULT NULL COMMENT '职位分类',
 *   `COMPETENCY` varchar(32) DEFAULT NULL COMMENT '能力ID',
 *   `JDM_ID` varchar(32) DEFAULT NULL COMMENT '表单能力ID',
 *   `COMPETENCY_CATEGORY` varchar(32) DEFAULT NULL COMMENT '能力分类',
 *   `COMPETENCY_NAME_CN` varchar(128) DEFAULT NULL COMMENT '能力中文名称',
 *   `COMPETENCY_NAME_EN` varchar(128) DEFAULT NULL COMMENT '能力英文名称',
 *   `COMPETENCY_WEIGHT` int(11) DEFAULT NULL COMMENT '能力排序',
 *   `SCORE_STANDARD` varchar(32) DEFAULT NULL COMMENT '能力标准分数',
 *   `SCORE` varchar(32) DEFAULT NULL COMMENT '能力评分',
 *   `DEL_FLAG` char(1) DEFAULT NULL COMMENT '逻辑删除标记',
 *   `create_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `create_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   `update_By` varchar(32) DEFAULT NULL COMMENT '创建者',
 *   `update_Time` datetime DEFAULT NULL COMMENT '创建时间',
 *   PRIMARY KEY (`id`)
 * ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='员工能力分数表';
 *
 *员工能力分数表
 *
 */ 
@Getter
@Setter
@Accessors(chain = true)
@TableName("HRSF_PMS_USER_COMPETENCY")
@ApiModel(value = "HrsfPmsUserCompetency对象", description = "员工能力分数表(包含人才卡功能需要的数据)")
public class HrsfPmsUserCompetency extends BaseEntity {


    @ApiModelProperty("主键")
    @TableId(value = "ID", type = IdType.INPUT)
    private String id;

    @ApiModelProperty(value = "员工ID")
    @TableField("STAFF_ID")
    private String staffId;

    @ApiModelProperty(value = "职位分类")
    @TableField("JOB_CODE")
    private String jobCode;

    @ApiModelProperty(value = "能力ID")
    @TableField("COMPETENCY")
    private String competency;

    @ApiModelProperty(value = "表单能力ID")
    @TableField("JDM_ID")
    private String jdmId;

    @ApiModelProperty(value = "能力分类")
    @TableField("COMPETENCY_CATEGORY")
    private String competencyCategory;

    @ApiModelProperty(value = "能力分类-英文")
    @TableField("COMPETENCY_CATEGORY_EN")
    private String competencyCategoryEn;

    //能力分类排序 模板数据填充使用
    @Transient
    private transient Integer competencyCategoryWeight;

    @ApiModelProperty(value = "能力中文名称")
    @TableField("COMPETENCY_NAME_CN")
    private String competencyNameCN;

    //非数据库字段 能力原始全称
    @Transient
    private transient String name;

    @ApiModelProperty(value = "能力英文名称")
    @TableField("COMPETENCY_NAME_EN")
    private String competencyNameEN;

    @ApiModelProperty(value = "能力排序")
    @TableField("COMPETENCY_WEIGHT")
    private Integer competencyWeight;

    @ApiModelProperty(value = "能力标准分数")
    @TableField("SCORE_STANDARD")
    private String scoreStandard;

    @ApiModelProperty(value = "能力评分")
    @TableField("SCORE")
    private String score;

    @ApiModelProperty(value = "最后修改时间")
    @TableField("LAST_MODIFIED_DATE")
    private String lastModifiedDate;

    @ApiModelProperty("0-正常，1-删除")
    @TableLogic
    @TableField(fill = FieldFill.INSERT, value = "DEL_FLAG")
    private String delFlag;

}
