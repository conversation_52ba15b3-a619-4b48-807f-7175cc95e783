package com.bbac.hrsf.admin.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScoreCollectVO implements Serializable {


    private static final long serialVersionUID = -2368697937642708091L;
    @ApiModelProperty("已评分")
    private String rationComplete;

    @ApiModelProperty("绩效均分")
    private BigDecimal averageRating;

    @ApiModelProperty("总人数")
    private Integer totalNum;

    @ApiModelProperty("绩效均分(固定)")
    private BigDecimal averageRatingFixed;

    @ApiModelProperty("总人数(固定)")
    private Integer totalNumFixed;


}
