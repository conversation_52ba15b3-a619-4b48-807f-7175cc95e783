package com.bbac.hrsf.admin.api.dto;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 历史绩效分数任务查询 DTO
 * </p>
 *
 * <AUTHOR> Xueke
 * @since 2025-01-12
 */
@Data
public class HrsfPmsUserScoreTaskQueryDTO extends Page {

    /** 任务ID */
    @ApiModelProperty("任务ID 主键")
    private Long taskId;

    @ApiModelProperty("开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    /** 状态：1-执行中，2-成功，3-失败 */
    @ApiModelProperty("状态：1-执行中，2-成功，3-失败")
    private Integer status;

    @ApiModelProperty("员工ID")
    private List<String> staffIdList;

}
