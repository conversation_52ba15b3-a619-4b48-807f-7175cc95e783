package com.bbac.hrsf.admin.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class HrsfUserBaseFilterDTO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("员工姓名")
    private String fullName;

    @ApiModelProperty("所属系统")
    private String system;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("科室")
    private String section;

    @ApiModelProperty("工段/组")
    private String userGroup;

    @ApiModelProperty("职级")
    private String userLevel;


}
