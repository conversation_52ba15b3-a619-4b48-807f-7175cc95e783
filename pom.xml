<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright (c) 2020 bbac Authors. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~     http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.bbac</groupId>
	<artifactId>hrsf</artifactId>
	<name>${project.artifactId}</name>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>pom</packaging>
	<url>https://www.bbac.com</url>

	<properties>
		<spring-boot.version>2.6.3</spring-boot.version>
		<spring-cloud.version>2021.0.1</spring-cloud.version>
		<spring-cloud-alibaba.version>2021.1</spring-cloud-alibaba.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
		<spring-boot-admin.version>2.6.2</spring-boot-admin.version>
		<hutool.version>5.7.19</hutool.version>
		<dynamic-ds.version>3.4.1</dynamic-ds.version>
		<captcha.version>2.2.1</captcha.version>
		<velocity.version>2.3</velocity.version>
		<velocity.tool.version>3.1</velocity.tool.version>
		<configuration.version>1.10</configuration.version>
		<nacos.version>2.0.3</nacos.version>
		<jasypt.version>2.1.0</jasypt.version>
		<swagger.fox.version>3.0.0</swagger.fox.version>
		<xxl-job.version>2.3.0</xxl-job.version>
		<docker.plugin.version>0.32.0</docker.plugin.version>
		<docker.host>http://*************:2375</docker.host>
		<docker.registry>*************</docker.registry>
		<docker.namespace>bbac</docker.namespace>
		<docker.username>username</docker.username>
		<docker.password>password</docker.password>
		<git.commit.plugin>4.9.9</git.commit.plugin>
		<spring.checkstyle.plugin>0.0.29</spring.checkstyle.plugin>
	</properties>

	<!-- 以下依赖 全局所有的模块都会引入  -->
	<dependencies>
		<!--bootstrap 启动器-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-bootstrap</artifactId>
		</dependency>
		<!--配置文件处理器-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<!--配置文件加解密-->
		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>${jasypt.version}</version>
		</dependency>
		<!--Lombok-->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>
		<!--测试依赖-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<modules>
		<module>hrsf-register</module>
		<module>hrsf-gateway</module>
		<module>hrsf-auth</module>
		<module>hrsf-upms</module>
		<module>hrsf-common</module>
		<module>hrsf-performance</module>
		<module>hrsf-monitor</module>
	</modules>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>31.1-jre</version>
			</dependency>
			<!--hrsf 公共版本定义-->
			<dependency>
				<groupId>com.bbac</groupId>
				<artifactId>hrsf-common-bom</artifactId>
				<version>${project.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- spring boot 依赖 -->
			<!-- 更新 spring boot 依赖中的 这一系列包的版本号为4.3.0 -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-java</artifactId>
				<version>4.3.0</version>
			</dependency>
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-api</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-chrome-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-chrome-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-edge-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-edge-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-firefox-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-firefox-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-ie-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-ie-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-java -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-java</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-opera-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-opera-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-remote-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-remote-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-safari-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-safari-driver</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-support -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>selenium-support</artifactId>
				<version>4.3.0</version>
			</dependency>
			<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/htmlunit-driver -->
			<dependency>
				<groupId>org.seleniumhq.selenium</groupId>
				<artifactId>htmlunit-driver</artifactId>
				<version>3.62.0</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- spring cloud 依赖 -->
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!-- spring cloud alibaba 依赖 -->
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>${spring-cloud-alibaba.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<finalName>${project.name}</finalName>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
			</resource>
		</resources>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-maven-plugin</artifactId>
					<version>${spring-boot.version}</version>
					<configuration>
						<finalName>${project.build.finalName}</finalName>
						<layers>
							<enabled>true</enabled>
						</layers>
					</configuration>
					<executions>
						<execution>
							<goals>
								<goal>repackage</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>io.fabric8</groupId>
					<artifactId>docker-maven-plugin</artifactId>
					<version>${docker.plugin.version}</version>
					<configuration>
						<!-- Docker Remote Api-->
						<dockerHost>${docker.host}</dockerHost>
						<!-- Docker 镜像私服-->
						<registry>${docker.registry}</registry>
						<!-- 认证信息-->
						<authConfig>
							<push>
								<username>${docker.username}</username>
								<password>${docker.password}</password>
							</push>
						</authConfig>
						<images>
							<image>
								<!-- 镜像名称： ************/library/hrsf-gateway:2.6.3-->
								<name>${docker.registry}/${docker.namespace}/${project.name}:${project.version}</name>
								<build>
									<dockerFile>${project.basedir}/Dockerfile</dockerFile>
								</build>
							</image>
						</images>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<!--打包jar 与git commit 关联插件-->
			<plugin>
				<groupId>io.github.git-commit-id</groupId>
				<artifactId>git-commit-id-maven-plugin</artifactId>
				<version>${git.commit.plugin}</version>
				<executions>
					<execution>
						<id>get-the-git-infos</id>
						<goals>
							<goal>revision</goal>
						</goals>
						<phase>initialize</phase>
					</execution>
				</executions>
				<configuration>
					<failOnNoGitDirectory>false</failOnNoGitDirectory>
					<generateGitPropertiesFile>true</generateGitPropertiesFile>
					<!--因为项目定制了jackson的日期时间序列化/反序列化格式，因此这里要进行配置,不然通过management.info.git.mode=full进行完整git信息监控时会存在问题-->
					<dateFormat>yyyy-MM-dd HH:mm:ss</dateFormat>
					<includeOnlyProperties>
						<includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
						<includeOnlyProperty>^git.commit.(id|message|time).*$</includeOnlyProperty>
					</includeOnlyProperties>
				</configuration>
			</plugin>
			<!--代码格式插件，默认使用spring 规则-->
			<plugin>
				<groupId>io.spring.javaformat</groupId>
				<artifactId>spring-javaformat-maven-plugin</artifactId>
				<version>${spring.checkstyle.plugin}</version>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>local</id>
			<properties>
				<!-- 环境标识，需要与配置文件的名称相对应 -->
				<profiles.active>local</profiles.active>
			</properties>
			<activation>
				<!-- 默认环境 -->
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
        <profile>
			<id>dev</id>
			<properties>
				<!-- 环境标识，需要与配置文件的名称相对应 -->
				<profiles.active>dev</profiles.active>
			</properties>
		</profile>
        <profile>
			<id>prod</id>
			<properties>
				<!-- 环境标识，需要与配置文件的名称相对应 -->
				<profiles.active>prod</profiles.active>
			</properties>
		</profile>
	</profiles>
</project>
